"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/parseley";
exports.ids = ["vendor-chunks/parseley"];
exports.modules = {

/***/ "(rsc)/./node_modules/parseley/lib/parseley.cjs":
/*!************************************************!*\
  !*** ./node_modules/parseley/lib/parseley.cjs ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar leac = __webpack_require__(/*! leac */ \"(rsc)/./node_modules/leac/lib/leac.cjs\");\nvar p = __webpack_require__(/*! peberminta */ \"(rsc)/./node_modules/peberminta/lib/core.cjs\");\n\nfunction _interopNamespace(e) {\n    if (e && e.__esModule) return e;\n    var n = Object.create(null);\n    if (e) {\n        Object.keys(e).forEach(function (k) {\n            if (k !== 'default') {\n                var d = Object.getOwnPropertyDescriptor(e, k);\n                Object.defineProperty(n, k, d.get ? d : {\n                    enumerable: true,\n                    get: function () { return e[k]; }\n                });\n            }\n        });\n    }\n    n[\"default\"] = e;\n    return Object.freeze(n);\n}\n\nvar p__namespace = /*#__PURE__*/_interopNamespace(p);\n\nvar ast = /*#__PURE__*/Object.freeze({\n    __proto__: null\n});\n\nconst ws = `(?:[ \\\\t\\\\r\\\\n\\\\f]*)`;\nconst nl = `(?:\\\\n|\\\\r\\\\n|\\\\r|\\\\f)`;\nconst nonascii = `[^\\\\x00-\\\\x7F]`;\nconst unicode = `(?:\\\\\\\\[0-9a-f]{1,6}(?:\\\\r\\\\n|[ \\\\n\\\\r\\\\t\\\\f])?)`;\nconst escape = `(?:\\\\\\\\[^\\\\n\\\\r\\\\f0-9a-f])`;\nconst nmstart = `(?:[_a-z]|${nonascii}|${unicode}|${escape})`;\nconst nmchar = `(?:[_a-z0-9-]|${nonascii}|${unicode}|${escape})`;\nconst name = `(?:${nmchar}+)`;\nconst ident = `(?:[-]?${nmstart}${nmchar}*)`;\nconst string1 = `'([^\\\\n\\\\r\\\\f\\\\\\\\']|\\\\\\\\${nl}|${nonascii}|${unicode}|${escape})*'`;\nconst string2 = `\"([^\\\\n\\\\r\\\\f\\\\\\\\\"]|\\\\\\\\${nl}|${nonascii}|${unicode}|${escape})*\"`;\nconst lexSelector = leac.createLexer([\n    { name: 'ws', regex: new RegExp(ws) },\n    { name: 'hash', regex: new RegExp(`#${name}`, 'i') },\n    { name: 'ident', regex: new RegExp(ident, 'i') },\n    { name: 'str1', regex: new RegExp(string1, 'i') },\n    { name: 'str2', regex: new RegExp(string2, 'i') },\n    { name: '*' },\n    { name: '.' },\n    { name: ',' },\n    { name: '[' },\n    { name: ']' },\n    { name: '=' },\n    { name: '>' },\n    { name: '|' },\n    { name: '+' },\n    { name: '~' },\n    { name: '^' },\n    { name: '$' },\n]);\nconst lexEscapedString = leac.createLexer([\n    { name: 'unicode', regex: new RegExp(unicode, 'i') },\n    { name: 'escape', regex: new RegExp(escape, 'i') },\n    { name: 'any', regex: new RegExp('[\\\\s\\\\S]', 'i') }\n]);\nfunction sumSpec([a0, a1, a2], [b0, b1, b2]) {\n    return [a0 + b0, a1 + b1, a2 + b2];\n}\nfunction sumAllSpec(ss) {\n    return ss.reduce(sumSpec, [0, 0, 0]);\n}\nconst unicodeEscapedSequence_ = p__namespace.token((t) => t.name === 'unicode' ? String.fromCodePoint(parseInt(t.text.slice(1), 16)) : undefined);\nconst escapedSequence_ = p__namespace.token((t) => t.name === 'escape' ? t.text.slice(1) : undefined);\nconst anyChar_ = p__namespace.token((t) => t.name === 'any' ? t.text : undefined);\nconst escapedString_ = p__namespace.map(p__namespace.many(p__namespace.or(unicodeEscapedSequence_, escapedSequence_, anyChar_)), (cs) => cs.join(''));\nfunction unescape(escapedString) {\n    const lexerResult = lexEscapedString(escapedString);\n    const result = escapedString_({ tokens: lexerResult.tokens, options: undefined }, 0);\n    return result.value;\n}\nfunction literal(name) {\n    return p__namespace.token((t) => t.name === name ? true : undefined);\n}\nconst whitespace_ = p__namespace.token((t) => t.name === 'ws' ? null : undefined);\nconst optionalWhitespace_ = p__namespace.option(whitespace_, null);\nfunction optionallySpaced(parser) {\n    return p__namespace.middle(optionalWhitespace_, parser, optionalWhitespace_);\n}\nconst identifier_ = p__namespace.token((t) => t.name === 'ident' ? unescape(t.text) : undefined);\nconst hashId_ = p__namespace.token((t) => t.name === 'hash' ? unescape(t.text.slice(1)) : undefined);\nconst string_ = p__namespace.token((t) => t.name.startsWith('str') ? unescape(t.text.slice(1, -1)) : undefined);\nconst namespace_ = p__namespace.left(p__namespace.option(identifier_, ''), literal('|'));\nconst qualifiedName_ = p__namespace.eitherOr(p__namespace.ab(namespace_, identifier_, (ns, name) => ({ name: name, namespace: ns })), p__namespace.map(identifier_, (name) => ({ name: name, namespace: null })));\nconst uniSelector_ = p__namespace.eitherOr(p__namespace.ab(namespace_, literal('*'), (ns) => ({ type: 'universal', namespace: ns, specificity: [0, 0, 0] })), p__namespace.map(literal('*'), () => ({ type: 'universal', namespace: null, specificity: [0, 0, 0] })));\nconst tagSelector_ = p__namespace.map(qualifiedName_, ({ name, namespace }) => ({\n    type: 'tag',\n    name: name,\n    namespace: namespace,\n    specificity: [0, 0, 1]\n}));\nconst classSelector_ = p__namespace.ab(literal('.'), identifier_, (fullstop, name) => ({\n    type: 'class',\n    name: name,\n    specificity: [0, 1, 0]\n}));\nconst idSelector_ = p__namespace.map(hashId_, (name) => ({\n    type: 'id',\n    name: name,\n    specificity: [1, 0, 0]\n}));\nconst attrModifier_ = p__namespace.token((t) => {\n    if (t.name === 'ident') {\n        if (t.text === 'i' || t.text === 'I') {\n            return 'i';\n        }\n        if (t.text === 's' || t.text === 'S') {\n            return 's';\n        }\n    }\n    return undefined;\n});\nconst attrValue_ = p__namespace.eitherOr(p__namespace.ab(string_, p__namespace.option(p__namespace.right(optionalWhitespace_, attrModifier_), null), (v, mod) => ({ value: v, modifier: mod })), p__namespace.ab(identifier_, p__namespace.option(p__namespace.right(whitespace_, attrModifier_), null), (v, mod) => ({ value: v, modifier: mod })));\nconst attrMatcher_ = p__namespace.choice(p__namespace.map(literal('='), () => '='), p__namespace.ab(literal('~'), literal('='), () => '~='), p__namespace.ab(literal('|'), literal('='), () => '|='), p__namespace.ab(literal('^'), literal('='), () => '^='), p__namespace.ab(literal('$'), literal('='), () => '$='), p__namespace.ab(literal('*'), literal('='), () => '*='));\nconst attrPresenceSelector_ = p__namespace.abc(literal('['), optionallySpaced(qualifiedName_), literal(']'), (lbr, { name, namespace }) => ({\n    type: 'attrPresence',\n    name: name,\n    namespace: namespace,\n    specificity: [0, 1, 0]\n}));\nconst attrValueSelector_ = p__namespace.middle(literal('['), p__namespace.abc(optionallySpaced(qualifiedName_), attrMatcher_, optionallySpaced(attrValue_), ({ name, namespace }, matcher, { value, modifier }) => ({\n    type: 'attrValue',\n    name: name,\n    namespace: namespace,\n    matcher: matcher,\n    value: value,\n    modifier: modifier,\n    specificity: [0, 1, 0]\n})), literal(']'));\nconst attrSelector_ = p__namespace.eitherOr(attrPresenceSelector_, attrValueSelector_);\nconst typeSelector_ = p__namespace.eitherOr(uniSelector_, tagSelector_);\nconst subclassSelector_ = p__namespace.choice(idSelector_, classSelector_, attrSelector_);\nconst compoundSelector_ = p__namespace.map(p__namespace.eitherOr(p__namespace.flatten(typeSelector_, p__namespace.many(subclassSelector_)), p__namespace.many1(subclassSelector_)), (ss) => {\n    return {\n        type: 'compound',\n        list: ss,\n        specificity: sumAllSpec(ss.map(s => s.specificity))\n    };\n});\nconst combinator_ = p__namespace.choice(p__namespace.map(literal('>'), () => '>'), p__namespace.map(literal('+'), () => '+'), p__namespace.map(literal('~'), () => '~'), p__namespace.ab(literal('|'), literal('|'), () => '||'));\nconst combinatorSeparator_ = p__namespace.eitherOr(optionallySpaced(combinator_), p__namespace.map(whitespace_, () => ' '));\nconst complexSelector_ = p__namespace.leftAssoc2(compoundSelector_, p__namespace.map(combinatorSeparator_, (c) => (left, right) => ({\n    type: 'compound',\n    list: [...right.list, { type: 'combinator', combinator: c, left: left, specificity: left.specificity }],\n    specificity: sumSpec(left.specificity, right.specificity)\n})), compoundSelector_);\nconst listSelector_ = p__namespace.leftAssoc2(p__namespace.map(complexSelector_, (s) => ({ type: 'list', list: [s] })), p__namespace.map(optionallySpaced(literal(',')), () => (acc, next) => ({ type: 'list', list: [...acc.list, next] })), complexSelector_);\nfunction parse_(parser, str) {\n    if (!(typeof str === 'string' || str instanceof String)) {\n        throw new Error('Expected a selector string. Actual input is not a string!');\n    }\n    const lexerResult = lexSelector(str);\n    if (!lexerResult.complete) {\n        throw new Error(`The input \"${str}\" was only partially tokenized, stopped at offset ${lexerResult.offset}!\\n` +\n            prettyPrintPosition(str, lexerResult.offset));\n    }\n    const result = optionallySpaced(parser)({ tokens: lexerResult.tokens, options: undefined }, 0);\n    if (!result.matched) {\n        throw new Error(`No match for \"${str}\" input!`);\n    }\n    if (result.position < lexerResult.tokens.length) {\n        const token = lexerResult.tokens[result.position];\n        throw new Error(`The input \"${str}\" was only partially parsed, stopped at offset ${token.offset}!\\n` +\n            prettyPrintPosition(str, token.offset, token.len));\n    }\n    return result.value;\n}\nfunction prettyPrintPosition(str, offset, len = 1) {\n    return `${str.replace(/(\\t)|(\\r)|(\\n)/g, (m, t, r) => t ? '\\u2409' : r ? '\\u240d' : '\\u240a')}\\n${''.padEnd(offset)}${'^'.repeat(len)}`;\n}\nfunction parse(str) {\n    return parse_(listSelector_, str);\n}\nfunction parse1(str) {\n    return parse_(complexSelector_, str);\n}\n\nfunction serialize(selector) {\n    if (!selector.type) {\n        throw new Error('This is not an AST node.');\n    }\n    switch (selector.type) {\n        case 'universal':\n            return _serNs(selector.namespace) + '*';\n        case 'tag':\n            return _serNs(selector.namespace) + _serIdent(selector.name);\n        case 'class':\n            return '.' + _serIdent(selector.name);\n        case 'id':\n            return '#' + _serIdent(selector.name);\n        case 'attrPresence':\n            return `[${_serNs(selector.namespace)}${_serIdent(selector.name)}]`;\n        case 'attrValue':\n            return `[${_serNs(selector.namespace)}${_serIdent(selector.name)}${selector.matcher}\"${_serStr(selector.value)}\"${(selector.modifier ? selector.modifier : '')}]`;\n        case 'combinator':\n            return serialize(selector.left) + selector.combinator;\n        case 'compound':\n            return selector.list.reduce((acc, node) => {\n                if (node.type === 'combinator') {\n                    return serialize(node) + acc;\n                }\n                else {\n                    return acc + serialize(node);\n                }\n            }, '');\n        case 'list':\n            return selector.list.map(serialize).join(',');\n    }\n}\nfunction _serNs(ns) {\n    return (ns || ns === '')\n        ? _serIdent(ns) + '|'\n        : '';\n}\nfunction _codePoint(char) {\n    return `\\\\${char.codePointAt(0).toString(16)} `;\n}\nfunction _serIdent(str) {\n    return str.replace(\n    /(^[0-9])|(^-[0-9])|(^-$)|([-0-9a-zA-Z_]|[^\\x00-\\x7F])|(\\x00)|([\\x01-\\x1f]|\\x7f)|([\\s\\S])/g, (m, d1, d2, hy, safe, nl, ctrl, other) => d1 ? _codePoint(d1) :\n        d2 ? '-' + _codePoint(d2.slice(1)) :\n            hy ? '\\\\-' :\n                safe ? safe :\n                    nl ? '\\ufffd' :\n                        ctrl ? _codePoint(ctrl) :\n                            '\\\\' + other);\n}\nfunction _serStr(str) {\n    return str.replace(\n    /(\")|(\\\\)|(\\x00)|([\\x01-\\x1f]|\\x7f)/g, (m, dq, bs, nl, ctrl) => dq ? '\\\\\"' :\n        bs ? '\\\\\\\\' :\n            nl ? '\\ufffd' :\n                _codePoint(ctrl));\n}\nfunction normalize(selector) {\n    if (!selector.type) {\n        throw new Error('This is not an AST node.');\n    }\n    switch (selector.type) {\n        case 'compound': {\n            selector.list.forEach(normalize);\n            selector.list.sort((a, b) => _compareArrays(_getSelectorPriority(a), _getSelectorPriority(b)));\n            break;\n        }\n        case 'combinator': {\n            normalize(selector.left);\n            break;\n        }\n        case 'list': {\n            selector.list.forEach(normalize);\n            selector.list.sort((a, b) => (serialize(a) < serialize(b)) ? -1 : 1);\n            break;\n        }\n    }\n    return selector;\n}\nfunction _getSelectorPriority(selector) {\n    switch (selector.type) {\n        case 'universal':\n            return [1];\n        case 'tag':\n            return [1];\n        case 'id':\n            return [2];\n        case 'class':\n            return [3, selector.name];\n        case 'attrPresence':\n            return [4, serialize(selector)];\n        case 'attrValue':\n            return [5, serialize(selector)];\n        case 'combinator':\n            return [15, serialize(selector)];\n    }\n}\nfunction compareSelectors(a, b) {\n    return _compareArrays(a.specificity, b.specificity);\n}\nfunction compareSpecificity(a, b) {\n    return _compareArrays(a, b);\n}\nfunction _compareArrays(a, b) {\n    if (!Array.isArray(a) || !Array.isArray(b)) {\n        throw new Error('Arguments must be arrays.');\n    }\n    const shorter = (a.length < b.length) ? a.length : b.length;\n    for (let i = 0; i < shorter; i++) {\n        if (a[i] === b[i]) {\n            continue;\n        }\n        return (a[i] < b[i]) ? -1 : 1;\n    }\n    return a.length - b.length;\n}\n\nexports.Ast = ast;\nexports.compareSelectors = compareSelectors;\nexports.compareSpecificity = compareSpecificity;\nexports.normalize = normalize;\nexports.parse = parse;\nexports.parse1 = parse1;\nexports.serialize = serialize;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcGFyc2VsZXkvbGliL3BhcnNlbGV5LmNqcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7O0FBRTdELFdBQVcsbUJBQU8sQ0FBQyxvREFBTTtBQUN6QixRQUFRLG1CQUFPLENBQUMsZ0VBQVk7O0FBRTVCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVDQUF1QztBQUN2QyxpQkFBaUI7QUFDakI7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBLENBQUM7O0FBRUQ7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDLElBQUk7QUFDckM7QUFDQSw2QkFBNkIsU0FBUyxHQUFHLFFBQVEsR0FBRyxPQUFPO0FBQzNELGdDQUFnQyxTQUFTLEdBQUcsUUFBUSxHQUFHLE9BQU87QUFDOUQsbUJBQW1CLE9BQU87QUFDMUIsd0JBQXdCLFFBQVEsRUFBRSxPQUFPO0FBQ3pDLDJDQUEyQyxHQUFHLEdBQUcsU0FBUyxHQUFHLFFBQVEsR0FBRyxPQUFPO0FBQy9FLDJDQUEyQyxHQUFHLEdBQUcsU0FBUyxHQUFHLFFBQVEsR0FBRyxPQUFPO0FBQy9FO0FBQ0EsTUFBTSxtQ0FBbUM7QUFDekMsTUFBTSxvQ0FBb0MsS0FBSyxTQUFTO0FBQ3hELE1BQU0sOENBQThDO0FBQ3BELE1BQU0sK0NBQStDO0FBQ3JELE1BQU0sK0NBQStDO0FBQ3JELE1BQU0sV0FBVztBQUNqQixNQUFNLFdBQVc7QUFDakIsTUFBTSxXQUFXO0FBQ2pCLE1BQU0sV0FBVztBQUNqQixNQUFNLFdBQVc7QUFDakIsTUFBTSxXQUFXO0FBQ2pCLE1BQU0sV0FBVztBQUNqQixNQUFNLFdBQVc7QUFDakIsTUFBTSxXQUFXO0FBQ2pCLE1BQU0sV0FBVztBQUNqQixNQUFNLFdBQVc7QUFDakIsTUFBTSxXQUFXO0FBQ2pCO0FBQ0E7QUFDQSxNQUFNLGtEQUFrRDtBQUN4RCxNQUFNLGdEQUFnRDtBQUN0RCxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQ0FBb0MsZ0RBQWdEO0FBQ3BGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1R0FBdUcsMkJBQTJCLCtDQUErQyw2QkFBNkI7QUFDOU0sZ0dBQWdHLDBEQUEwRCw0Q0FBNEMsNERBQTREO0FBQ2xRLHlEQUF5RCxpQkFBaUI7QUFDMUU7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Qsb0tBQW9LLHlCQUF5QiwySEFBMkgseUJBQXlCO0FBQ2pWO0FBQ0EscUhBQXFILGlCQUFpQjtBQUN0STtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRCwrSkFBK0osaUJBQWlCLGFBQWEsaUJBQWlCO0FBQzlNO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0Qiw4RUFBOEU7QUFDMUc7QUFDQSxDQUFDO0FBQ0QsMkZBQTJGLHlCQUF5Qiw2RUFBNkUseUNBQXlDO0FBQzFPO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNDQUFzQyxJQUFJLG9EQUFvRCxtQkFBbUI7QUFDakg7QUFDQTtBQUNBLDhDQUE4QyxnREFBZ0Q7QUFDOUY7QUFDQSx5Q0FBeUMsSUFBSTtBQUM3QztBQUNBO0FBQ0E7QUFDQSxzQ0FBc0MsSUFBSSxpREFBaUQsYUFBYTtBQUN4RztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYyxvRkFBb0YsSUFBSSxrQkFBa0IsRUFBRSxnQkFBZ0I7QUFDMUk7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QiwyQkFBMkIsRUFBRSx5QkFBeUI7QUFDN0U7QUFDQSx1QkFBdUIsMkJBQTJCLEVBQUUseUJBQXlCLEVBQUUsaUJBQWlCLEdBQUcsd0JBQXdCLEdBQUcsNkNBQTZDO0FBQzNLO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLGtDQUFrQztBQUNsRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsYUFBYTtBQUNqQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxXQUFXO0FBQ1gsd0JBQXdCO0FBQ3hCLDBCQUEwQjtBQUMxQixpQkFBaUI7QUFDakIsYUFBYTtBQUNiLGNBQWM7QUFDZCxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxDb2RlXFxvbmUtbWFpbFxcbm9kZV9tb2R1bGVzXFxwYXJzZWxleVxcbGliXFxwYXJzZWxleS5janMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgJ19fZXNNb2R1bGUnLCB7IHZhbHVlOiB0cnVlIH0pO1xuXG52YXIgbGVhYyA9IHJlcXVpcmUoJ2xlYWMnKTtcbnZhciBwID0gcmVxdWlyZSgncGViZXJtaW50YScpO1xuXG5mdW5jdGlvbiBfaW50ZXJvcE5hbWVzcGFjZShlKSB7XG4gICAgaWYgKGUgJiYgZS5fX2VzTW9kdWxlKSByZXR1cm4gZTtcbiAgICB2YXIgbiA9IE9iamVjdC5jcmVhdGUobnVsbCk7XG4gICAgaWYgKGUpIHtcbiAgICAgICAgT2JqZWN0LmtleXMoZSkuZm9yRWFjaChmdW5jdGlvbiAoaykge1xuICAgICAgICAgICAgaWYgKGsgIT09ICdkZWZhdWx0Jykge1xuICAgICAgICAgICAgICAgIHZhciBkID0gT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcihlLCBrKTtcbiAgICAgICAgICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkobiwgaywgZC5nZXQgPyBkIDoge1xuICAgICAgICAgICAgICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgICAgICAgICBnZXQ6IGZ1bmN0aW9uICgpIHsgcmV0dXJuIGVba107IH1cbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgfVxuICAgIG5bXCJkZWZhdWx0XCJdID0gZTtcbiAgICByZXR1cm4gT2JqZWN0LmZyZWV6ZShuKTtcbn1cblxudmFyIHBfX25hbWVzcGFjZSA9IC8qI19fUFVSRV9fKi9faW50ZXJvcE5hbWVzcGFjZShwKTtcblxudmFyIGFzdCA9IC8qI19fUFVSRV9fKi9PYmplY3QuZnJlZXplKHtcbiAgICBfX3Byb3RvX186IG51bGxcbn0pO1xuXG5jb25zdCB3cyA9IGAoPzpbIFxcXFx0XFxcXHJcXFxcblxcXFxmXSopYDtcbmNvbnN0IG5sID0gYCg/OlxcXFxufFxcXFxyXFxcXG58XFxcXHJ8XFxcXGYpYDtcbmNvbnN0IG5vbmFzY2lpID0gYFteXFxcXHgwMC1cXFxceDdGXWA7XG5jb25zdCB1bmljb2RlID0gYCg/OlxcXFxcXFxcWzAtOWEtZl17MSw2fSg/OlxcXFxyXFxcXG58WyBcXFxcblxcXFxyXFxcXHRcXFxcZl0pPylgO1xuY29uc3QgZXNjYXBlID0gYCg/OlxcXFxcXFxcW15cXFxcblxcXFxyXFxcXGYwLTlhLWZdKWA7XG5jb25zdCBubXN0YXJ0ID0gYCg/OltfYS16XXwke25vbmFzY2lpfXwke3VuaWNvZGV9fCR7ZXNjYXBlfSlgO1xuY29uc3Qgbm1jaGFyID0gYCg/OltfYS16MC05LV18JHtub25hc2NpaX18JHt1bmljb2RlfXwke2VzY2FwZX0pYDtcbmNvbnN0IG5hbWUgPSBgKD86JHtubWNoYXJ9KylgO1xuY29uc3QgaWRlbnQgPSBgKD86Wy1dPyR7bm1zdGFydH0ke25tY2hhcn0qKWA7XG5jb25zdCBzdHJpbmcxID0gYCcoW15cXFxcblxcXFxyXFxcXGZcXFxcXFxcXCddfFxcXFxcXFxcJHtubH18JHtub25hc2NpaX18JHt1bmljb2RlfXwke2VzY2FwZX0pKidgO1xuY29uc3Qgc3RyaW5nMiA9IGBcIihbXlxcXFxuXFxcXHJcXFxcZlxcXFxcXFxcXCJdfFxcXFxcXFxcJHtubH18JHtub25hc2NpaX18JHt1bmljb2RlfXwke2VzY2FwZX0pKlwiYDtcbmNvbnN0IGxleFNlbGVjdG9yID0gbGVhYy5jcmVhdGVMZXhlcihbXG4gICAgeyBuYW1lOiAnd3MnLCByZWdleDogbmV3IFJlZ0V4cCh3cykgfSxcbiAgICB7IG5hbWU6ICdoYXNoJywgcmVnZXg6IG5ldyBSZWdFeHAoYCMke25hbWV9YCwgJ2knKSB9LFxuICAgIHsgbmFtZTogJ2lkZW50JywgcmVnZXg6IG5ldyBSZWdFeHAoaWRlbnQsICdpJykgfSxcbiAgICB7IG5hbWU6ICdzdHIxJywgcmVnZXg6IG5ldyBSZWdFeHAoc3RyaW5nMSwgJ2knKSB9LFxuICAgIHsgbmFtZTogJ3N0cjInLCByZWdleDogbmV3IFJlZ0V4cChzdHJpbmcyLCAnaScpIH0sXG4gICAgeyBuYW1lOiAnKicgfSxcbiAgICB7IG5hbWU6ICcuJyB9LFxuICAgIHsgbmFtZTogJywnIH0sXG4gICAgeyBuYW1lOiAnWycgfSxcbiAgICB7IG5hbWU6ICddJyB9LFxuICAgIHsgbmFtZTogJz0nIH0sXG4gICAgeyBuYW1lOiAnPicgfSxcbiAgICB7IG5hbWU6ICd8JyB9LFxuICAgIHsgbmFtZTogJysnIH0sXG4gICAgeyBuYW1lOiAnficgfSxcbiAgICB7IG5hbWU6ICdeJyB9LFxuICAgIHsgbmFtZTogJyQnIH0sXG5dKTtcbmNvbnN0IGxleEVzY2FwZWRTdHJpbmcgPSBsZWFjLmNyZWF0ZUxleGVyKFtcbiAgICB7IG5hbWU6ICd1bmljb2RlJywgcmVnZXg6IG5ldyBSZWdFeHAodW5pY29kZSwgJ2knKSB9LFxuICAgIHsgbmFtZTogJ2VzY2FwZScsIHJlZ2V4OiBuZXcgUmVnRXhwKGVzY2FwZSwgJ2knKSB9LFxuICAgIHsgbmFtZTogJ2FueScsIHJlZ2V4OiBuZXcgUmVnRXhwKCdbXFxcXHNcXFxcU10nLCAnaScpIH1cbl0pO1xuZnVuY3Rpb24gc3VtU3BlYyhbYTAsIGExLCBhMl0sIFtiMCwgYjEsIGIyXSkge1xuICAgIHJldHVybiBbYTAgKyBiMCwgYTEgKyBiMSwgYTIgKyBiMl07XG59XG5mdW5jdGlvbiBzdW1BbGxTcGVjKHNzKSB7XG4gICAgcmV0dXJuIHNzLnJlZHVjZShzdW1TcGVjLCBbMCwgMCwgMF0pO1xufVxuY29uc3QgdW5pY29kZUVzY2FwZWRTZXF1ZW5jZV8gPSBwX19uYW1lc3BhY2UudG9rZW4oKHQpID0+IHQubmFtZSA9PT0gJ3VuaWNvZGUnID8gU3RyaW5nLmZyb21Db2RlUG9pbnQocGFyc2VJbnQodC50ZXh0LnNsaWNlKDEpLCAxNikpIDogdW5kZWZpbmVkKTtcbmNvbnN0IGVzY2FwZWRTZXF1ZW5jZV8gPSBwX19uYW1lc3BhY2UudG9rZW4oKHQpID0+IHQubmFtZSA9PT0gJ2VzY2FwZScgPyB0LnRleHQuc2xpY2UoMSkgOiB1bmRlZmluZWQpO1xuY29uc3QgYW55Q2hhcl8gPSBwX19uYW1lc3BhY2UudG9rZW4oKHQpID0+IHQubmFtZSA9PT0gJ2FueScgPyB0LnRleHQgOiB1bmRlZmluZWQpO1xuY29uc3QgZXNjYXBlZFN0cmluZ18gPSBwX19uYW1lc3BhY2UubWFwKHBfX25hbWVzcGFjZS5tYW55KHBfX25hbWVzcGFjZS5vcih1bmljb2RlRXNjYXBlZFNlcXVlbmNlXywgZXNjYXBlZFNlcXVlbmNlXywgYW55Q2hhcl8pKSwgKGNzKSA9PiBjcy5qb2luKCcnKSk7XG5mdW5jdGlvbiB1bmVzY2FwZShlc2NhcGVkU3RyaW5nKSB7XG4gICAgY29uc3QgbGV4ZXJSZXN1bHQgPSBsZXhFc2NhcGVkU3RyaW5nKGVzY2FwZWRTdHJpbmcpO1xuICAgIGNvbnN0IHJlc3VsdCA9IGVzY2FwZWRTdHJpbmdfKHsgdG9rZW5zOiBsZXhlclJlc3VsdC50b2tlbnMsIG9wdGlvbnM6IHVuZGVmaW5lZCB9LCAwKTtcbiAgICByZXR1cm4gcmVzdWx0LnZhbHVlO1xufVxuZnVuY3Rpb24gbGl0ZXJhbChuYW1lKSB7XG4gICAgcmV0dXJuIHBfX25hbWVzcGFjZS50b2tlbigodCkgPT4gdC5uYW1lID09PSBuYW1lID8gdHJ1ZSA6IHVuZGVmaW5lZCk7XG59XG5jb25zdCB3aGl0ZXNwYWNlXyA9IHBfX25hbWVzcGFjZS50b2tlbigodCkgPT4gdC5uYW1lID09PSAnd3MnID8gbnVsbCA6IHVuZGVmaW5lZCk7XG5jb25zdCBvcHRpb25hbFdoaXRlc3BhY2VfID0gcF9fbmFtZXNwYWNlLm9wdGlvbih3aGl0ZXNwYWNlXywgbnVsbCk7XG5mdW5jdGlvbiBvcHRpb25hbGx5U3BhY2VkKHBhcnNlcikge1xuICAgIHJldHVybiBwX19uYW1lc3BhY2UubWlkZGxlKG9wdGlvbmFsV2hpdGVzcGFjZV8sIHBhcnNlciwgb3B0aW9uYWxXaGl0ZXNwYWNlXyk7XG59XG5jb25zdCBpZGVudGlmaWVyXyA9IHBfX25hbWVzcGFjZS50b2tlbigodCkgPT4gdC5uYW1lID09PSAnaWRlbnQnID8gdW5lc2NhcGUodC50ZXh0KSA6IHVuZGVmaW5lZCk7XG5jb25zdCBoYXNoSWRfID0gcF9fbmFtZXNwYWNlLnRva2VuKCh0KSA9PiB0Lm5hbWUgPT09ICdoYXNoJyA/IHVuZXNjYXBlKHQudGV4dC5zbGljZSgxKSkgOiB1bmRlZmluZWQpO1xuY29uc3Qgc3RyaW5nXyA9IHBfX25hbWVzcGFjZS50b2tlbigodCkgPT4gdC5uYW1lLnN0YXJ0c1dpdGgoJ3N0cicpID8gdW5lc2NhcGUodC50ZXh0LnNsaWNlKDEsIC0xKSkgOiB1bmRlZmluZWQpO1xuY29uc3QgbmFtZXNwYWNlXyA9IHBfX25hbWVzcGFjZS5sZWZ0KHBfX25hbWVzcGFjZS5vcHRpb24oaWRlbnRpZmllcl8sICcnKSwgbGl0ZXJhbCgnfCcpKTtcbmNvbnN0IHF1YWxpZmllZE5hbWVfID0gcF9fbmFtZXNwYWNlLmVpdGhlck9yKHBfX25hbWVzcGFjZS5hYihuYW1lc3BhY2VfLCBpZGVudGlmaWVyXywgKG5zLCBuYW1lKSA9PiAoeyBuYW1lOiBuYW1lLCBuYW1lc3BhY2U6IG5zIH0pKSwgcF9fbmFtZXNwYWNlLm1hcChpZGVudGlmaWVyXywgKG5hbWUpID0+ICh7IG5hbWU6IG5hbWUsIG5hbWVzcGFjZTogbnVsbCB9KSkpO1xuY29uc3QgdW5pU2VsZWN0b3JfID0gcF9fbmFtZXNwYWNlLmVpdGhlck9yKHBfX25hbWVzcGFjZS5hYihuYW1lc3BhY2VfLCBsaXRlcmFsKCcqJyksIChucykgPT4gKHsgdHlwZTogJ3VuaXZlcnNhbCcsIG5hbWVzcGFjZTogbnMsIHNwZWNpZmljaXR5OiBbMCwgMCwgMF0gfSkpLCBwX19uYW1lc3BhY2UubWFwKGxpdGVyYWwoJyonKSwgKCkgPT4gKHsgdHlwZTogJ3VuaXZlcnNhbCcsIG5hbWVzcGFjZTogbnVsbCwgc3BlY2lmaWNpdHk6IFswLCAwLCAwXSB9KSkpO1xuY29uc3QgdGFnU2VsZWN0b3JfID0gcF9fbmFtZXNwYWNlLm1hcChxdWFsaWZpZWROYW1lXywgKHsgbmFtZSwgbmFtZXNwYWNlIH0pID0+ICh7XG4gICAgdHlwZTogJ3RhZycsXG4gICAgbmFtZTogbmFtZSxcbiAgICBuYW1lc3BhY2U6IG5hbWVzcGFjZSxcbiAgICBzcGVjaWZpY2l0eTogWzAsIDAsIDFdXG59KSk7XG5jb25zdCBjbGFzc1NlbGVjdG9yXyA9IHBfX25hbWVzcGFjZS5hYihsaXRlcmFsKCcuJyksIGlkZW50aWZpZXJfLCAoZnVsbHN0b3AsIG5hbWUpID0+ICh7XG4gICAgdHlwZTogJ2NsYXNzJyxcbiAgICBuYW1lOiBuYW1lLFxuICAgIHNwZWNpZmljaXR5OiBbMCwgMSwgMF1cbn0pKTtcbmNvbnN0IGlkU2VsZWN0b3JfID0gcF9fbmFtZXNwYWNlLm1hcChoYXNoSWRfLCAobmFtZSkgPT4gKHtcbiAgICB0eXBlOiAnaWQnLFxuICAgIG5hbWU6IG5hbWUsXG4gICAgc3BlY2lmaWNpdHk6IFsxLCAwLCAwXVxufSkpO1xuY29uc3QgYXR0ck1vZGlmaWVyXyA9IHBfX25hbWVzcGFjZS50b2tlbigodCkgPT4ge1xuICAgIGlmICh0Lm5hbWUgPT09ICdpZGVudCcpIHtcbiAgICAgICAgaWYgKHQudGV4dCA9PT0gJ2knIHx8IHQudGV4dCA9PT0gJ0knKSB7XG4gICAgICAgICAgICByZXR1cm4gJ2knO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0LnRleHQgPT09ICdzJyB8fCB0LnRleHQgPT09ICdTJykge1xuICAgICAgICAgICAgcmV0dXJuICdzJztcbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gdW5kZWZpbmVkO1xufSk7XG5jb25zdCBhdHRyVmFsdWVfID0gcF9fbmFtZXNwYWNlLmVpdGhlck9yKHBfX25hbWVzcGFjZS5hYihzdHJpbmdfLCBwX19uYW1lc3BhY2Uub3B0aW9uKHBfX25hbWVzcGFjZS5yaWdodChvcHRpb25hbFdoaXRlc3BhY2VfLCBhdHRyTW9kaWZpZXJfKSwgbnVsbCksICh2LCBtb2QpID0+ICh7IHZhbHVlOiB2LCBtb2RpZmllcjogbW9kIH0pKSwgcF9fbmFtZXNwYWNlLmFiKGlkZW50aWZpZXJfLCBwX19uYW1lc3BhY2Uub3B0aW9uKHBfX25hbWVzcGFjZS5yaWdodCh3aGl0ZXNwYWNlXywgYXR0ck1vZGlmaWVyXyksIG51bGwpLCAodiwgbW9kKSA9PiAoeyB2YWx1ZTogdiwgbW9kaWZpZXI6IG1vZCB9KSkpO1xuY29uc3QgYXR0ck1hdGNoZXJfID0gcF9fbmFtZXNwYWNlLmNob2ljZShwX19uYW1lc3BhY2UubWFwKGxpdGVyYWwoJz0nKSwgKCkgPT4gJz0nKSwgcF9fbmFtZXNwYWNlLmFiKGxpdGVyYWwoJ34nKSwgbGl0ZXJhbCgnPScpLCAoKSA9PiAnfj0nKSwgcF9fbmFtZXNwYWNlLmFiKGxpdGVyYWwoJ3wnKSwgbGl0ZXJhbCgnPScpLCAoKSA9PiAnfD0nKSwgcF9fbmFtZXNwYWNlLmFiKGxpdGVyYWwoJ14nKSwgbGl0ZXJhbCgnPScpLCAoKSA9PiAnXj0nKSwgcF9fbmFtZXNwYWNlLmFiKGxpdGVyYWwoJyQnKSwgbGl0ZXJhbCgnPScpLCAoKSA9PiAnJD0nKSwgcF9fbmFtZXNwYWNlLmFiKGxpdGVyYWwoJyonKSwgbGl0ZXJhbCgnPScpLCAoKSA9PiAnKj0nKSk7XG5jb25zdCBhdHRyUHJlc2VuY2VTZWxlY3Rvcl8gPSBwX19uYW1lc3BhY2UuYWJjKGxpdGVyYWwoJ1snKSwgb3B0aW9uYWxseVNwYWNlZChxdWFsaWZpZWROYW1lXyksIGxpdGVyYWwoJ10nKSwgKGxiciwgeyBuYW1lLCBuYW1lc3BhY2UgfSkgPT4gKHtcbiAgICB0eXBlOiAnYXR0clByZXNlbmNlJyxcbiAgICBuYW1lOiBuYW1lLFxuICAgIG5hbWVzcGFjZTogbmFtZXNwYWNlLFxuICAgIHNwZWNpZmljaXR5OiBbMCwgMSwgMF1cbn0pKTtcbmNvbnN0IGF0dHJWYWx1ZVNlbGVjdG9yXyA9IHBfX25hbWVzcGFjZS5taWRkbGUobGl0ZXJhbCgnWycpLCBwX19uYW1lc3BhY2UuYWJjKG9wdGlvbmFsbHlTcGFjZWQocXVhbGlmaWVkTmFtZV8pLCBhdHRyTWF0Y2hlcl8sIG9wdGlvbmFsbHlTcGFjZWQoYXR0clZhbHVlXyksICh7IG5hbWUsIG5hbWVzcGFjZSB9LCBtYXRjaGVyLCB7IHZhbHVlLCBtb2RpZmllciB9KSA9PiAoe1xuICAgIHR5cGU6ICdhdHRyVmFsdWUnLFxuICAgIG5hbWU6IG5hbWUsXG4gICAgbmFtZXNwYWNlOiBuYW1lc3BhY2UsXG4gICAgbWF0Y2hlcjogbWF0Y2hlcixcbiAgICB2YWx1ZTogdmFsdWUsXG4gICAgbW9kaWZpZXI6IG1vZGlmaWVyLFxuICAgIHNwZWNpZmljaXR5OiBbMCwgMSwgMF1cbn0pKSwgbGl0ZXJhbCgnXScpKTtcbmNvbnN0IGF0dHJTZWxlY3Rvcl8gPSBwX19uYW1lc3BhY2UuZWl0aGVyT3IoYXR0clByZXNlbmNlU2VsZWN0b3JfLCBhdHRyVmFsdWVTZWxlY3Rvcl8pO1xuY29uc3QgdHlwZVNlbGVjdG9yXyA9IHBfX25hbWVzcGFjZS5laXRoZXJPcih1bmlTZWxlY3Rvcl8sIHRhZ1NlbGVjdG9yXyk7XG5jb25zdCBzdWJjbGFzc1NlbGVjdG9yXyA9IHBfX25hbWVzcGFjZS5jaG9pY2UoaWRTZWxlY3Rvcl8sIGNsYXNzU2VsZWN0b3JfLCBhdHRyU2VsZWN0b3JfKTtcbmNvbnN0IGNvbXBvdW5kU2VsZWN0b3JfID0gcF9fbmFtZXNwYWNlLm1hcChwX19uYW1lc3BhY2UuZWl0aGVyT3IocF9fbmFtZXNwYWNlLmZsYXR0ZW4odHlwZVNlbGVjdG9yXywgcF9fbmFtZXNwYWNlLm1hbnkoc3ViY2xhc3NTZWxlY3Rvcl8pKSwgcF9fbmFtZXNwYWNlLm1hbnkxKHN1YmNsYXNzU2VsZWN0b3JfKSksIChzcykgPT4ge1xuICAgIHJldHVybiB7XG4gICAgICAgIHR5cGU6ICdjb21wb3VuZCcsXG4gICAgICAgIGxpc3Q6IHNzLFxuICAgICAgICBzcGVjaWZpY2l0eTogc3VtQWxsU3BlYyhzcy5tYXAocyA9PiBzLnNwZWNpZmljaXR5KSlcbiAgICB9O1xufSk7XG5jb25zdCBjb21iaW5hdG9yXyA9IHBfX25hbWVzcGFjZS5jaG9pY2UocF9fbmFtZXNwYWNlLm1hcChsaXRlcmFsKCc+JyksICgpID0+ICc+JyksIHBfX25hbWVzcGFjZS5tYXAobGl0ZXJhbCgnKycpLCAoKSA9PiAnKycpLCBwX19uYW1lc3BhY2UubWFwKGxpdGVyYWwoJ34nKSwgKCkgPT4gJ34nKSwgcF9fbmFtZXNwYWNlLmFiKGxpdGVyYWwoJ3wnKSwgbGl0ZXJhbCgnfCcpLCAoKSA9PiAnfHwnKSk7XG5jb25zdCBjb21iaW5hdG9yU2VwYXJhdG9yXyA9IHBfX25hbWVzcGFjZS5laXRoZXJPcihvcHRpb25hbGx5U3BhY2VkKGNvbWJpbmF0b3JfKSwgcF9fbmFtZXNwYWNlLm1hcCh3aGl0ZXNwYWNlXywgKCkgPT4gJyAnKSk7XG5jb25zdCBjb21wbGV4U2VsZWN0b3JfID0gcF9fbmFtZXNwYWNlLmxlZnRBc3NvYzIoY29tcG91bmRTZWxlY3Rvcl8sIHBfX25hbWVzcGFjZS5tYXAoY29tYmluYXRvclNlcGFyYXRvcl8sIChjKSA9PiAobGVmdCwgcmlnaHQpID0+ICh7XG4gICAgdHlwZTogJ2NvbXBvdW5kJyxcbiAgICBsaXN0OiBbLi4ucmlnaHQubGlzdCwgeyB0eXBlOiAnY29tYmluYXRvcicsIGNvbWJpbmF0b3I6IGMsIGxlZnQ6IGxlZnQsIHNwZWNpZmljaXR5OiBsZWZ0LnNwZWNpZmljaXR5IH1dLFxuICAgIHNwZWNpZmljaXR5OiBzdW1TcGVjKGxlZnQuc3BlY2lmaWNpdHksIHJpZ2h0LnNwZWNpZmljaXR5KVxufSkpLCBjb21wb3VuZFNlbGVjdG9yXyk7XG5jb25zdCBsaXN0U2VsZWN0b3JfID0gcF9fbmFtZXNwYWNlLmxlZnRBc3NvYzIocF9fbmFtZXNwYWNlLm1hcChjb21wbGV4U2VsZWN0b3JfLCAocykgPT4gKHsgdHlwZTogJ2xpc3QnLCBsaXN0OiBbc10gfSkpLCBwX19uYW1lc3BhY2UubWFwKG9wdGlvbmFsbHlTcGFjZWQobGl0ZXJhbCgnLCcpKSwgKCkgPT4gKGFjYywgbmV4dCkgPT4gKHsgdHlwZTogJ2xpc3QnLCBsaXN0OiBbLi4uYWNjLmxpc3QsIG5leHRdIH0pKSwgY29tcGxleFNlbGVjdG9yXyk7XG5mdW5jdGlvbiBwYXJzZV8ocGFyc2VyLCBzdHIpIHtcbiAgICBpZiAoISh0eXBlb2Ygc3RyID09PSAnc3RyaW5nJyB8fCBzdHIgaW5zdGFuY2VvZiBTdHJpbmcpKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignRXhwZWN0ZWQgYSBzZWxlY3RvciBzdHJpbmcuIEFjdHVhbCBpbnB1dCBpcyBub3QgYSBzdHJpbmchJyk7XG4gICAgfVxuICAgIGNvbnN0IGxleGVyUmVzdWx0ID0gbGV4U2VsZWN0b3Ioc3RyKTtcbiAgICBpZiAoIWxleGVyUmVzdWx0LmNvbXBsZXRlKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgVGhlIGlucHV0IFwiJHtzdHJ9XCIgd2FzIG9ubHkgcGFydGlhbGx5IHRva2VuaXplZCwgc3RvcHBlZCBhdCBvZmZzZXQgJHtsZXhlclJlc3VsdC5vZmZzZXR9IVxcbmAgK1xuICAgICAgICAgICAgcHJldHR5UHJpbnRQb3NpdGlvbihzdHIsIGxleGVyUmVzdWx0Lm9mZnNldCkpO1xuICAgIH1cbiAgICBjb25zdCByZXN1bHQgPSBvcHRpb25hbGx5U3BhY2VkKHBhcnNlcikoeyB0b2tlbnM6IGxleGVyUmVzdWx0LnRva2Vucywgb3B0aW9uczogdW5kZWZpbmVkIH0sIDApO1xuICAgIGlmICghcmVzdWx0Lm1hdGNoZWQpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBObyBtYXRjaCBmb3IgXCIke3N0cn1cIiBpbnB1dCFgKTtcbiAgICB9XG4gICAgaWYgKHJlc3VsdC5wb3NpdGlvbiA8IGxleGVyUmVzdWx0LnRva2Vucy5sZW5ndGgpIHtcbiAgICAgICAgY29uc3QgdG9rZW4gPSBsZXhlclJlc3VsdC50b2tlbnNbcmVzdWx0LnBvc2l0aW9uXTtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBUaGUgaW5wdXQgXCIke3N0cn1cIiB3YXMgb25seSBwYXJ0aWFsbHkgcGFyc2VkLCBzdG9wcGVkIGF0IG9mZnNldCAke3Rva2VuLm9mZnNldH0hXFxuYCArXG4gICAgICAgICAgICBwcmV0dHlQcmludFBvc2l0aW9uKHN0ciwgdG9rZW4ub2Zmc2V0LCB0b2tlbi5sZW4pKTtcbiAgICB9XG4gICAgcmV0dXJuIHJlc3VsdC52YWx1ZTtcbn1cbmZ1bmN0aW9uIHByZXR0eVByaW50UG9zaXRpb24oc3RyLCBvZmZzZXQsIGxlbiA9IDEpIHtcbiAgICByZXR1cm4gYCR7c3RyLnJlcGxhY2UoLyhcXHQpfChcXHIpfChcXG4pL2csIChtLCB0LCByKSA9PiB0ID8gJ1xcdTI0MDknIDogciA/ICdcXHUyNDBkJyA6ICdcXHUyNDBhJyl9XFxuJHsnJy5wYWRFbmQob2Zmc2V0KX0keydeJy5yZXBlYXQobGVuKX1gO1xufVxuZnVuY3Rpb24gcGFyc2Uoc3RyKSB7XG4gICAgcmV0dXJuIHBhcnNlXyhsaXN0U2VsZWN0b3JfLCBzdHIpO1xufVxuZnVuY3Rpb24gcGFyc2UxKHN0cikge1xuICAgIHJldHVybiBwYXJzZV8oY29tcGxleFNlbGVjdG9yXywgc3RyKTtcbn1cblxuZnVuY3Rpb24gc2VyaWFsaXplKHNlbGVjdG9yKSB7XG4gICAgaWYgKCFzZWxlY3Rvci50eXBlKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignVGhpcyBpcyBub3QgYW4gQVNUIG5vZGUuJyk7XG4gICAgfVxuICAgIHN3aXRjaCAoc2VsZWN0b3IudHlwZSkge1xuICAgICAgICBjYXNlICd1bml2ZXJzYWwnOlxuICAgICAgICAgICAgcmV0dXJuIF9zZXJOcyhzZWxlY3Rvci5uYW1lc3BhY2UpICsgJyonO1xuICAgICAgICBjYXNlICd0YWcnOlxuICAgICAgICAgICAgcmV0dXJuIF9zZXJOcyhzZWxlY3Rvci5uYW1lc3BhY2UpICsgX3NlcklkZW50KHNlbGVjdG9yLm5hbWUpO1xuICAgICAgICBjYXNlICdjbGFzcyc6XG4gICAgICAgICAgICByZXR1cm4gJy4nICsgX3NlcklkZW50KHNlbGVjdG9yLm5hbWUpO1xuICAgICAgICBjYXNlICdpZCc6XG4gICAgICAgICAgICByZXR1cm4gJyMnICsgX3NlcklkZW50KHNlbGVjdG9yLm5hbWUpO1xuICAgICAgICBjYXNlICdhdHRyUHJlc2VuY2UnOlxuICAgICAgICAgICAgcmV0dXJuIGBbJHtfc2VyTnMoc2VsZWN0b3IubmFtZXNwYWNlKX0ke19zZXJJZGVudChzZWxlY3Rvci5uYW1lKX1dYDtcbiAgICAgICAgY2FzZSAnYXR0clZhbHVlJzpcbiAgICAgICAgICAgIHJldHVybiBgWyR7X3Nlck5zKHNlbGVjdG9yLm5hbWVzcGFjZSl9JHtfc2VySWRlbnQoc2VsZWN0b3IubmFtZSl9JHtzZWxlY3Rvci5tYXRjaGVyfVwiJHtfc2VyU3RyKHNlbGVjdG9yLnZhbHVlKX1cIiR7KHNlbGVjdG9yLm1vZGlmaWVyID8gc2VsZWN0b3IubW9kaWZpZXIgOiAnJyl9XWA7XG4gICAgICAgIGNhc2UgJ2NvbWJpbmF0b3InOlxuICAgICAgICAgICAgcmV0dXJuIHNlcmlhbGl6ZShzZWxlY3Rvci5sZWZ0KSArIHNlbGVjdG9yLmNvbWJpbmF0b3I7XG4gICAgICAgIGNhc2UgJ2NvbXBvdW5kJzpcbiAgICAgICAgICAgIHJldHVybiBzZWxlY3Rvci5saXN0LnJlZHVjZSgoYWNjLCBub2RlKSA9PiB7XG4gICAgICAgICAgICAgICAgaWYgKG5vZGUudHlwZSA9PT0gJ2NvbWJpbmF0b3InKSB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBzZXJpYWxpemUobm9kZSkgKyBhY2M7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gYWNjICsgc2VyaWFsaXplKG5vZGUpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0sICcnKTtcbiAgICAgICAgY2FzZSAnbGlzdCc6XG4gICAgICAgICAgICByZXR1cm4gc2VsZWN0b3IubGlzdC5tYXAoc2VyaWFsaXplKS5qb2luKCcsJyk7XG4gICAgfVxufVxuZnVuY3Rpb24gX3Nlck5zKG5zKSB7XG4gICAgcmV0dXJuIChucyB8fCBucyA9PT0gJycpXG4gICAgICAgID8gX3NlcklkZW50KG5zKSArICd8J1xuICAgICAgICA6ICcnO1xufVxuZnVuY3Rpb24gX2NvZGVQb2ludChjaGFyKSB7XG4gICAgcmV0dXJuIGBcXFxcJHtjaGFyLmNvZGVQb2ludEF0KDApLnRvU3RyaW5nKDE2KX0gYDtcbn1cbmZ1bmN0aW9uIF9zZXJJZGVudChzdHIpIHtcbiAgICByZXR1cm4gc3RyLnJlcGxhY2UoXG4gICAgLyheWzAtOV0pfCheLVswLTldKXwoXi0kKXwoWy0wLTlhLXpBLVpfXXxbXlxceDAwLVxceDdGXSl8KFxceDAwKXwoW1xceDAxLVxceDFmXXxcXHg3Zil8KFtcXHNcXFNdKS9nLCAobSwgZDEsIGQyLCBoeSwgc2FmZSwgbmwsIGN0cmwsIG90aGVyKSA9PiBkMSA/IF9jb2RlUG9pbnQoZDEpIDpcbiAgICAgICAgZDIgPyAnLScgKyBfY29kZVBvaW50KGQyLnNsaWNlKDEpKSA6XG4gICAgICAgICAgICBoeSA/ICdcXFxcLScgOlxuICAgICAgICAgICAgICAgIHNhZmUgPyBzYWZlIDpcbiAgICAgICAgICAgICAgICAgICAgbmwgPyAnXFx1ZmZmZCcgOlxuICAgICAgICAgICAgICAgICAgICAgICAgY3RybCA/IF9jb2RlUG9pbnQoY3RybCkgOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICdcXFxcJyArIG90aGVyKTtcbn1cbmZ1bmN0aW9uIF9zZXJTdHIoc3RyKSB7XG4gICAgcmV0dXJuIHN0ci5yZXBsYWNlKFxuICAgIC8oXCIpfChcXFxcKXwoXFx4MDApfChbXFx4MDEtXFx4MWZdfFxceDdmKS9nLCAobSwgZHEsIGJzLCBubCwgY3RybCkgPT4gZHEgPyAnXFxcXFwiJyA6XG4gICAgICAgIGJzID8gJ1xcXFxcXFxcJyA6XG4gICAgICAgICAgICBubCA/ICdcXHVmZmZkJyA6XG4gICAgICAgICAgICAgICAgX2NvZGVQb2ludChjdHJsKSk7XG59XG5mdW5jdGlvbiBub3JtYWxpemUoc2VsZWN0b3IpIHtcbiAgICBpZiAoIXNlbGVjdG9yLnR5cGUpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdUaGlzIGlzIG5vdCBhbiBBU1Qgbm9kZS4nKTtcbiAgICB9XG4gICAgc3dpdGNoIChzZWxlY3Rvci50eXBlKSB7XG4gICAgICAgIGNhc2UgJ2NvbXBvdW5kJzoge1xuICAgICAgICAgICAgc2VsZWN0b3IubGlzdC5mb3JFYWNoKG5vcm1hbGl6ZSk7XG4gICAgICAgICAgICBzZWxlY3Rvci5saXN0LnNvcnQoKGEsIGIpID0+IF9jb21wYXJlQXJyYXlzKF9nZXRTZWxlY3RvclByaW9yaXR5KGEpLCBfZ2V0U2VsZWN0b3JQcmlvcml0eShiKSkpO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgIH1cbiAgICAgICAgY2FzZSAnY29tYmluYXRvcic6IHtcbiAgICAgICAgICAgIG5vcm1hbGl6ZShzZWxlY3Rvci5sZWZ0KTtcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICB9XG4gICAgICAgIGNhc2UgJ2xpc3QnOiB7XG4gICAgICAgICAgICBzZWxlY3Rvci5saXN0LmZvckVhY2gobm9ybWFsaXplKTtcbiAgICAgICAgICAgIHNlbGVjdG9yLmxpc3Quc29ydCgoYSwgYikgPT4gKHNlcmlhbGl6ZShhKSA8IHNlcmlhbGl6ZShiKSkgPyAtMSA6IDEpO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIHNlbGVjdG9yO1xufVxuZnVuY3Rpb24gX2dldFNlbGVjdG9yUHJpb3JpdHkoc2VsZWN0b3IpIHtcbiAgICBzd2l0Y2ggKHNlbGVjdG9yLnR5cGUpIHtcbiAgICAgICAgY2FzZSAndW5pdmVyc2FsJzpcbiAgICAgICAgICAgIHJldHVybiBbMV07XG4gICAgICAgIGNhc2UgJ3RhZyc6XG4gICAgICAgICAgICByZXR1cm4gWzFdO1xuICAgICAgICBjYXNlICdpZCc6XG4gICAgICAgICAgICByZXR1cm4gWzJdO1xuICAgICAgICBjYXNlICdjbGFzcyc6XG4gICAgICAgICAgICByZXR1cm4gWzMsIHNlbGVjdG9yLm5hbWVdO1xuICAgICAgICBjYXNlICdhdHRyUHJlc2VuY2UnOlxuICAgICAgICAgICAgcmV0dXJuIFs0LCBzZXJpYWxpemUoc2VsZWN0b3IpXTtcbiAgICAgICAgY2FzZSAnYXR0clZhbHVlJzpcbiAgICAgICAgICAgIHJldHVybiBbNSwgc2VyaWFsaXplKHNlbGVjdG9yKV07XG4gICAgICAgIGNhc2UgJ2NvbWJpbmF0b3InOlxuICAgICAgICAgICAgcmV0dXJuIFsxNSwgc2VyaWFsaXplKHNlbGVjdG9yKV07XG4gICAgfVxufVxuZnVuY3Rpb24gY29tcGFyZVNlbGVjdG9ycyhhLCBiKSB7XG4gICAgcmV0dXJuIF9jb21wYXJlQXJyYXlzKGEuc3BlY2lmaWNpdHksIGIuc3BlY2lmaWNpdHkpO1xufVxuZnVuY3Rpb24gY29tcGFyZVNwZWNpZmljaXR5KGEsIGIpIHtcbiAgICByZXR1cm4gX2NvbXBhcmVBcnJheXMoYSwgYik7XG59XG5mdW5jdGlvbiBfY29tcGFyZUFycmF5cyhhLCBiKSB7XG4gICAgaWYgKCFBcnJheS5pc0FycmF5KGEpIHx8ICFBcnJheS5pc0FycmF5KGIpKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignQXJndW1lbnRzIG11c3QgYmUgYXJyYXlzLicpO1xuICAgIH1cbiAgICBjb25zdCBzaG9ydGVyID0gKGEubGVuZ3RoIDwgYi5sZW5ndGgpID8gYS5sZW5ndGggOiBiLmxlbmd0aDtcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IHNob3J0ZXI7IGkrKykge1xuICAgICAgICBpZiAoYVtpXSA9PT0gYltpXSkge1xuICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIChhW2ldIDwgYltpXSkgPyAtMSA6IDE7XG4gICAgfVxuICAgIHJldHVybiBhLmxlbmd0aCAtIGIubGVuZ3RoO1xufVxuXG5leHBvcnRzLkFzdCA9IGFzdDtcbmV4cG9ydHMuY29tcGFyZVNlbGVjdG9ycyA9IGNvbXBhcmVTZWxlY3RvcnM7XG5leHBvcnRzLmNvbXBhcmVTcGVjaWZpY2l0eSA9IGNvbXBhcmVTcGVjaWZpY2l0eTtcbmV4cG9ydHMubm9ybWFsaXplID0gbm9ybWFsaXplO1xuZXhwb3J0cy5wYXJzZSA9IHBhcnNlO1xuZXhwb3J0cy5wYXJzZTEgPSBwYXJzZTE7XG5leHBvcnRzLnNlcmlhbGl6ZSA9IHNlcmlhbGl6ZTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/parseley/lib/parseley.cjs\n");

/***/ })

};
;