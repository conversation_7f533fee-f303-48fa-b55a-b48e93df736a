"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/linkify-it";
exports.ids = ["vendor-chunks/linkify-it"];
exports.modules = {

/***/ "(rsc)/./node_modules/linkify-it/build/index.cjs.js":
/*!****************************************************!*\
  !*** ./node_modules/linkify-it/build/index.cjs.js ***!
  \****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar uc_micro = __webpack_require__(/*! uc.micro */ \"(rsc)/./node_modules/uc.micro/build/index.cjs.js\");\n\nfunction reFactory (opts) {\n  const re = {};\n  opts = opts || {};\n\n  re.src_Any = uc_micro.Any.source;\n  re.src_Cc = uc_micro.Cc.source;\n  re.src_Z = uc_micro.Z.source;\n  re.src_P = uc_micro.P.source;\n\n  // \\p{\\Z\\P\\Cc\\CF} (white spaces + control + format + punctuation)\n  re.src_ZPCc = [re.src_Z, re.src_P, re.src_Cc].join('|');\n\n  // \\p{\\Z\\Cc} (white spaces + control)\n  re.src_ZCc = [re.src_Z, re.src_Cc].join('|');\n\n  // Experimental. List of chars, completely prohibited in links\n  // because can separate it from other part of text\n  const text_separators = '[><\\uff5c]';\n\n  // All possible word characters (everything without punctuation, spaces & controls)\n  // Defined via punctuation & spaces to save space\n  // Should be something like \\p{\\L\\N\\S\\M} (\\w but without `_`)\n  re.src_pseudo_letter = '(?:(?!' + text_separators + '|' + re.src_ZPCc + ')' + re.src_Any + ')';\n  // The same as abothe but without [0-9]\n  // var src_pseudo_letter_non_d = '(?:(?![0-9]|' + src_ZPCc + ')' + src_Any + ')';\n\n  re.src_ip4 =\n\n    '(?:(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)';\n\n  // Prohibit any of \"@/[]()\" in user/pass to avoid wrong domain fetch.\n  re.src_auth = '(?:(?:(?!' + re.src_ZCc + '|[@/\\\\[\\\\]()]).)+@)?';\n\n  re.src_port =\n\n    '(?::(?:6(?:[0-4]\\\\d{3}|5(?:[0-4]\\\\d{2}|5(?:[0-2]\\\\d|3[0-5])))|[1-5]?\\\\d{1,4}))?';\n\n  re.src_host_terminator =\n\n    '(?=$|' + text_separators + '|' + re.src_ZPCc + ')' +\n    '(?!' + (opts['---'] ? '-(?!--)|' : '-|') + '_|:\\\\d|\\\\.-|\\\\.(?!$|' + re.src_ZPCc + '))';\n\n  re.src_path =\n\n    '(?:' +\n      '[/?#]' +\n        '(?:' +\n          '(?!' + re.src_ZCc + '|' + text_separators + '|[()[\\\\]{}.,\"\\'?!\\\\-;]).|' +\n          '\\\\[(?:(?!' + re.src_ZCc + '|\\\\]).)*\\\\]|' +\n          '\\\\((?:(?!' + re.src_ZCc + '|[)]).)*\\\\)|' +\n          '\\\\{(?:(?!' + re.src_ZCc + '|[}]).)*\\\\}|' +\n          '\\\\\"(?:(?!' + re.src_ZCc + '|[\"]).)+\\\\\"|' +\n          \"\\\\'(?:(?!\" + re.src_ZCc + \"|[']).)+\\\\'|\" +\n\n          // allow `I'm_king` if no pair found\n          \"\\\\'(?=\" + re.src_pseudo_letter + '|[-])|' +\n\n          // google has many dots in \"google search\" links (#66, #81).\n          // github has ... in commit range links,\n          // Restrict to\n          // - english\n          // - percent-encoded\n          // - parts of file path\n          // - params separator\n          // until more examples found.\n          '\\\\.{2,}[a-zA-Z0-9%/&]|' +\n\n          '\\\\.(?!' + re.src_ZCc + '|[.]|$)|' +\n          (opts['---']\n            ? '\\\\-(?!--(?:[^-]|$))(?:-*)|' // `---` => long dash, terminate\n            : '\\\\-+|'\n          ) +\n          // allow `,,,` in paths\n          ',(?!' + re.src_ZCc + '|$)|' +\n\n          // allow `;` if not followed by space-like char\n          ';(?!' + re.src_ZCc + '|$)|' +\n\n          // allow `!!!` in paths, but not at the end\n          '\\\\!+(?!' + re.src_ZCc + '|[!]|$)|' +\n\n          '\\\\?(?!' + re.src_ZCc + '|[?]|$)' +\n        ')+' +\n      '|\\\\/' +\n    ')?';\n\n  // Allow anything in markdown spec, forbid quote (\") at the first position\n  // because emails enclosed in quotes are far more common\n  re.src_email_name =\n\n    '[\\\\-;:&=\\\\+\\\\$,\\\\.a-zA-Z0-9_][\\\\-;:&=\\\\+\\\\$,\\\\\"\\\\.a-zA-Z0-9_]*';\n\n  re.src_xn =\n\n    'xn--[a-z0-9\\\\-]{1,59}';\n\n  // More to read about domain names\n  // http://serverfault.com/questions/638260/\n\n  re.src_domain_root =\n\n    // Allow letters & digits (http://test1)\n    '(?:' +\n      re.src_xn +\n      '|' +\n      re.src_pseudo_letter + '{1,63}' +\n    ')';\n\n  re.src_domain =\n\n    '(?:' +\n      re.src_xn +\n      '|' +\n      '(?:' + re.src_pseudo_letter + ')' +\n      '|' +\n      '(?:' + re.src_pseudo_letter + '(?:-|' + re.src_pseudo_letter + '){0,61}' + re.src_pseudo_letter + ')' +\n    ')';\n\n  re.src_host =\n\n    '(?:' +\n    // Don't need IP check, because digits are already allowed in normal domain names\n    //   src_ip4 +\n    // '|' +\n      '(?:(?:(?:' + re.src_domain + ')\\\\.)*' + re.src_domain/* _root */ + ')' +\n    ')';\n\n  re.tpl_host_fuzzy =\n\n    '(?:' +\n      re.src_ip4 +\n    '|' +\n      '(?:(?:(?:' + re.src_domain + ')\\\\.)+(?:%TLDS%))' +\n    ')';\n\n  re.tpl_host_no_ip_fuzzy =\n\n    '(?:(?:(?:' + re.src_domain + ')\\\\.)+(?:%TLDS%))';\n\n  re.src_host_strict =\n\n    re.src_host + re.src_host_terminator;\n\n  re.tpl_host_fuzzy_strict =\n\n    re.tpl_host_fuzzy + re.src_host_terminator;\n\n  re.src_host_port_strict =\n\n    re.src_host + re.src_port + re.src_host_terminator;\n\n  re.tpl_host_port_fuzzy_strict =\n\n    re.tpl_host_fuzzy + re.src_port + re.src_host_terminator;\n\n  re.tpl_host_port_no_ip_fuzzy_strict =\n\n    re.tpl_host_no_ip_fuzzy + re.src_port + re.src_host_terminator;\n\n  //\n  // Main rules\n  //\n\n  // Rude test fuzzy links by host, for quick deny\n  re.tpl_host_fuzzy_test =\n\n    'localhost|www\\\\.|\\\\.\\\\d{1,3}\\\\.|(?:\\\\.(?:%TLDS%)(?:' + re.src_ZPCc + '|>|$))';\n\n  re.tpl_email_fuzzy =\n\n      '(^|' + text_separators + '|\"|\\\\(|' + re.src_ZCc + ')' +\n      '(' + re.src_email_name + '@' + re.tpl_host_fuzzy_strict + ')';\n\n  re.tpl_link_fuzzy =\n      // Fuzzy link can't be prepended with .:/\\- and non punctuation.\n      // but can start with > (markdown blockquote)\n      '(^|(?![.:/\\\\-_@])(?:[$+<=>^`|\\uff5c]|' + re.src_ZPCc + '))' +\n      '((?![$+<=>^`|\\uff5c])' + re.tpl_host_port_fuzzy_strict + re.src_path + ')';\n\n  re.tpl_link_no_ip_fuzzy =\n      // Fuzzy link can't be prepended with .:/\\- and non punctuation.\n      // but can start with > (markdown blockquote)\n      '(^|(?![.:/\\\\-_@])(?:[$+<=>^`|\\uff5c]|' + re.src_ZPCc + '))' +\n      '((?![$+<=>^`|\\uff5c])' + re.tpl_host_port_no_ip_fuzzy_strict + re.src_path + ')';\n\n  return re\n}\n\n//\n// Helpers\n//\n\n// Merge objects\n//\nfunction assign (obj /* from1, from2, from3, ... */) {\n  const sources = Array.prototype.slice.call(arguments, 1);\n\n  sources.forEach(function (source) {\n    if (!source) { return }\n\n    Object.keys(source).forEach(function (key) {\n      obj[key] = source[key];\n    });\n  });\n\n  return obj\n}\n\nfunction _class (obj) { return Object.prototype.toString.call(obj) }\nfunction isString (obj) { return _class(obj) === '[object String]' }\nfunction isObject (obj) { return _class(obj) === '[object Object]' }\nfunction isRegExp (obj) { return _class(obj) === '[object RegExp]' }\nfunction isFunction (obj) { return _class(obj) === '[object Function]' }\n\nfunction escapeRE (str) { return str.replace(/[.?*+^$[\\]\\\\(){}|-]/g, '\\\\$&') }\n\n//\n\nconst defaultOptions = {\n  fuzzyLink: true,\n  fuzzyEmail: true,\n  fuzzyIP: false\n};\n\nfunction isOptionsObj (obj) {\n  return Object.keys(obj || {}).reduce(function (acc, k) {\n    /* eslint-disable-next-line no-prototype-builtins */\n    return acc || defaultOptions.hasOwnProperty(k)\n  }, false)\n}\n\nconst defaultSchemas = {\n  'http:': {\n    validate: function (text, pos, self) {\n      const tail = text.slice(pos);\n\n      if (!self.re.http) {\n        // compile lazily, because \"host\"-containing variables can change on tlds update.\n        self.re.http = new RegExp(\n          '^\\\\/\\\\/' + self.re.src_auth + self.re.src_host_port_strict + self.re.src_path, 'i'\n        );\n      }\n      if (self.re.http.test(tail)) {\n        return tail.match(self.re.http)[0].length\n      }\n      return 0\n    }\n  },\n  'https:': 'http:',\n  'ftp:': 'http:',\n  '//': {\n    validate: function (text, pos, self) {\n      const tail = text.slice(pos);\n\n      if (!self.re.no_http) {\n      // compile lazily, because \"host\"-containing variables can change on tlds update.\n        self.re.no_http = new RegExp(\n          '^' +\n          self.re.src_auth +\n          // Don't allow single-level domains, because of false positives like '//test'\n          // with code comments\n          '(?:localhost|(?:(?:' + self.re.src_domain + ')\\\\.)+' + self.re.src_domain_root + ')' +\n          self.re.src_port +\n          self.re.src_host_terminator +\n          self.re.src_path,\n\n          'i'\n        );\n      }\n\n      if (self.re.no_http.test(tail)) {\n        // should not be `://` & `///`, that protects from errors in protocol name\n        if (pos >= 3 && text[pos - 3] === ':') { return 0 }\n        if (pos >= 3 && text[pos - 3] === '/') { return 0 }\n        return tail.match(self.re.no_http)[0].length\n      }\n      return 0\n    }\n  },\n  'mailto:': {\n    validate: function (text, pos, self) {\n      const tail = text.slice(pos);\n\n      if (!self.re.mailto) {\n        self.re.mailto = new RegExp(\n          '^' + self.re.src_email_name + '@' + self.re.src_host_strict, 'i'\n        );\n      }\n      if (self.re.mailto.test(tail)) {\n        return tail.match(self.re.mailto)[0].length\n      }\n      return 0\n    }\n  }\n};\n\n// RE pattern for 2-character tlds (autogenerated by ./support/tlds_2char_gen.js)\n/* eslint-disable-next-line max-len */\nconst tlds_2ch_src_re = 'a[cdefgilmnoqrstuwxz]|b[abdefghijmnorstvwyz]|c[acdfghiklmnoruvwxyz]|d[ejkmoz]|e[cegrstu]|f[ijkmor]|g[abdefghilmnpqrstuwy]|h[kmnrtu]|i[delmnoqrst]|j[emop]|k[eghimnprwyz]|l[abcikrstuvy]|m[acdeghklmnopqrstuvwxyz]|n[acefgilopruz]|om|p[aefghklmnrstwy]|qa|r[eosuw]|s[abcdeghijklmnortuvxyz]|t[cdfghjklmnortvwz]|u[agksyz]|v[aceginu]|w[fs]|y[et]|z[amw]';\n\n// DON'T try to make PRs with changes. Extend TLDs with LinkifyIt.tlds() instead\nconst tlds_default = 'biz|com|edu|gov|net|org|pro|web|xxx|aero|asia|coop|info|museum|name|shop|рф'.split('|');\n\nfunction resetScanCache (self) {\n  self.__index__ = -1;\n  self.__text_cache__ = '';\n}\n\nfunction createValidator (re) {\n  return function (text, pos) {\n    const tail = text.slice(pos);\n\n    if (re.test(tail)) {\n      return tail.match(re)[0].length\n    }\n    return 0\n  }\n}\n\nfunction createNormalizer () {\n  return function (match, self) {\n    self.normalize(match);\n  }\n}\n\n// Schemas compiler. Build regexps.\n//\nfunction compile (self) {\n  // Load & clone RE patterns.\n  const re = self.re = reFactory(self.__opts__);\n\n  // Define dynamic patterns\n  const tlds = self.__tlds__.slice();\n\n  self.onCompile();\n\n  if (!self.__tlds_replaced__) {\n    tlds.push(tlds_2ch_src_re);\n  }\n  tlds.push(re.src_xn);\n\n  re.src_tlds = tlds.join('|');\n\n  function untpl (tpl) { return tpl.replace('%TLDS%', re.src_tlds) }\n\n  re.email_fuzzy = RegExp(untpl(re.tpl_email_fuzzy), 'i');\n  re.link_fuzzy = RegExp(untpl(re.tpl_link_fuzzy), 'i');\n  re.link_no_ip_fuzzy = RegExp(untpl(re.tpl_link_no_ip_fuzzy), 'i');\n  re.host_fuzzy_test = RegExp(untpl(re.tpl_host_fuzzy_test), 'i');\n\n  //\n  // Compile each schema\n  //\n\n  const aliases = [];\n\n  self.__compiled__ = {}; // Reset compiled data\n\n  function schemaError (name, val) {\n    throw new Error('(LinkifyIt) Invalid schema \"' + name + '\": ' + val)\n  }\n\n  Object.keys(self.__schemas__).forEach(function (name) {\n    const val = self.__schemas__[name];\n\n    // skip disabled methods\n    if (val === null) { return }\n\n    const compiled = { validate: null, link: null };\n\n    self.__compiled__[name] = compiled;\n\n    if (isObject(val)) {\n      if (isRegExp(val.validate)) {\n        compiled.validate = createValidator(val.validate);\n      } else if (isFunction(val.validate)) {\n        compiled.validate = val.validate;\n      } else {\n        schemaError(name, val);\n      }\n\n      if (isFunction(val.normalize)) {\n        compiled.normalize = val.normalize;\n      } else if (!val.normalize) {\n        compiled.normalize = createNormalizer();\n      } else {\n        schemaError(name, val);\n      }\n\n      return\n    }\n\n    if (isString(val)) {\n      aliases.push(name);\n      return\n    }\n\n    schemaError(name, val);\n  });\n\n  //\n  // Compile postponed aliases\n  //\n\n  aliases.forEach(function (alias) {\n    if (!self.__compiled__[self.__schemas__[alias]]) {\n      // Silently fail on missed schemas to avoid errons on disable.\n      // schemaError(alias, self.__schemas__[alias]);\n      return\n    }\n\n    self.__compiled__[alias].validate =\n      self.__compiled__[self.__schemas__[alias]].validate;\n    self.__compiled__[alias].normalize =\n      self.__compiled__[self.__schemas__[alias]].normalize;\n  });\n\n  //\n  // Fake record for guessed links\n  //\n  self.__compiled__[''] = { validate: null, normalize: createNormalizer() };\n\n  //\n  // Build schema condition\n  //\n  const slist = Object.keys(self.__compiled__)\n    .filter(function (name) {\n      // Filter disabled & fake schemas\n      return name.length > 0 && self.__compiled__[name]\n    })\n    .map(escapeRE)\n    .join('|');\n  // (?!_) cause 1.5x slowdown\n  self.re.schema_test = RegExp('(^|(?!_)(?:[><\\uff5c]|' + re.src_ZPCc + '))(' + slist + ')', 'i');\n  self.re.schema_search = RegExp('(^|(?!_)(?:[><\\uff5c]|' + re.src_ZPCc + '))(' + slist + ')', 'ig');\n  self.re.schema_at_start = RegExp('^' + self.re.schema_search.source, 'i');\n\n  self.re.pretest = RegExp(\n    '(' + self.re.schema_test.source + ')|(' + self.re.host_fuzzy_test.source + ')|@',\n    'i'\n  );\n\n  //\n  // Cleanup\n  //\n\n  resetScanCache(self);\n}\n\n/**\n * class Match\n *\n * Match result. Single element of array, returned by [[LinkifyIt#match]]\n **/\nfunction Match (self, shift) {\n  const start = self.__index__;\n  const end = self.__last_index__;\n  const text = self.__text_cache__.slice(start, end);\n\n  /**\n   * Match#schema -> String\n   *\n   * Prefix (protocol) for matched string.\n   **/\n  this.schema = self.__schema__.toLowerCase();\n  /**\n   * Match#index -> Number\n   *\n   * First position of matched string.\n   **/\n  this.index = start + shift;\n  /**\n   * Match#lastIndex -> Number\n   *\n   * Next position after matched string.\n   **/\n  this.lastIndex = end + shift;\n  /**\n   * Match#raw -> String\n   *\n   * Matched string.\n   **/\n  this.raw = text;\n  /**\n   * Match#text -> String\n   *\n   * Notmalized text of matched string.\n   **/\n  this.text = text;\n  /**\n   * Match#url -> String\n   *\n   * Normalized url of matched string.\n   **/\n  this.url = text;\n}\n\nfunction createMatch (self, shift) {\n  const match = new Match(self, shift);\n\n  self.__compiled__[match.schema].normalize(match, self);\n\n  return match\n}\n\n/**\n * class LinkifyIt\n **/\n\n/**\n * new LinkifyIt(schemas, options)\n * - schemas (Object): Optional. Additional schemas to validate (prefix/validator)\n * - options (Object): { fuzzyLink|fuzzyEmail|fuzzyIP: true|false }\n *\n * Creates new linkifier instance with optional additional schemas.\n * Can be called without `new` keyword for convenience.\n *\n * By default understands:\n *\n * - `http(s)://...` , `ftp://...`, `mailto:...` & `//...` links\n * - \"fuzzy\" links and emails (example.com, <EMAIL>).\n *\n * `schemas` is an object, where each key/value describes protocol/rule:\n *\n * - __key__ - link prefix (usually, protocol name with `:` at the end, `skype:`\n *   for example). `linkify-it` makes shure that prefix is not preceeded with\n *   alphanumeric char and symbols. Only whitespaces and punctuation allowed.\n * - __value__ - rule to check tail after link prefix\n *   - _String_ - just alias to existing rule\n *   - _Object_\n *     - _validate_ - validator function (should return matched length on success),\n *       or `RegExp`.\n *     - _normalize_ - optional function to normalize text & url of matched result\n *       (for example, for @twitter mentions).\n *\n * `options`:\n *\n * - __fuzzyLink__ - recognige URL-s without `http(s):` prefix. Default `true`.\n * - __fuzzyIP__ - allow IPs in fuzzy links above. Can conflict with some texts\n *   like version numbers. Default `false`.\n * - __fuzzyEmail__ - recognize emails without `mailto:` prefix.\n *\n **/\nfunction LinkifyIt (schemas, options) {\n  if (!(this instanceof LinkifyIt)) {\n    return new LinkifyIt(schemas, options)\n  }\n\n  if (!options) {\n    if (isOptionsObj(schemas)) {\n      options = schemas;\n      schemas = {};\n    }\n  }\n\n  this.__opts__ = assign({}, defaultOptions, options);\n\n  // Cache last tested result. Used to skip repeating steps on next `match` call.\n  this.__index__ = -1;\n  this.__last_index__ = -1; // Next scan position\n  this.__schema__ = '';\n  this.__text_cache__ = '';\n\n  this.__schemas__ = assign({}, defaultSchemas, schemas);\n  this.__compiled__ = {};\n\n  this.__tlds__ = tlds_default;\n  this.__tlds_replaced__ = false;\n\n  this.re = {};\n\n  compile(this);\n}\n\n/** chainable\n * LinkifyIt#add(schema, definition)\n * - schema (String): rule name (fixed pattern prefix)\n * - definition (String|RegExp|Object): schema definition\n *\n * Add new rule definition. See constructor description for details.\n **/\nLinkifyIt.prototype.add = function add (schema, definition) {\n  this.__schemas__[schema] = definition;\n  compile(this);\n  return this\n};\n\n/** chainable\n * LinkifyIt#set(options)\n * - options (Object): { fuzzyLink|fuzzyEmail|fuzzyIP: true|false }\n *\n * Set recognition options for links without schema.\n **/\nLinkifyIt.prototype.set = function set (options) {\n  this.__opts__ = assign(this.__opts__, options);\n  return this\n};\n\n/**\n * LinkifyIt#test(text) -> Boolean\n *\n * Searches linkifiable pattern and returns `true` on success or `false` on fail.\n **/\nLinkifyIt.prototype.test = function test (text) {\n  // Reset scan cache\n  this.__text_cache__ = text;\n  this.__index__ = -1;\n\n  if (!text.length) { return false }\n\n  let m, ml, me, len, shift, next, re, tld_pos, at_pos;\n\n  // try to scan for link with schema - that's the most simple rule\n  if (this.re.schema_test.test(text)) {\n    re = this.re.schema_search;\n    re.lastIndex = 0;\n    while ((m = re.exec(text)) !== null) {\n      len = this.testSchemaAt(text, m[2], re.lastIndex);\n      if (len) {\n        this.__schema__ = m[2];\n        this.__index__ = m.index + m[1].length;\n        this.__last_index__ = m.index + m[0].length + len;\n        break\n      }\n    }\n  }\n\n  if (this.__opts__.fuzzyLink && this.__compiled__['http:']) {\n    // guess schemaless links\n    tld_pos = text.search(this.re.host_fuzzy_test);\n    if (tld_pos >= 0) {\n      // if tld is located after found link - no need to check fuzzy pattern\n      if (this.__index__ < 0 || tld_pos < this.__index__) {\n        if ((ml = text.match(this.__opts__.fuzzyIP ? this.re.link_fuzzy : this.re.link_no_ip_fuzzy)) !== null) {\n          shift = ml.index + ml[1].length;\n\n          if (this.__index__ < 0 || shift < this.__index__) {\n            this.__schema__ = '';\n            this.__index__ = shift;\n            this.__last_index__ = ml.index + ml[0].length;\n          }\n        }\n      }\n    }\n  }\n\n  if (this.__opts__.fuzzyEmail && this.__compiled__['mailto:']) {\n    // guess schemaless emails\n    at_pos = text.indexOf('@');\n    if (at_pos >= 0) {\n      // We can't skip this check, because this cases are possible:\n      // <EMAIL>, <EMAIL>\n      if ((me = text.match(this.re.email_fuzzy)) !== null) {\n        shift = me.index + me[1].length;\n        next = me.index + me[0].length;\n\n        if (this.__index__ < 0 || shift < this.__index__ ||\n            (shift === this.__index__ && next > this.__last_index__)) {\n          this.__schema__ = 'mailto:';\n          this.__index__ = shift;\n          this.__last_index__ = next;\n        }\n      }\n    }\n  }\n\n  return this.__index__ >= 0\n};\n\n/**\n * LinkifyIt#pretest(text) -> Boolean\n *\n * Very quick check, that can give false positives. Returns true if link MAY BE\n * can exists. Can be used for speed optimization, when you need to check that\n * link NOT exists.\n **/\nLinkifyIt.prototype.pretest = function pretest (text) {\n  return this.re.pretest.test(text)\n};\n\n/**\n * LinkifyIt#testSchemaAt(text, name, position) -> Number\n * - text (String): text to scan\n * - name (String): rule (schema) name\n * - position (Number): text offset to check from\n *\n * Similar to [[LinkifyIt#test]] but checks only specific protocol tail exactly\n * at given position. Returns length of found pattern (0 on fail).\n **/\nLinkifyIt.prototype.testSchemaAt = function testSchemaAt (text, schema, pos) {\n  // If not supported schema check requested - terminate\n  if (!this.__compiled__[schema.toLowerCase()]) {\n    return 0\n  }\n  return this.__compiled__[schema.toLowerCase()].validate(text, pos, this)\n};\n\n/**\n * LinkifyIt#match(text) -> Array|null\n *\n * Returns array of found link descriptions or `null` on fail. We strongly\n * recommend to use [[LinkifyIt#test]] first, for best speed.\n *\n * ##### Result match description\n *\n * - __schema__ - link schema, can be empty for fuzzy links, or `//` for\n *   protocol-neutral  links.\n * - __index__ - offset of matched text\n * - __lastIndex__ - index of next char after mathch end\n * - __raw__ - matched text\n * - __text__ - normalized text\n * - __url__ - link, generated from matched text\n **/\nLinkifyIt.prototype.match = function match (text) {\n  const result = [];\n  let shift = 0;\n\n  // Try to take previous element from cache, if .test() called before\n  if (this.__index__ >= 0 && this.__text_cache__ === text) {\n    result.push(createMatch(this, shift));\n    shift = this.__last_index__;\n  }\n\n  // Cut head if cache was used\n  let tail = shift ? text.slice(shift) : text;\n\n  // Scan string until end reached\n  while (this.test(tail)) {\n    result.push(createMatch(this, shift));\n\n    tail = tail.slice(this.__last_index__);\n    shift += this.__last_index__;\n  }\n\n  if (result.length) {\n    return result\n  }\n\n  return null\n};\n\n/**\n * LinkifyIt#matchAtStart(text) -> Match|null\n *\n * Returns fully-formed (not fuzzy) link if it starts at the beginning\n * of the string, and null otherwise.\n **/\nLinkifyIt.prototype.matchAtStart = function matchAtStart (text) {\n  // Reset scan cache\n  this.__text_cache__ = text;\n  this.__index__ = -1;\n\n  if (!text.length) return null\n\n  const m = this.re.schema_at_start.exec(text);\n  if (!m) return null\n\n  const len = this.testSchemaAt(text, m[2], m[0].length);\n  if (!len) return null\n\n  this.__schema__ = m[2];\n  this.__index__ = m.index + m[1].length;\n  this.__last_index__ = m.index + m[0].length + len;\n\n  return createMatch(this, 0)\n};\n\n/** chainable\n * LinkifyIt#tlds(list [, keepOld]) -> this\n * - list (Array): list of tlds\n * - keepOld (Boolean): merge with current list if `true` (`false` by default)\n *\n * Load (or merge) new tlds list. Those are user for fuzzy links (without prefix)\n * to avoid false positives. By default this algorythm used:\n *\n * - hostname with any 2-letter root zones are ok.\n * - biz|com|edu|gov|net|org|pro|web|xxx|aero|asia|coop|info|museum|name|shop|рф\n *   are ok.\n * - encoded (`xn--...`) root zones are ok.\n *\n * If list is replaced, then exact match for 2-chars root zones will be checked.\n **/\nLinkifyIt.prototype.tlds = function tlds (list, keepOld) {\n  list = Array.isArray(list) ? list : [list];\n\n  if (!keepOld) {\n    this.__tlds__ = list.slice();\n    this.__tlds_replaced__ = true;\n    compile(this);\n    return this\n  }\n\n  this.__tlds__ = this.__tlds__.concat(list)\n    .sort()\n    .filter(function (el, idx, arr) {\n      return el !== arr[idx - 1]\n    })\n    .reverse();\n\n  compile(this);\n  return this\n};\n\n/**\n * LinkifyIt#normalize(match)\n *\n * Default normalizer (if schema does not define it's own).\n **/\nLinkifyIt.prototype.normalize = function normalize (match) {\n  // Do minimal possible changes by default. Need to collect feedback prior\n  // to move forward https://github.com/markdown-it/linkify-it/issues/1\n\n  if (!match.schema) { match.url = 'http://' + match.url; }\n\n  if (match.schema === 'mailto:' && !/^mailto:/i.test(match.url)) {\n    match.url = 'mailto:' + match.url;\n  }\n};\n\n/**\n * LinkifyIt#onCompile()\n *\n * Override to modify basic RegExp-s.\n **/\nLinkifyIt.prototype.onCompile = function onCompile () {\n};\n\nmodule.exports = LinkifyIt;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/linkify-it/build/index.cjs.js\n");

/***/ })

};
;