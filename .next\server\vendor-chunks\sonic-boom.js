"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/sonic-boom";
exports.ids = ["vendor-chunks/sonic-boom"];
exports.modules = {

/***/ "(rsc)/./node_modules/sonic-boom/index.js":
/*!******************************************!*\
  !*** ./node_modules/sonic-boom/index.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst fs = __webpack_require__(/*! fs */ \"fs\")\nconst EventEmitter = __webpack_require__(/*! events */ \"events\")\nconst inherits = (__webpack_require__(/*! util */ \"util\").inherits)\nconst path = __webpack_require__(/*! path */ \"path\")\nconst sleep = __webpack_require__(/*! atomic-sleep */ \"(rsc)/./node_modules/atomic-sleep/index.js\")\nconst assert = __webpack_require__(/*! assert */ \"assert\")\n\nconst BUSY_WRITE_TIMEOUT = 100\nconst kEmptyBuffer = Buffer.allocUnsafe(0)\n\n// 16 KB. Don't write more than docker buffer size.\n// https://github.com/moby/moby/blob/513ec73831269947d38a644c278ce3cac36783b2/daemon/logger/copier.go#L13\nconst MAX_WRITE = 16 * 1024\n\nconst kContentModeBuffer = 'buffer'\nconst kContentModeUtf8 = 'utf8'\n\nconst [major, minor] = (process.versions.node || '0.0').split('.').map(Number)\nconst kCopyBuffer = major >= 22 && minor >= 7\n\nfunction openFile (file, sonic) {\n  sonic._opening = true\n  sonic._writing = true\n  sonic._asyncDrainScheduled = false\n\n  // NOTE: 'error' and 'ready' events emitted below only relevant when sonic.sync===false\n  // for sync mode, there is no way to add a listener that will receive these\n\n  function fileOpened (err, fd) {\n    if (err) {\n      sonic._reopening = false\n      sonic._writing = false\n      sonic._opening = false\n\n      if (sonic.sync) {\n        process.nextTick(() => {\n          if (sonic.listenerCount('error') > 0) {\n            sonic.emit('error', err)\n          }\n        })\n      } else {\n        sonic.emit('error', err)\n      }\n      return\n    }\n\n    const reopening = sonic._reopening\n\n    sonic.fd = fd\n    sonic.file = file\n    sonic._reopening = false\n    sonic._opening = false\n    sonic._writing = false\n\n    if (sonic.sync) {\n      process.nextTick(() => sonic.emit('ready'))\n    } else {\n      sonic.emit('ready')\n    }\n\n    if (sonic.destroyed) {\n      return\n    }\n\n    // start\n    if ((!sonic._writing && sonic._len > sonic.minLength) || sonic._flushPending) {\n      sonic._actualWrite()\n    } else if (reopening) {\n      process.nextTick(() => sonic.emit('drain'))\n    }\n  }\n\n  const flags = sonic.append ? 'a' : 'w'\n  const mode = sonic.mode\n\n  if (sonic.sync) {\n    try {\n      if (sonic.mkdir) fs.mkdirSync(path.dirname(file), { recursive: true })\n      const fd = fs.openSync(file, flags, mode)\n      fileOpened(null, fd)\n    } catch (err) {\n      fileOpened(err)\n      throw err\n    }\n  } else if (sonic.mkdir) {\n    fs.mkdir(path.dirname(file), { recursive: true }, (err) => {\n      if (err) return fileOpened(err)\n      fs.open(file, flags, mode, fileOpened)\n    })\n  } else {\n    fs.open(file, flags, mode, fileOpened)\n  }\n}\n\nfunction SonicBoom (opts) {\n  if (!(this instanceof SonicBoom)) {\n    return new SonicBoom(opts)\n  }\n\n  let { fd, dest, minLength, maxLength, maxWrite, periodicFlush, sync, append = true, mkdir, retryEAGAIN, fsync, contentMode, mode } = opts || {}\n\n  fd = fd || dest\n\n  this._len = 0\n  this.fd = -1\n  this._bufs = []\n  this._lens = []\n  this._writing = false\n  this._ending = false\n  this._reopening = false\n  this._asyncDrainScheduled = false\n  this._flushPending = false\n  this._hwm = Math.max(minLength || 0, 16387)\n  this.file = null\n  this.destroyed = false\n  this.minLength = minLength || 0\n  this.maxLength = maxLength || 0\n  this.maxWrite = maxWrite || MAX_WRITE\n  this._periodicFlush = periodicFlush || 0\n  this._periodicFlushTimer = undefined\n  this.sync = sync || false\n  this.writable = true\n  this._fsync = fsync || false\n  this.append = append || false\n  this.mode = mode\n  this.retryEAGAIN = retryEAGAIN || (() => true)\n  this.mkdir = mkdir || false\n\n  let fsWriteSync\n  let fsWrite\n  if (contentMode === kContentModeBuffer) {\n    this._writingBuf = kEmptyBuffer\n    this.write = writeBuffer\n    this.flush = flushBuffer\n    this.flushSync = flushBufferSync\n    this._actualWrite = actualWriteBuffer\n    fsWriteSync = () => fs.writeSync(this.fd, this._writingBuf)\n    fsWrite = () => fs.write(this.fd, this._writingBuf, this.release)\n  } else if (contentMode === undefined || contentMode === kContentModeUtf8) {\n    this._writingBuf = ''\n    this.write = write\n    this.flush = flush\n    this.flushSync = flushSync\n    this._actualWrite = actualWrite\n    fsWriteSync = () => fs.writeSync(this.fd, this._writingBuf, 'utf8')\n    fsWrite = () => fs.write(this.fd, this._writingBuf, 'utf8', this.release)\n  } else {\n    throw new Error(`SonicBoom supports \"${kContentModeUtf8}\" and \"${kContentModeBuffer}\", but passed ${contentMode}`)\n  }\n\n  if (typeof fd === 'number') {\n    this.fd = fd\n    process.nextTick(() => this.emit('ready'))\n  } else if (typeof fd === 'string') {\n    openFile(fd, this)\n  } else {\n    throw new Error('SonicBoom supports only file descriptors and files')\n  }\n  if (this.minLength >= this.maxWrite) {\n    throw new Error(`minLength should be smaller than maxWrite (${this.maxWrite})`)\n  }\n\n  this.release = (err, n) => {\n    if (err) {\n      if ((err.code === 'EAGAIN' || err.code === 'EBUSY') && this.retryEAGAIN(err, this._writingBuf.length, this._len - this._writingBuf.length)) {\n        if (this.sync) {\n          // This error code should not happen in sync mode, because it is\n          // not using the underlining operating system asynchronous functions.\n          // However it happens, and so we handle it.\n          // Ref: https://github.com/pinojs/pino/issues/783\n          try {\n            sleep(BUSY_WRITE_TIMEOUT)\n            this.release(undefined, 0)\n          } catch (err) {\n            this.release(err)\n          }\n        } else {\n          // Let's give the destination some time to process the chunk.\n          setTimeout(fsWrite, BUSY_WRITE_TIMEOUT)\n        }\n      } else {\n        this._writing = false\n\n        this.emit('error', err)\n      }\n      return\n    }\n\n    this.emit('write', n)\n    const releasedBufObj = releaseWritingBuf(this._writingBuf, this._len, n)\n    this._len = releasedBufObj.len\n    this._writingBuf = releasedBufObj.writingBuf\n\n    if (this._writingBuf.length) {\n      if (!this.sync) {\n        fsWrite()\n        return\n      }\n\n      try {\n        do {\n          const n = fsWriteSync()\n          const releasedBufObj = releaseWritingBuf(this._writingBuf, this._len, n)\n          this._len = releasedBufObj.len\n          this._writingBuf = releasedBufObj.writingBuf\n        } while (this._writingBuf.length)\n      } catch (err) {\n        this.release(err)\n        return\n      }\n    }\n\n    if (this._fsync) {\n      fs.fsyncSync(this.fd)\n    }\n\n    const len = this._len\n    if (this._reopening) {\n      this._writing = false\n      this._reopening = false\n      this.reopen()\n    } else if (len > this.minLength) {\n      this._actualWrite()\n    } else if (this._ending) {\n      if (len > 0) {\n        this._actualWrite()\n      } else {\n        this._writing = false\n        actualClose(this)\n      }\n    } else {\n      this._writing = false\n      if (this.sync) {\n        if (!this._asyncDrainScheduled) {\n          this._asyncDrainScheduled = true\n          process.nextTick(emitDrain, this)\n        }\n      } else {\n        this.emit('drain')\n      }\n    }\n  }\n\n  this.on('newListener', function (name) {\n    if (name === 'drain') {\n      this._asyncDrainScheduled = false\n    }\n  })\n\n  if (this._periodicFlush !== 0) {\n    this._periodicFlushTimer = setInterval(() => this.flush(null), this._periodicFlush)\n    this._periodicFlushTimer.unref()\n  }\n}\n\n/**\n * Release the writingBuf after fs.write n bytes data\n * @param {string | Buffer} writingBuf - currently writing buffer, usually be instance._writingBuf.\n * @param {number} len - currently buffer length, usually be instance._len.\n * @param {number} n - number of bytes fs already written\n * @returns {{writingBuf: string | Buffer, len: number}} released writingBuf and length\n */\nfunction releaseWritingBuf (writingBuf, len, n) {\n  // if Buffer.byteLength is equal to n, that means writingBuf contains no multi-byte character\n  if (typeof writingBuf === 'string' && Buffer.byteLength(writingBuf) !== n) {\n    // Since the fs.write callback parameter `n` means how many bytes the passed of string\n    // We calculate the original string length for avoiding the multi-byte character issue\n    n = Buffer.from(writingBuf).subarray(0, n).toString().length\n  }\n  len = Math.max(len - n, 0)\n  writingBuf = writingBuf.slice(n)\n  return { writingBuf, len }\n}\n\nfunction emitDrain (sonic) {\n  const hasListeners = sonic.listenerCount('drain') > 0\n  if (!hasListeners) return\n  sonic._asyncDrainScheduled = false\n  sonic.emit('drain')\n}\n\ninherits(SonicBoom, EventEmitter)\n\nfunction mergeBuf (bufs, len) {\n  if (bufs.length === 0) {\n    return kEmptyBuffer\n  }\n\n  if (bufs.length === 1) {\n    return bufs[0]\n  }\n\n  return Buffer.concat(bufs, len)\n}\n\nfunction write (data) {\n  if (this.destroyed) {\n    throw new Error('SonicBoom destroyed')\n  }\n\n  const len = this._len + data.length\n  const bufs = this._bufs\n\n  if (this.maxLength && len > this.maxLength) {\n    this.emit('drop', data)\n    return this._len < this._hwm\n  }\n\n  if (\n    bufs.length === 0 ||\n    bufs[bufs.length - 1].length + data.length > this.maxWrite\n  ) {\n    bufs.push('' + data)\n  } else {\n    bufs[bufs.length - 1] += data\n  }\n\n  this._len = len\n\n  if (!this._writing && this._len >= this.minLength) {\n    this._actualWrite()\n  }\n\n  return this._len < this._hwm\n}\n\nfunction writeBuffer (data) {\n  if (this.destroyed) {\n    throw new Error('SonicBoom destroyed')\n  }\n\n  const len = this._len + data.length\n  const bufs = this._bufs\n  const lens = this._lens\n\n  if (this.maxLength && len > this.maxLength) {\n    this.emit('drop', data)\n    return this._len < this._hwm\n  }\n\n  if (\n    bufs.length === 0 ||\n    lens[lens.length - 1] + data.length > this.maxWrite\n  ) {\n    bufs.push([data])\n    lens.push(data.length)\n  } else {\n    bufs[bufs.length - 1].push(data)\n    lens[lens.length - 1] += data.length\n  }\n\n  this._len = len\n\n  if (!this._writing && this._len >= this.minLength) {\n    this._actualWrite()\n  }\n\n  return this._len < this._hwm\n}\n\nfunction callFlushCallbackOnDrain (cb) {\n  this._flushPending = true\n  const onDrain = () => {\n    // only if _fsync is false to avoid double fsync\n    if (!this._fsync) {\n      try {\n        fs.fsync(this.fd, (err) => {\n          this._flushPending = false\n          cb(err)\n        })\n      } catch (err) {\n        cb(err)\n      }\n    } else {\n      this._flushPending = false\n      cb()\n    }\n    this.off('error', onError)\n  }\n  const onError = (err) => {\n    this._flushPending = false\n    cb(err)\n    this.off('drain', onDrain)\n  }\n\n  this.once('drain', onDrain)\n  this.once('error', onError)\n}\n\nfunction flush (cb) {\n  if (cb != null && typeof cb !== 'function') {\n    throw new Error('flush cb must be a function')\n  }\n\n  if (this.destroyed) {\n    const error = new Error('SonicBoom destroyed')\n    if (cb) {\n      cb(error)\n      return\n    }\n\n    throw error\n  }\n\n  if (this.minLength <= 0) {\n    cb?.()\n    return\n  }\n\n  if (cb) {\n    callFlushCallbackOnDrain.call(this, cb)\n  }\n\n  if (this._writing) {\n    return\n  }\n\n  if (this._bufs.length === 0) {\n    this._bufs.push('')\n  }\n\n  this._actualWrite()\n}\n\nfunction flushBuffer (cb) {\n  if (cb != null && typeof cb !== 'function') {\n    throw new Error('flush cb must be a function')\n  }\n\n  if (this.destroyed) {\n    const error = new Error('SonicBoom destroyed')\n    if (cb) {\n      cb(error)\n      return\n    }\n\n    throw error\n  }\n\n  if (this.minLength <= 0) {\n    cb?.()\n    return\n  }\n\n  if (cb) {\n    callFlushCallbackOnDrain.call(this, cb)\n  }\n\n  if (this._writing) {\n    return\n  }\n\n  if (this._bufs.length === 0) {\n    this._bufs.push([])\n    this._lens.push(0)\n  }\n\n  this._actualWrite()\n}\n\nSonicBoom.prototype.reopen = function (file) {\n  if (this.destroyed) {\n    throw new Error('SonicBoom destroyed')\n  }\n\n  if (this._opening) {\n    this.once('ready', () => {\n      this.reopen(file)\n    })\n    return\n  }\n\n  if (this._ending) {\n    return\n  }\n\n  if (!this.file) {\n    throw new Error('Unable to reopen a file descriptor, you must pass a file to SonicBoom')\n  }\n\n  if (file) {\n    this.file = file\n  }\n  this._reopening = true\n\n  if (this._writing) {\n    return\n  }\n\n  const fd = this.fd\n  this.once('ready', () => {\n    if (fd !== this.fd) {\n      fs.close(fd, (err) => {\n        if (err) {\n          return this.emit('error', err)\n        }\n      })\n    }\n  })\n\n  openFile(this.file, this)\n}\n\nSonicBoom.prototype.end = function () {\n  if (this.destroyed) {\n    throw new Error('SonicBoom destroyed')\n  }\n\n  if (this._opening) {\n    this.once('ready', () => {\n      this.end()\n    })\n    return\n  }\n\n  if (this._ending) {\n    return\n  }\n\n  this._ending = true\n\n  if (this._writing) {\n    return\n  }\n\n  if (this._len > 0 && this.fd >= 0) {\n    this._actualWrite()\n  } else {\n    actualClose(this)\n  }\n}\n\nfunction flushSync () {\n  if (this.destroyed) {\n    throw new Error('SonicBoom destroyed')\n  }\n\n  if (this.fd < 0) {\n    throw new Error('sonic boom is not ready yet')\n  }\n\n  if (!this._writing && this._writingBuf.length > 0) {\n    this._bufs.unshift(this._writingBuf)\n    this._writingBuf = ''\n  }\n\n  let buf = ''\n  while (this._bufs.length || buf) {\n    if (buf.length <= 0) {\n      buf = this._bufs[0]\n    }\n    try {\n      const n = fs.writeSync(this.fd, buf, 'utf8')\n      const releasedBufObj = releaseWritingBuf(buf, this._len, n)\n      buf = releasedBufObj.writingBuf\n      this._len = releasedBufObj.len\n      if (buf.length <= 0) {\n        this._bufs.shift()\n      }\n    } catch (err) {\n      const shouldRetry = err.code === 'EAGAIN' || err.code === 'EBUSY'\n      if (shouldRetry && !this.retryEAGAIN(err, buf.length, this._len - buf.length)) {\n        throw err\n      }\n\n      sleep(BUSY_WRITE_TIMEOUT)\n    }\n  }\n\n  try {\n    fs.fsyncSync(this.fd)\n  } catch {\n    // Skip the error. The fd might not support fsync.\n  }\n}\n\nfunction flushBufferSync () {\n  if (this.destroyed) {\n    throw new Error('SonicBoom destroyed')\n  }\n\n  if (this.fd < 0) {\n    throw new Error('sonic boom is not ready yet')\n  }\n\n  if (!this._writing && this._writingBuf.length > 0) {\n    this._bufs.unshift([this._writingBuf])\n    this._writingBuf = kEmptyBuffer\n  }\n\n  let buf = kEmptyBuffer\n  while (this._bufs.length || buf.length) {\n    if (buf.length <= 0) {\n      buf = mergeBuf(this._bufs[0], this._lens[0])\n    }\n    try {\n      const n = fs.writeSync(this.fd, buf)\n      buf = buf.subarray(n)\n      this._len = Math.max(this._len - n, 0)\n      if (buf.length <= 0) {\n        this._bufs.shift()\n        this._lens.shift()\n      }\n    } catch (err) {\n      const shouldRetry = err.code === 'EAGAIN' || err.code === 'EBUSY'\n      if (shouldRetry && !this.retryEAGAIN(err, buf.length, this._len - buf.length)) {\n        throw err\n      }\n\n      sleep(BUSY_WRITE_TIMEOUT)\n    }\n  }\n}\n\nSonicBoom.prototype.destroy = function () {\n  if (this.destroyed) {\n    return\n  }\n  actualClose(this)\n}\n\nfunction actualWrite () {\n  const release = this.release\n  this._writing = true\n  this._writingBuf = this._writingBuf || this._bufs.shift() || ''\n\n  if (this.sync) {\n    try {\n      const written = fs.writeSync(this.fd, this._writingBuf, 'utf8')\n      release(null, written)\n    } catch (err) {\n      release(err)\n    }\n  } else {\n    fs.write(this.fd, this._writingBuf, 'utf8', release)\n  }\n}\n\nfunction actualWriteBuffer () {\n  const release = this.release\n  this._writing = true\n  this._writingBuf = this._writingBuf.length ? this._writingBuf : mergeBuf(this._bufs.shift(), this._lens.shift())\n\n  if (this.sync) {\n    try {\n      const written = fs.writeSync(this.fd, this._writingBuf)\n      release(null, written)\n    } catch (err) {\n      release(err)\n    }\n  } else {\n    // fs.write will need to copy string to buffer anyway so\n    // we do it here to avoid the overhead of calculating the buffer size\n    // in releaseWritingBuf.\n    if (kCopyBuffer) {\n      this._writingBuf = Buffer.from(this._writingBuf)\n    }\n    fs.write(this.fd, this._writingBuf, release)\n  }\n}\n\nfunction actualClose (sonic) {\n  if (sonic.fd === -1) {\n    sonic.once('ready', actualClose.bind(null, sonic))\n    return\n  }\n\n  if (sonic._periodicFlushTimer !== undefined) {\n    clearInterval(sonic._periodicFlushTimer)\n  }\n\n  sonic.destroyed = true\n  sonic._bufs = []\n  sonic._lens = []\n\n  assert(typeof sonic.fd === 'number', `sonic.fd must be a number, got ${typeof sonic.fd}`)\n  try {\n    fs.fsync(sonic.fd, closeWrapped)\n  } catch {\n  }\n\n  function closeWrapped () {\n    // We skip errors in fsync\n\n    if (sonic.fd !== 1 && sonic.fd !== 2) {\n      fs.close(sonic.fd, done)\n    } else {\n      done()\n    }\n  }\n\n  function done (err) {\n    if (err) {\n      sonic.emit('error', err)\n      return\n    }\n\n    if (sonic._ending && !sonic._writing) {\n      sonic.emit('finish')\n    }\n    sonic.emit('close')\n  }\n}\n\n/**\n * These export configurations enable JS and TS developers\n * to consumer SonicBoom in whatever way best suits their needs.\n * Some examples of supported import syntax includes:\n * - `const SonicBoom = require('SonicBoom')`\n * - `const { SonicBoom } = require('SonicBoom')`\n * - `import * as SonicBoom from 'SonicBoom'`\n * - `import { SonicBoom } from 'SonicBoom'`\n * - `import SonicBoom from 'SonicBoom'`\n */\nSonicBoom.SonicBoom = SonicBoom\nSonicBoom.default = SonicBoom\nmodule.exports = SonicBoom\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/sonic-boom/index.js\n");

/***/ })

};
;