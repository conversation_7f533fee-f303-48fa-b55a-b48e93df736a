"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/leac";
exports.ids = ["vendor-chunks/leac"];
exports.modules = {

/***/ "(rsc)/./node_modules/leac/lib/leac.cjs":
/*!****************************************!*\
  !*** ./node_modules/leac/lib/leac.cjs ***!
  \****************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("Object.defineProperty(exports, \"__esModule\", ({value:!0}));const e=/\\n/g;function t(t){const o=[...t.matchAll(e)].map((e=>e.index||0));o.unshift(-1);const s=n(o,0,o.length);return e=>r(s,e)}function n(e,t,r){if(r-t==1)return{offset:e[t],index:t+1};const o=Math.ceil((t+r)/2),s=n(e,t,o),l=n(e,o,r);return{offset:s.offset,low:s,high:l}}function r(e,t){return function(e){return Object.prototype.hasOwnProperty.call(e,\"index\")}(e)?{line:e.index,column:t-e.offset}:r(e.high.offset<t?e.high:e.low,t)}function o(e,t){return{...e,regex:s(e,t)}}function s(e,t){if(0===e.name.length)throw new Error(`Rule #${t} has empty name, which is not allowed.`);if(function(e){return Object.prototype.hasOwnProperty.call(e,\"regex\")}(e))return function(e){if(e.global)throw new Error(`Regular expression /${e.source}/${e.flags} contains the global flag, which is not allowed.`);return e.sticky?e:new RegExp(e.source,e.flags+\"y\")}(e.regex);if(function(e){return Object.prototype.hasOwnProperty.call(e,\"str\")}(e)){if(0===e.str.length)throw new Error(`Rule #${t} (\"${e.name}\") has empty \"str\" property, which is not allowed.`);return new RegExp(l(e.str),\"y\")}return new RegExp(l(e.name),\"y\")}function l(e){return e.replace(/[-[\\]{}()*+!<=:?./\\\\^$|#\\s,]/g,\"\\\\$&\")}exports.createLexer=function(e,n=\"\",r={}){const s=\"string\"!=typeof n?n:r,l=\"string\"==typeof n?n:\"\",c=e.map(o),i=!!s.lineNumbers;return function(e,n=0){const r=i?t(e):()=>({line:0,column:0});let o=n;const s=[];e:for(;o<e.length;){let t=!1;for(const n of c){n.regex.lastIndex=o;const c=n.regex.exec(e);if(c&&c[0].length>0){if(!n.discard){const e=r(o),t=\"string\"==typeof n.replace?c[0].replace(new RegExp(n.regex.source,n.regex.flags),n.replace):c[0];s.push({state:l,name:n.name,text:t,offset:o,len:c[0].length,line:e.line,column:e.column})}if(o=n.regex.lastIndex,t=!0,n.push){const t=n.push(e,o);s.push(...t.tokens),o=t.offset}if(n.pop)break e;break}}if(!t)break}return{tokens:s,offset:o,complete:e.length<=o}}};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/leac/lib/leac.cjs\n");

/***/ })

};
;