"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/keycloak-js";
exports.ids = ["vendor-chunks/keycloak-js"];
exports.modules = {

/***/ "(ssr)/./node_modules/keycloak-js/lib/keycloak.js":
/*!**************************************************!*\
  !*** ./node_modules/keycloak-js/lib/keycloak.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/*\n * Copyright 2016 Red Hat, Inc. and/or its affiliates\n * and other contributors as indicated by the <AUTHOR> *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction Keycloak (config) {\n    if (!(this instanceof Keycloak)) {\n        throw new Error(\"The 'Keycloak' constructor must be invoked with 'new'.\")\n    }\n\n    if (typeof config !== 'string' && !isObject(config)) {\n        throw new Error(\"The 'Keycloak' constructor must be provided with a configuration object, or a URL to a JSON configuration file.\");\n    }\n\n    if (isObject(config)) {\n        const requiredProperties = 'oidcProvider' in config\n            ? ['clientId']\n            : ['url', 'realm', 'clientId'];\n\n        for (const property of requiredProperties) {\n            if (!config[property]) {\n                throw new Error(`The configuration object is missing the required '${property}' property.`);\n            }\n        }\n    }\n\n    var kc = this;\n    var adapter;\n    var refreshQueue = [];\n    var callbackStorage;\n\n    var loginIframe = {\n        enable: true,\n        callbackList: [],\n        interval: 5\n    };\n\n    kc.didInitialize = false;\n\n    var useNonce = true;\n    var logInfo = createLogger(console.info);\n    var logWarn = createLogger(console.warn);\n\n    if (!globalThis.isSecureContext) {\n        logWarn(\n            \"[KEYCLOAK] Keycloak JS must be used in a 'secure context' to function properly as it relies on browser APIs that are otherwise not available.\\n\" +\n            \"Continuing to run your application insecurely will lead to unexpected behavior and breakage.\\n\\n\" +\n            \"For more information see: https://developer.mozilla.org/en-US/docs/Web/Security/Secure_Contexts\"\n        );\n    }\n\n    kc.init = function (initOptions = {}) {\n        if (kc.didInitialize) {\n            throw new Error(\"A 'Keycloak' instance can only be initialized once.\");\n        }\n\n        kc.didInitialize = true;\n\n        kc.authenticated = false;\n\n        callbackStorage = createCallbackStorage();\n        var adapters = ['default', 'cordova', 'cordova-native'];\n\n        if (adapters.indexOf(initOptions.adapter) > -1) {\n            adapter = loadAdapter(initOptions.adapter);\n        } else if (typeof initOptions.adapter === \"object\") {\n            adapter = initOptions.adapter;\n        } else {\n            if (window.Cordova || window.cordova) {\n                adapter = loadAdapter('cordova');\n            } else {\n                adapter = loadAdapter();\n            }\n        }\n\n        if (typeof initOptions.useNonce !== 'undefined') {\n            useNonce = initOptions.useNonce;\n        }\n\n        if (typeof initOptions.checkLoginIframe !== 'undefined') {\n            loginIframe.enable = initOptions.checkLoginIframe;\n        }\n\n        if (initOptions.checkLoginIframeInterval) {\n            loginIframe.interval = initOptions.checkLoginIframeInterval;\n        }\n\n        if (initOptions.onLoad === 'login-required') {\n            kc.loginRequired = true;\n        }\n\n        if (initOptions.responseMode) {\n            if (initOptions.responseMode === 'query' || initOptions.responseMode === 'fragment') {\n                kc.responseMode = initOptions.responseMode;\n            } else {\n                throw 'Invalid value for responseMode';\n            }\n        }\n\n        if (initOptions.flow) {\n            switch (initOptions.flow) {\n                case 'standard':\n                    kc.responseType = 'code';\n                    break;\n                case 'implicit':\n                    kc.responseType = 'id_token token';\n                    break;\n                case 'hybrid':\n                    kc.responseType = 'code id_token token';\n                    break;\n                default:\n                    throw 'Invalid value for flow';\n            }\n            kc.flow = initOptions.flow;\n        }\n\n        if (initOptions.timeSkew != null) {\n            kc.timeSkew = initOptions.timeSkew;\n        }\n\n        if(initOptions.redirectUri) {\n            kc.redirectUri = initOptions.redirectUri;\n        }\n\n        if (initOptions.silentCheckSsoRedirectUri) {\n            kc.silentCheckSsoRedirectUri = initOptions.silentCheckSsoRedirectUri;\n        }\n\n        if (typeof initOptions.silentCheckSsoFallback === 'boolean') {\n            kc.silentCheckSsoFallback = initOptions.silentCheckSsoFallback;\n        } else {\n            kc.silentCheckSsoFallback = true;\n        }\n\n        if (typeof initOptions.pkceMethod !== \"undefined\") {\n            if (initOptions.pkceMethod !== \"S256\" && initOptions.pkceMethod !== false) {\n                throw new TypeError(`Invalid value for pkceMethod', expected 'S256' or false but got ${initOptions.pkceMethod}.`);\n            }\n\n            kc.pkceMethod = initOptions.pkceMethod;\n        } else {\n            kc.pkceMethod = \"S256\";\n        }\n\n        if (typeof initOptions.enableLogging === 'boolean') {\n            kc.enableLogging = initOptions.enableLogging;\n        } else {\n            kc.enableLogging = false;\n        }\n\n        if (initOptions.logoutMethod === 'POST') {\n            kc.logoutMethod = 'POST';\n        } else {\n            kc.logoutMethod = 'GET';\n        }\n\n        if (typeof initOptions.scope === 'string') {\n            kc.scope = initOptions.scope;\n        }\n\n        if (typeof initOptions.acrValues === 'string') {\n            kc.acrValues = initOptions.acrValues;\n        }\n\n        if (typeof initOptions.messageReceiveTimeout === 'number' && initOptions.messageReceiveTimeout > 0) {\n            kc.messageReceiveTimeout = initOptions.messageReceiveTimeout;\n        } else {\n            kc.messageReceiveTimeout = 10000;\n        }\n\n        if (!kc.responseMode) {\n            kc.responseMode = 'fragment';\n        }\n        if (!kc.responseType) {\n            kc.responseType = 'code';\n            kc.flow = 'standard';\n        }\n\n        var promise = createPromise();\n\n        var initPromise = createPromise();\n        initPromise.promise.then(function() {\n            kc.onReady && kc.onReady(kc.authenticated);\n            promise.setSuccess(kc.authenticated);\n        }).catch(function(error) {\n            promise.setError(error);\n        });\n\n        var configPromise = loadConfig();\n\n        function onLoad() {\n            var doLogin = function(prompt) {\n                if (!prompt) {\n                    options.prompt = 'none';\n                }\n\n                if (initOptions.locale) {\n                    options.locale = initOptions.locale;\n                }\n                kc.login(options).then(function () {\n                    initPromise.setSuccess();\n                }).catch(function (error) {\n                    initPromise.setError(error);\n                });\n            }\n\n            var checkSsoSilently = async function() {\n                var ifrm = document.createElement(\"iframe\");\n                var src = await kc.createLoginUrl({prompt: 'none', redirectUri: kc.silentCheckSsoRedirectUri});\n                ifrm.setAttribute(\"src\", src);\n                ifrm.setAttribute(\"sandbox\", \"allow-storage-access-by-user-activation allow-scripts allow-same-origin\");\n                ifrm.setAttribute(\"title\", \"keycloak-silent-check-sso\");\n                ifrm.style.display = \"none\";\n                document.body.appendChild(ifrm);\n\n                var messageCallback = function(event) {\n                    if (event.origin !== window.location.origin || ifrm.contentWindow !== event.source) {\n                        return;\n                    }\n\n                    var oauth = parseCallback(event.data);\n                    processCallback(oauth, initPromise);\n\n                    document.body.removeChild(ifrm);\n                    window.removeEventListener(\"message\", messageCallback);\n                };\n\n                window.addEventListener(\"message\", messageCallback);\n            };\n\n            var options = {};\n            switch (initOptions.onLoad) {\n                case 'check-sso':\n                    if (loginIframe.enable) {\n                        setupCheckLoginIframe().then(function() {\n                            checkLoginIframe().then(function (unchanged) {\n                                if (!unchanged) {\n                                    kc.silentCheckSsoRedirectUri ? checkSsoSilently() : doLogin(false);\n                                } else {\n                                    initPromise.setSuccess();\n                                }\n                            }).catch(function (error) {\n                                initPromise.setError(error);\n                            });\n                        });\n                    } else {\n                        kc.silentCheckSsoRedirectUri ? checkSsoSilently() : doLogin(false);\n                    }\n                    break;\n                case 'login-required':\n                    doLogin(true);\n                    break;\n                default:\n                    throw 'Invalid value for onLoad';\n            }\n        }\n\n        function processInit() {\n            var callback = parseCallback(window.location.href);\n\n            if (callback) {\n                window.history.replaceState(window.history.state, null, callback.newUrl);\n            }\n\n            if (callback && callback.valid) {\n                return setupCheckLoginIframe().then(function() {\n                    processCallback(callback, initPromise);\n                }).catch(function (error) {\n                    initPromise.setError(error);\n                });\n            }\n\n            if (initOptions.token && initOptions.refreshToken) {\n                setToken(initOptions.token, initOptions.refreshToken, initOptions.idToken);\n\n                if (loginIframe.enable) {\n                    setupCheckLoginIframe().then(function() {\n                        checkLoginIframe().then(function (unchanged) {\n                            if (unchanged) {\n                                kc.onAuthSuccess && kc.onAuthSuccess();\n                                initPromise.setSuccess();\n                                scheduleCheckIframe();\n                            } else {\n                                initPromise.setSuccess();\n                            }\n                        }).catch(function (error) {\n                            initPromise.setError(error);\n                        });\n                    });\n                } else {\n                    kc.updateToken(-1).then(function() {\n                        kc.onAuthSuccess && kc.onAuthSuccess();\n                        initPromise.setSuccess();\n                    }).catch(function(error) {\n                        kc.onAuthError && kc.onAuthError();\n                        if (initOptions.onLoad) {\n                            onLoad();\n                        } else {\n                            initPromise.setError(error);\n                        }\n                    });\n                }\n            } else if (initOptions.onLoad) {\n                onLoad();\n            } else {\n                initPromise.setSuccess();\n            }\n        }\n\n        configPromise.then(function () {\n            check3pCookiesSupported()\n                .then(processInit)\n                .catch(function (error) {\n                    promise.setError(error);\n                });\n        });\n        configPromise.catch(function (error) {\n            promise.setError(error);\n        });\n\n        return promise.promise;\n    }\n\n    kc.login = function (options) {\n        return adapter.login(options);\n    }\n\n    function generateRandomData(len) {\n        if (typeof crypto === \"undefined\" || typeof crypto.getRandomValues === \"undefined\") {\n            throw new Error(\"Web Crypto API is not available.\");\n        }\n\n        return crypto.getRandomValues(new Uint8Array(len));\n    }\n\n    function generateCodeVerifier(len) {\n        return generateRandomString(len, 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789');\n    }\n\n    function generateRandomString(len, alphabet){\n        var randomData = generateRandomData(len);\n        var chars = new Array(len);\n        for (var i = 0; i < len; i++) {\n            chars[i] = alphabet.charCodeAt(randomData[i] % alphabet.length);\n        }\n        return String.fromCharCode.apply(null, chars);\n    }\n\n    async function generatePkceChallenge(pkceMethod, codeVerifier) {\n        if (pkceMethod !== \"S256\") {\n            throw new TypeError(`Invalid value for 'pkceMethod', expected 'S256' but got '${pkceMethod}'.`);\n        }\n\n        // hash codeVerifier, then encode as url-safe base64 without padding\n        const hashBytes = new Uint8Array(await sha256Digest(codeVerifier));\n        const encodedHash = bytesToBase64(hashBytes)\n            .replace(/\\+/g, '-')\n            .replace(/\\//g, '_')\n            .replace(/\\=/g, '');\n\n        return encodedHash;\n    }\n\n    function buildClaimsParameter(requestedAcr){\n        var claims = {\n            id_token: {\n                acr: requestedAcr\n            }\n        }\n        return JSON.stringify(claims);\n    }\n\n    kc.createLoginUrl = async function(options) {\n        var state = createUUID();\n        var nonce = createUUID();\n\n        var redirectUri = adapter.redirectUri(options);\n\n        var callbackState = {\n            state: state,\n            nonce: nonce,\n            redirectUri: encodeURIComponent(redirectUri),\n            loginOptions: options\n        };\n\n        if (options && options.prompt) {\n            callbackState.prompt = options.prompt;\n        }\n\n        var baseUrl;\n        if (options && options.action == 'register') {\n            baseUrl = kc.endpoints.register();\n        } else {\n            baseUrl = kc.endpoints.authorize();\n        }\n\n        var scope = options && options.scope || kc.scope;\n        if (!scope) {\n            // if scope is not set, default to \"openid\"\n            scope = \"openid\";\n        } else if (scope.indexOf(\"openid\") === -1) {\n            // if openid scope is missing, prefix the given scopes with it\n            scope = \"openid \" + scope;\n        }\n\n        var url = baseUrl\n            + '?client_id=' + encodeURIComponent(kc.clientId)\n            + '&redirect_uri=' + encodeURIComponent(redirectUri)\n            + '&state=' + encodeURIComponent(state)\n            + '&response_mode=' + encodeURIComponent(kc.responseMode)\n            + '&response_type=' + encodeURIComponent(kc.responseType)\n            + '&scope=' + encodeURIComponent(scope);\n        if (useNonce) {\n            url = url + '&nonce=' + encodeURIComponent(nonce);\n        }\n\n        if (options && options.prompt) {\n            url += '&prompt=' + encodeURIComponent(options.prompt);\n        }\n\n        if (options && typeof options.maxAge === 'number') {\n            url += '&max_age=' + encodeURIComponent(options.maxAge);\n        }\n\n        if (options && options.loginHint) {\n            url += '&login_hint=' + encodeURIComponent(options.loginHint);\n        }\n\n        if (options && options.idpHint) {\n            url += '&kc_idp_hint=' + encodeURIComponent(options.idpHint);\n        }\n\n        if (options && options.action && options.action != 'register') {\n            url += '&kc_action=' + encodeURIComponent(options.action);\n        }\n\n        if (options && options.locale) {\n            url += '&ui_locales=' + encodeURIComponent(options.locale);\n        }\n\n        if (options && options.acr) {\n            var claimsParameter = buildClaimsParameter(options.acr);\n            url += '&claims=' + encodeURIComponent(claimsParameter);\n        }\n\n        if ((options && options.acrValues) || kc.acrValues) {\n            url += '&acr_values=' + encodeURIComponent(options.acrValues || kc.acrValues);\n        }\n\n        if (kc.pkceMethod) {\n            try {\n                const codeVerifier = generateCodeVerifier(96);\n                const pkceChallenge = await generatePkceChallenge(kc.pkceMethod, codeVerifier);\n\n                callbackState.pkceCodeVerifier = codeVerifier;\n\n                url += '&code_challenge=' + pkceChallenge;\n                url += '&code_challenge_method=' + kc.pkceMethod;\n            } catch (error) {\n                throw new Error(\"Failed to generate PKCE challenge.\", { cause: error });\n            }\n        }\n\n        callbackStorage.add(callbackState);\n\n        return url;\n    }\n\n    kc.logout = function(options) {\n        return adapter.logout(options);\n    }\n\n    kc.createLogoutUrl = function(options) {\n\n        const logoutMethod = options?.logoutMethod ?? kc.logoutMethod;\n        if (logoutMethod === 'POST') {\n            return kc.endpoints.logout();\n        }\n\n        var url = kc.endpoints.logout()\n            + '?client_id=' + encodeURIComponent(kc.clientId)\n            + '&post_logout_redirect_uri=' + encodeURIComponent(adapter.redirectUri(options, false));\n\n        if (kc.idToken) {\n            url += '&id_token_hint=' + encodeURIComponent(kc.idToken);\n        }\n\n        return url;\n    }\n\n    kc.register = function (options) {\n        return adapter.register(options);\n    }\n\n    kc.createRegisterUrl = async function(options) {\n        if (!options) {\n            options = {};\n        }\n        options.action = 'register';\n        return await kc.createLoginUrl(options);\n    }\n\n    kc.createAccountUrl = function(options) {\n        var realm = getRealmUrl();\n        var url = undefined;\n        if (typeof realm !== 'undefined') {\n            url = realm\n            + '/account'\n            + '?referrer=' + encodeURIComponent(kc.clientId)\n            + '&referrer_uri=' + encodeURIComponent(adapter.redirectUri(options));\n        }\n        return url;\n    }\n\n    kc.accountManagement = function() {\n        return adapter.accountManagement();\n    }\n\n    kc.hasRealmRole = function (role) {\n        var access = kc.realmAccess;\n        return !!access && access.roles.indexOf(role) >= 0;\n    }\n\n    kc.hasResourceRole = function(role, resource) {\n        if (!kc.resourceAccess) {\n            return false;\n        }\n\n        var access = kc.resourceAccess[resource || kc.clientId];\n        return !!access && access.roles.indexOf(role) >= 0;\n    }\n\n    kc.loadUserProfile = function() {\n        var url = getRealmUrl() + '/account';\n        var req = new XMLHttpRequest();\n        req.open('GET', url, true);\n        req.setRequestHeader('Accept', 'application/json');\n        req.setRequestHeader('Authorization', 'bearer ' + kc.token);\n\n        var promise = createPromise();\n\n        req.onreadystatechange = function () {\n            if (req.readyState == 4) {\n                if (req.status == 200) {\n                    kc.profile = JSON.parse(req.responseText);\n                    promise.setSuccess(kc.profile);\n                } else {\n                    promise.setError();\n                }\n            }\n        }\n\n        req.send();\n\n        return promise.promise;\n    }\n\n    kc.loadUserInfo = function() {\n        var url = kc.endpoints.userinfo();\n        var req = new XMLHttpRequest();\n        req.open('GET', url, true);\n        req.setRequestHeader('Accept', 'application/json');\n        req.setRequestHeader('Authorization', 'bearer ' + kc.token);\n\n        var promise = createPromise();\n\n        req.onreadystatechange = function () {\n            if (req.readyState == 4) {\n                if (req.status == 200) {\n                    kc.userInfo = JSON.parse(req.responseText);\n                    promise.setSuccess(kc.userInfo);\n                } else {\n                    promise.setError();\n                }\n            }\n        }\n\n        req.send();\n\n        return promise.promise;\n    }\n\n    kc.isTokenExpired = function(minValidity) {\n        if (!kc.tokenParsed || (!kc.refreshToken && kc.flow != 'implicit' )) {\n            throw 'Not authenticated';\n        }\n\n        if (kc.timeSkew == null) {\n            logInfo('[KEYCLOAK] Unable to determine if token is expired as timeskew is not set');\n            return true;\n        }\n\n        var expiresIn = kc.tokenParsed['exp'] - Math.ceil(new Date().getTime() / 1000) + kc.timeSkew;\n        if (minValidity) {\n            if (isNaN(minValidity)) {\n                throw 'Invalid minValidity';\n            }\n            expiresIn -= minValidity;\n        }\n        return expiresIn < 0;\n    }\n\n    kc.updateToken = function(minValidity) {\n        var promise = createPromise();\n\n        if (!kc.refreshToken) {\n            promise.setError();\n            return promise.promise;\n        }\n\n        minValidity = minValidity || 5;\n\n        var exec = function() {\n            var refreshToken = false;\n            if (minValidity == -1) {\n                refreshToken = true;\n                logInfo('[KEYCLOAK] Refreshing token: forced refresh');\n            } else if (!kc.tokenParsed || kc.isTokenExpired(minValidity)) {\n                refreshToken = true;\n                logInfo('[KEYCLOAK] Refreshing token: token expired');\n            }\n\n            if (!refreshToken) {\n                promise.setSuccess(false);\n            } else {\n                var params = 'grant_type=refresh_token&' + 'refresh_token=' + kc.refreshToken;\n                var url = kc.endpoints.token();\n\n                refreshQueue.push(promise);\n\n                if (refreshQueue.length == 1) {\n                    var req = new XMLHttpRequest();\n                    req.open('POST', url, true);\n                    req.setRequestHeader('Content-type', 'application/x-www-form-urlencoded');\n                    req.withCredentials = true;\n\n                    params += '&client_id=' + encodeURIComponent(kc.clientId);\n\n                    var timeLocal = new Date().getTime();\n\n                    req.onreadystatechange = function () {\n                        if (req.readyState == 4) {\n                            if (req.status == 200) {\n                                logInfo('[KEYCLOAK] Token refreshed');\n\n                                timeLocal = (timeLocal + new Date().getTime()) / 2;\n\n                                var tokenResponse = JSON.parse(req.responseText);\n\n                                setToken(tokenResponse['access_token'], tokenResponse['refresh_token'], tokenResponse['id_token'], timeLocal);\n\n                                kc.onAuthRefreshSuccess && kc.onAuthRefreshSuccess();\n                                for (var p = refreshQueue.pop(); p != null; p = refreshQueue.pop()) {\n                                    p.setSuccess(true);\n                                }\n                            } else {\n                                logWarn('[KEYCLOAK] Failed to refresh token');\n\n                                if (req.status == 400) {\n                                    kc.clearToken();\n                                }\n\n                                kc.onAuthRefreshError && kc.onAuthRefreshError();\n                                for (var p = refreshQueue.pop(); p != null; p = refreshQueue.pop()) {\n                                    p.setError(\"Failed to refresh token: An unexpected HTTP error occurred while attempting to refresh the token.\");\n                                }\n                            }\n                        }\n                    };\n\n                    req.send(params);\n                }\n            }\n        }\n\n        if (loginIframe.enable) {\n            var iframePromise = checkLoginIframe();\n            iframePromise.then(function() {\n                exec();\n            }).catch(function(error) {\n                promise.setError(error);\n            });\n        } else {\n            exec();\n        }\n\n        return promise.promise;\n    }\n\n    kc.clearToken = function() {\n        if (kc.token) {\n            setToken(null, null, null);\n            kc.onAuthLogout && kc.onAuthLogout();\n            if (kc.loginRequired) {\n                kc.login();\n            }\n        }\n    }\n\n    function getRealmUrl() {\n        if (typeof kc.authServerUrl !== 'undefined') {\n            if (kc.authServerUrl.charAt(kc.authServerUrl.length - 1) == '/') {\n                return kc.authServerUrl + 'realms/' + encodeURIComponent(kc.realm);\n            } else {\n                return kc.authServerUrl + '/realms/' + encodeURIComponent(kc.realm);\n            }\n        } else {\n            return undefined;\n        }\n    }\n\n    function getOrigin() {\n        if (!window.location.origin) {\n            return window.location.protocol + \"//\" + window.location.hostname + (window.location.port ? ':' + window.location.port: '');\n        } else {\n            return window.location.origin;\n        }\n    }\n\n    function processCallback(oauth, promise) {\n        var code = oauth.code;\n        var error = oauth.error;\n        var prompt = oauth.prompt;\n\n        var timeLocal = new Date().getTime();\n\n        if (oauth['kc_action_status']) {\n            kc.onActionUpdate && kc.onActionUpdate(oauth['kc_action_status'], oauth['kc_action']);\n        }\n\n        if (error) {\n            if (prompt != 'none') {\n                if (oauth.error_description && oauth.error_description === \"authentication_expired\") {\n                    kc.login(oauth.loginOptions);\n                } else {\n                    var errorData = { error: error, error_description: oauth.error_description };\n                    kc.onAuthError && kc.onAuthError(errorData);\n                    promise && promise.setError(errorData);\n                }\n            } else {\n                promise && promise.setSuccess();\n            }\n            return;\n        } else if ((kc.flow != 'standard') && (oauth.access_token || oauth.id_token)) {\n            authSuccess(oauth.access_token, null, oauth.id_token, true);\n        }\n\n        if ((kc.flow != 'implicit') && code) {\n            var params = 'code=' + code + '&grant_type=authorization_code';\n            var url = kc.endpoints.token();\n\n            var req = new XMLHttpRequest();\n            req.open('POST', url, true);\n            req.setRequestHeader('Content-type', 'application/x-www-form-urlencoded');\n\n            params += '&client_id=' + encodeURIComponent(kc.clientId);\n            params += '&redirect_uri=' + oauth.redirectUri;\n\n            if (oauth.pkceCodeVerifier) {\n                params += '&code_verifier=' + oauth.pkceCodeVerifier;\n            }\n\n            req.withCredentials = true;\n\n            req.onreadystatechange = function() {\n                if (req.readyState == 4) {\n                    if (req.status == 200) {\n\n                        var tokenResponse = JSON.parse(req.responseText);\n                        authSuccess(tokenResponse['access_token'], tokenResponse['refresh_token'], tokenResponse['id_token'], kc.flow === 'standard');\n                        scheduleCheckIframe();\n                    } else {\n                        kc.onAuthError && kc.onAuthError();\n                        promise && promise.setError();\n                    }\n                }\n            };\n\n            req.send(params);\n        }\n\n        function authSuccess(accessToken, refreshToken, idToken, fulfillPromise) {\n            timeLocal = (timeLocal + new Date().getTime()) / 2;\n\n            setToken(accessToken, refreshToken, idToken, timeLocal);\n\n            if (useNonce && (kc.idTokenParsed && kc.idTokenParsed.nonce != oauth.storedNonce)) {\n                logInfo('[KEYCLOAK] Invalid nonce, clearing token');\n                kc.clearToken();\n                promise && promise.setError();\n            } else {\n                if (fulfillPromise) {\n                    kc.onAuthSuccess && kc.onAuthSuccess();\n                    promise && promise.setSuccess();\n                }\n            }\n        }\n\n    }\n\n    function loadConfig() {\n        var promise = createPromise();\n        var configUrl;\n\n        if (typeof config === 'string') {\n            configUrl = config;\n        }\n\n        function setupOidcEndoints(oidcConfiguration) {\n            if (! oidcConfiguration) {\n                kc.endpoints = {\n                    authorize: function() {\n                        return getRealmUrl() + '/protocol/openid-connect/auth';\n                    },\n                    token: function() {\n                        return getRealmUrl() + '/protocol/openid-connect/token';\n                    },\n                    logout: function() {\n                        return getRealmUrl() + '/protocol/openid-connect/logout';\n                    },\n                    checkSessionIframe: function() {\n                        return getRealmUrl() + '/protocol/openid-connect/login-status-iframe.html';\n                    },\n                    thirdPartyCookiesIframe: function() {\n                        return getRealmUrl() + '/protocol/openid-connect/3p-cookies/step1.html';\n                    },\n                    register: function() {\n                        return getRealmUrl() + '/protocol/openid-connect/registrations';\n                    },\n                    userinfo: function() {\n                        return getRealmUrl() + '/protocol/openid-connect/userinfo';\n                    }\n                };\n            } else {\n                kc.endpoints = {\n                    authorize: function() {\n                        return oidcConfiguration.authorization_endpoint;\n                    },\n                    token: function() {\n                        return oidcConfiguration.token_endpoint;\n                    },\n                    logout: function() {\n                        if (!oidcConfiguration.end_session_endpoint) {\n                            throw \"Not supported by the OIDC server\";\n                        }\n                        return oidcConfiguration.end_session_endpoint;\n                    },\n                    checkSessionIframe: function() {\n                        if (!oidcConfiguration.check_session_iframe) {\n                            throw \"Not supported by the OIDC server\";\n                        }\n                        return oidcConfiguration.check_session_iframe;\n                    },\n                    register: function() {\n                        throw 'Redirection to \"Register user\" page not supported in standard OIDC mode';\n                    },\n                    userinfo: function() {\n                        if (!oidcConfiguration.userinfo_endpoint) {\n                            throw \"Not supported by the OIDC server\";\n                        }\n                        return oidcConfiguration.userinfo_endpoint;\n                    }\n                }\n            }\n        }\n\n        if (configUrl) {\n            var req = new XMLHttpRequest();\n            req.open('GET', configUrl, true);\n            req.setRequestHeader('Accept', 'application/json');\n\n            req.onreadystatechange = function () {\n                if (req.readyState == 4) {\n                    if (req.status == 200 || fileLoaded(req)) {\n                        var config = JSON.parse(req.responseText);\n\n                        kc.authServerUrl = config['auth-server-url'];\n                        kc.realm = config['realm'];\n                        kc.clientId = config['resource'];\n                        setupOidcEndoints(null);\n                        promise.setSuccess();\n                    } else {\n                        promise.setError();\n                    }\n                }\n            };\n\n            req.send();\n        } else {\n            kc.clientId = config.clientId;\n\n            var oidcProvider = config['oidcProvider'];\n            if (!oidcProvider) {\n                kc.authServerUrl = config.url;\n                kc.realm = config.realm;\n                setupOidcEndoints(null);\n                promise.setSuccess();\n            } else {\n                if (typeof oidcProvider === 'string') {\n                    var oidcProviderConfigUrl;\n                    if (oidcProvider.charAt(oidcProvider.length - 1) == '/') {\n                        oidcProviderConfigUrl = oidcProvider + '.well-known/openid-configuration';\n                    } else {\n                        oidcProviderConfigUrl = oidcProvider + '/.well-known/openid-configuration';\n                    }\n                    var req = new XMLHttpRequest();\n                    req.open('GET', oidcProviderConfigUrl, true);\n                    req.setRequestHeader('Accept', 'application/json');\n\n                    req.onreadystatechange = function () {\n                        if (req.readyState == 4) {\n                            if (req.status == 200 || fileLoaded(req)) {\n                                var oidcProviderConfig = JSON.parse(req.responseText);\n                                setupOidcEndoints(oidcProviderConfig);\n                                promise.setSuccess();\n                            } else {\n                                promise.setError();\n                            }\n                        }\n                    };\n\n                    req.send();\n                } else {\n                    setupOidcEndoints(oidcProvider);\n                    promise.setSuccess();\n                }\n            }\n        }\n\n        return promise.promise;\n    }\n\n    function fileLoaded(xhr) {\n        return xhr.status == 0 && xhr.responseText && xhr.responseURL.startsWith('file:');\n    }\n\n    function setToken(token, refreshToken, idToken, timeLocal) {\n        if (kc.tokenTimeoutHandle) {\n            clearTimeout(kc.tokenTimeoutHandle);\n            kc.tokenTimeoutHandle = null;\n        }\n\n        if (refreshToken) {\n            kc.refreshToken = refreshToken;\n            kc.refreshTokenParsed = decodeToken(refreshToken);\n        } else {\n            delete kc.refreshToken;\n            delete kc.refreshTokenParsed;\n        }\n\n        if (idToken) {\n            kc.idToken = idToken;\n            kc.idTokenParsed = decodeToken(idToken);\n        } else {\n            delete kc.idToken;\n            delete kc.idTokenParsed;\n        }\n\n        if (token) {\n            kc.token = token;\n            kc.tokenParsed = decodeToken(token);\n            kc.sessionId = kc.tokenParsed.sid;\n            kc.authenticated = true;\n            kc.subject = kc.tokenParsed.sub;\n            kc.realmAccess = kc.tokenParsed.realm_access;\n            kc.resourceAccess = kc.tokenParsed.resource_access;\n\n            if (timeLocal) {\n                kc.timeSkew = Math.floor(timeLocal / 1000) - kc.tokenParsed.iat;\n            }\n\n            if (kc.timeSkew != null) {\n                logInfo('[KEYCLOAK] Estimated time difference between browser and server is ' + kc.timeSkew + ' seconds');\n\n                if (kc.onTokenExpired) {\n                    var expiresIn = (kc.tokenParsed['exp'] - (new Date().getTime() / 1000) + kc.timeSkew) * 1000;\n                    logInfo('[KEYCLOAK] Token expires in ' + Math.round(expiresIn / 1000) + ' s');\n                    if (expiresIn <= 0) {\n                        kc.onTokenExpired();\n                    } else {\n                        kc.tokenTimeoutHandle = setTimeout(kc.onTokenExpired, expiresIn);\n                    }\n                }\n            }\n        } else {\n            delete kc.token;\n            delete kc.tokenParsed;\n            delete kc.subject;\n            delete kc.realmAccess;\n            delete kc.resourceAccess;\n\n            kc.authenticated = false;\n        }\n    }\n\n    function createUUID() {\n        if (typeof crypto === \"undefined\" || typeof crypto.randomUUID === \"undefined\") {\n            throw new Error(\"Web Crypto API is not available.\");\n        }\n\n        return crypto.randomUUID();\n    }\n\n    function parseCallback(url) {\n        var oauth = parseCallbackUrl(url);\n        if (!oauth) {\n            return;\n        }\n\n        var oauthState = callbackStorage.get(oauth.state);\n\n        if (oauthState) {\n            oauth.valid = true;\n            oauth.redirectUri = oauthState.redirectUri;\n            oauth.storedNonce = oauthState.nonce;\n            oauth.prompt = oauthState.prompt;\n            oauth.pkceCodeVerifier = oauthState.pkceCodeVerifier;\n            oauth.loginOptions = oauthState.loginOptions;\n        }\n\n        return oauth;\n    }\n\n    function parseCallbackUrl(url) {\n        var supportedParams;\n        switch (kc.flow) {\n            case 'standard':\n                supportedParams = ['code', 'state', 'session_state', 'kc_action_status', 'kc_action', 'iss'];\n                break;\n            case 'implicit':\n                supportedParams = ['access_token', 'token_type', 'id_token', 'state', 'session_state', 'expires_in', 'kc_action_status', 'kc_action', 'iss'];\n                break;\n            case 'hybrid':\n                supportedParams = ['access_token', 'token_type', 'id_token', 'code', 'state', 'session_state', 'expires_in', 'kc_action_status', 'kc_action', 'iss'];\n                break;\n        }\n\n        supportedParams.push('error');\n        supportedParams.push('error_description');\n        supportedParams.push('error_uri');\n\n        var queryIndex = url.indexOf('?');\n        var fragmentIndex = url.indexOf('#');\n\n        var newUrl;\n        var parsed;\n\n        if (kc.responseMode === 'query' && queryIndex !== -1) {\n            newUrl = url.substring(0, queryIndex);\n            parsed = parseCallbackParams(url.substring(queryIndex + 1, fragmentIndex !== -1 ? fragmentIndex : url.length), supportedParams);\n            if (parsed.paramsString !== '') {\n                newUrl += '?' + parsed.paramsString;\n            }\n            if (fragmentIndex !== -1) {\n                newUrl += url.substring(fragmentIndex);\n            }\n        } else if (kc.responseMode === 'fragment' && fragmentIndex !== -1) {\n            newUrl = url.substring(0, fragmentIndex);\n            parsed = parseCallbackParams(url.substring(fragmentIndex + 1), supportedParams);\n            if (parsed.paramsString !== '') {\n                newUrl += '#' + parsed.paramsString;\n            }\n        }\n\n        if (parsed && parsed.oauthParams) {\n            if (kc.flow === 'standard' || kc.flow === 'hybrid') {\n                if ((parsed.oauthParams.code || parsed.oauthParams.error) && parsed.oauthParams.state) {\n                    parsed.oauthParams.newUrl = newUrl;\n                    return parsed.oauthParams;\n                }\n            } else if (kc.flow === 'implicit') {\n                if ((parsed.oauthParams.access_token || parsed.oauthParams.error) && parsed.oauthParams.state) {\n                    parsed.oauthParams.newUrl = newUrl;\n                    return parsed.oauthParams;\n                }\n            }\n        }\n    }\n\n    function parseCallbackParams(paramsString, supportedParams) {\n        var p = paramsString.split('&');\n        var result = {\n            paramsString: '',\n            oauthParams: {}\n        }\n        for (var i = 0; i < p.length; i++) {\n            var split = p[i].indexOf(\"=\");\n            var key = p[i].slice(0, split);\n            if (supportedParams.indexOf(key) !== -1) {\n                result.oauthParams[key] = p[i].slice(split + 1);\n            } else {\n                if (result.paramsString !== '') {\n                    result.paramsString += '&';\n                }\n                result.paramsString += p[i];\n            }\n        }\n        return result;\n    }\n\n    function createPromise() {\n        // Need to create a native Promise which also preserves the\n        // interface of the custom promise type previously used by the API\n        var p = {\n            setSuccess: function(result) {\n                p.resolve(result);\n            },\n\n            setError: function(result) {\n                p.reject(result);\n            }\n        };\n        p.promise = new Promise(function(resolve, reject) {\n            p.resolve = resolve;\n            p.reject = reject;\n        });\n\n        return p;\n    }\n\n    // Function to extend existing native Promise with timeout\n    function applyTimeoutToPromise(promise, timeout, errorMessage) {\n        var timeoutHandle = null;\n        var timeoutPromise = new Promise(function (resolve, reject) {\n            timeoutHandle = setTimeout(function () {\n                reject({ \"error\": errorMessage || \"Promise is not settled within timeout of \" + timeout + \"ms\" });\n            }, timeout);\n        });\n\n        return Promise.race([promise, timeoutPromise]).finally(function () {\n            clearTimeout(timeoutHandle);\n        });\n    }\n\n    function setupCheckLoginIframe() {\n        var promise = createPromise();\n\n        if (!loginIframe.enable) {\n            promise.setSuccess();\n            return promise.promise;\n        }\n\n        if (loginIframe.iframe) {\n            promise.setSuccess();\n            return promise.promise;\n        }\n\n        var iframe = document.createElement('iframe');\n        loginIframe.iframe = iframe;\n\n        iframe.onload = function() {\n            var authUrl = kc.endpoints.authorize();\n            if (authUrl.charAt(0) === '/') {\n                loginIframe.iframeOrigin = getOrigin();\n            } else {\n                loginIframe.iframeOrigin = authUrl.substring(0, authUrl.indexOf('/', 8));\n            }\n            promise.setSuccess();\n        }\n\n        var src = kc.endpoints.checkSessionIframe();\n        iframe.setAttribute('src', src );\n        iframe.setAttribute('sandbox', 'allow-storage-access-by-user-activation allow-scripts allow-same-origin');\n        iframe.setAttribute('title', 'keycloak-session-iframe' );\n        iframe.style.display = 'none';\n        document.body.appendChild(iframe);\n\n        var messageCallback = function(event) {\n            if ((event.origin !== loginIframe.iframeOrigin) || (loginIframe.iframe.contentWindow !== event.source)) {\n                return;\n            }\n\n            if (!(event.data == 'unchanged' || event.data == 'changed' || event.data == 'error')) {\n                return;\n            }\n\n\n            if (event.data != 'unchanged') {\n                kc.clearToken();\n            }\n\n            var callbacks = loginIframe.callbackList.splice(0, loginIframe.callbackList.length);\n\n            for (var i = callbacks.length - 1; i >= 0; --i) {\n                var promise = callbacks[i];\n                if (event.data == 'error') {\n                    promise.setError();\n                } else {\n                    promise.setSuccess(event.data == 'unchanged');\n                }\n            }\n        };\n\n        window.addEventListener('message', messageCallback, false);\n\n        return promise.promise;\n    }\n\n    function scheduleCheckIframe() {\n        if (loginIframe.enable) {\n            if (kc.token) {\n                setTimeout(function() {\n                    checkLoginIframe().then(function(unchanged) {\n                        if (unchanged) {\n                            scheduleCheckIframe();\n                        }\n                    });\n                }, loginIframe.interval * 1000);\n            }\n        }\n    }\n\n    function checkLoginIframe() {\n        var promise = createPromise();\n\n        if (loginIframe.iframe && loginIframe.iframeOrigin ) {\n            var msg = kc.clientId + ' ' + (kc.sessionId ? kc.sessionId : '');\n            loginIframe.callbackList.push(promise);\n            var origin = loginIframe.iframeOrigin;\n            if (loginIframe.callbackList.length == 1) {\n                loginIframe.iframe.contentWindow.postMessage(msg, origin);\n            }\n        } else {\n            promise.setSuccess();\n        }\n\n        return promise.promise;\n    }\n\n    function check3pCookiesSupported() {\n        var promise = createPromise();\n\n        if ((loginIframe.enable || kc.silentCheckSsoRedirectUri) && typeof kc.endpoints.thirdPartyCookiesIframe === 'function') {\n            var iframe = document.createElement('iframe');\n            iframe.setAttribute('src', kc.endpoints.thirdPartyCookiesIframe());\n            iframe.setAttribute('sandbox', 'allow-storage-access-by-user-activation allow-scripts allow-same-origin');\n            iframe.setAttribute('title', 'keycloak-3p-check-iframe' );\n            iframe.style.display = 'none';\n            document.body.appendChild(iframe);\n\n            var messageCallback = function(event) {\n                if (iframe.contentWindow !== event.source) {\n                    return;\n                }\n\n                if (event.data !== \"supported\" && event.data !== \"unsupported\") {\n                    return;\n                } else if (event.data === \"unsupported\") {\n                    logWarn(\n                        \"[KEYCLOAK] Your browser is blocking access to 3rd-party cookies, this means:\\n\\n\" +\n                        \" - It is not possible to retrieve tokens without redirecting to the Keycloak server (a.k.a. no support for silent authentication).\\n\" +\n                        \" - It is not possible to automatically detect changes to the session status (such as the user logging out in another tab).\\n\\n\" +\n                        \"For more information see: https://www.keycloak.org/securing-apps/javascript-adapter#_modern_browsers\"\n                    );\n\n                    loginIframe.enable = false;\n                    if (kc.silentCheckSsoFallback) {\n                        kc.silentCheckSsoRedirectUri = false;\n                    }\n                }\n\n                document.body.removeChild(iframe);\n                window.removeEventListener(\"message\", messageCallback);\n                promise.setSuccess();\n            };\n\n            window.addEventListener('message', messageCallback, false);\n        } else {\n            promise.setSuccess();\n        }\n\n        return applyTimeoutToPromise(promise.promise, kc.messageReceiveTimeout, \"Timeout when waiting for 3rd party check iframe message.\");\n    }\n\n    function loadAdapter(type) {\n        if (!type || type == 'default') {\n            return {\n                login: async function(options) {\n                    window.location.assign(await kc.createLoginUrl(options));\n                    return createPromise().promise;\n                },\n\n                logout: async function(options) {\n\n                    const logoutMethod = options?.logoutMethod ?? kc.logoutMethod;\n                    if (logoutMethod === \"GET\") {\n                        window.location.replace(kc.createLogoutUrl(options));\n                        return;\n                    }\n\n                    // Create form to send POST request.\n                    const form = document.createElement(\"form\");\n\n                    form.setAttribute(\"method\", \"POST\");\n                    form.setAttribute(\"action\", kc.createLogoutUrl(options));\n                    form.style.display = \"none\";\n\n                    // Add data to form as hidden input fields.\n                    const data = {\n                        id_token_hint: kc.idToken,\n                        client_id: kc.clientId,\n                        post_logout_redirect_uri: adapter.redirectUri(options, false)\n                    };\n\n                    for (const [name, value] of Object.entries(data)) {\n                        const input = document.createElement(\"input\");\n\n                        input.setAttribute(\"type\", \"hidden\");\n                        input.setAttribute(\"name\", name);\n                        input.setAttribute(\"value\", value);\n\n                        form.appendChild(input);\n                    }\n\n                    // Append form to page and submit it to perform logout and redirect.\n                    document.body.appendChild(form);\n                    form.submit();\n                },\n\n                register: async function(options) {\n                    window.location.assign(await kc.createRegisterUrl(options));\n                    return createPromise().promise;\n                },\n\n                accountManagement : function() {\n                    var accountUrl = kc.createAccountUrl();\n                    if (typeof accountUrl !== 'undefined') {\n                        window.location.href = accountUrl;\n                    } else {\n                        throw \"Not supported by the OIDC server\";\n                    }\n                    return createPromise().promise;\n                },\n\n                redirectUri: function(options, encodeHash) {\n                    if (arguments.length == 1) {\n                        encodeHash = true;\n                    }\n\n                    if (options && options.redirectUri) {\n                        return options.redirectUri;\n                    } else if (kc.redirectUri) {\n                        return kc.redirectUri;\n                    } else {\n                        return location.href;\n                    }\n                }\n            };\n        }\n\n        if (type == 'cordova') {\n            loginIframe.enable = false;\n            var cordovaOpenWindowWrapper = function(loginUrl, target, options) {\n                if (window.cordova && window.cordova.InAppBrowser) {\n                    // Use inappbrowser for IOS and Android if available\n                    return window.cordova.InAppBrowser.open(loginUrl, target, options);\n                } else {\n                    return window.open(loginUrl, target, options);\n                }\n            };\n\n            var shallowCloneCordovaOptions = function (userOptions) {\n                if (userOptions && userOptions.cordovaOptions) {\n                    return Object.keys(userOptions.cordovaOptions).reduce(function (options, optionName) {\n                        options[optionName] = userOptions.cordovaOptions[optionName];\n                        return options;\n                    }, {});\n                } else {\n                    return {};\n                }\n            };\n\n            var formatCordovaOptions = function (cordovaOptions) {\n                return Object.keys(cordovaOptions).reduce(function (options, optionName) {\n                    options.push(optionName+\"=\"+cordovaOptions[optionName]);\n                    return options;\n                }, []).join(\",\");\n            };\n\n            var createCordovaOptions = function (userOptions) {\n                var cordovaOptions = shallowCloneCordovaOptions(userOptions);\n                cordovaOptions.location = 'no';\n                if (userOptions && userOptions.prompt == 'none') {\n                    cordovaOptions.hidden = 'yes';\n                }\n                return formatCordovaOptions(cordovaOptions);\n            };\n\n            var getCordovaRedirectUri = function() {\n                return kc.redirectUri || 'http://localhost';\n            }\n\n            return {\n                login: async function(options) {\n                    var promise = createPromise();\n\n                    var cordovaOptions = createCordovaOptions(options);\n                    var loginUrl = await kc.createLoginUrl(options);\n                    var ref = cordovaOpenWindowWrapper(loginUrl, '_blank', cordovaOptions);\n                    var completed = false;\n\n                    var closed = false;\n                    var closeBrowser = function() {\n                        closed = true;\n                        ref.close();\n                    };\n\n                    ref.addEventListener('loadstart', function(event) {\n                        if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n                            var callback = parseCallback(event.url);\n                            processCallback(callback, promise);\n                            closeBrowser();\n                            completed = true;\n                        }\n                    });\n\n                    ref.addEventListener('loaderror', function(event) {\n                        if (!completed) {\n                            if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n                                var callback = parseCallback(event.url);\n                                processCallback(callback, promise);\n                                closeBrowser();\n                                completed = true;\n                            } else {\n                                promise.setError();\n                                closeBrowser();\n                            }\n                        }\n                    });\n\n                    ref.addEventListener('exit', function(event) {\n                        if (!closed) {\n                            promise.setError({\n                                reason: \"closed_by_user\"\n                            });\n                        }\n                    });\n\n                    return promise.promise;\n                },\n\n                logout: function(options) {\n                    var promise = createPromise();\n\n                    var logoutUrl = kc.createLogoutUrl(options);\n                    var ref = cordovaOpenWindowWrapper(logoutUrl, '_blank', 'location=no,hidden=yes,clearcache=yes');\n\n                    var error;\n\n                    ref.addEventListener('loadstart', function(event) {\n                        if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n                            ref.close();\n                        }\n                    });\n\n                    ref.addEventListener('loaderror', function(event) {\n                        if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n                            ref.close();\n                        } else {\n                            error = true;\n                            ref.close();\n                        }\n                    });\n\n                    ref.addEventListener('exit', function(event) {\n                        if (error) {\n                            promise.setError();\n                        } else {\n                            kc.clearToken();\n                            promise.setSuccess();\n                        }\n                    });\n\n                    return promise.promise;\n                },\n\n                register : async function(options) {\n                    var promise = createPromise();\n                    var registerUrl = await kc.createRegisterUrl();\n                    var cordovaOptions = createCordovaOptions(options);\n                    var ref = cordovaOpenWindowWrapper(registerUrl, '_blank', cordovaOptions);\n                    ref.addEventListener('loadstart', function(event) {\n                        if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n                            ref.close();\n                            var oauth = parseCallback(event.url);\n                            processCallback(oauth, promise);\n                        }\n                    });\n                    return promise.promise;\n                },\n\n                accountManagement : function() {\n                    var accountUrl = kc.createAccountUrl();\n                    if (typeof accountUrl !== 'undefined') {\n                        var ref = cordovaOpenWindowWrapper(accountUrl, '_blank', 'location=no');\n                        ref.addEventListener('loadstart', function(event) {\n                            if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n                                ref.close();\n                            }\n                        });\n                    } else {\n                        throw \"Not supported by the OIDC server\";\n                    }\n                },\n\n                redirectUri: function(options) {\n                    return getCordovaRedirectUri();\n                }\n            }\n        }\n\n        if (type == 'cordova-native') {\n            loginIframe.enable = false;\n\n            return {\n                login: async function(options) {\n                    var promise = createPromise();\n                    var loginUrl = await kc.createLoginUrl(options);\n\n                    universalLinks.subscribe('keycloak', function(event) {\n                        universalLinks.unsubscribe('keycloak');\n                        window.cordova.plugins.browsertab.close();\n                        var oauth = parseCallback(event.url);\n                        processCallback(oauth, promise);\n                    });\n\n                    window.cordova.plugins.browsertab.openUrl(loginUrl);\n                    return promise.promise;\n                },\n\n                logout: function(options) {\n                    var promise = createPromise();\n                    var logoutUrl = kc.createLogoutUrl(options);\n\n                    universalLinks.subscribe('keycloak', function(event) {\n                        universalLinks.unsubscribe('keycloak');\n                        window.cordova.plugins.browsertab.close();\n                        kc.clearToken();\n                        promise.setSuccess();\n                    });\n\n                    window.cordova.plugins.browsertab.openUrl(logoutUrl);\n                    return promise.promise;\n                },\n\n                register : async function(options) {\n                    var promise = createPromise();\n                    var registerUrl = await kc.createRegisterUrl(options);\n                    universalLinks.subscribe('keycloak' , function(event) {\n                        universalLinks.unsubscribe('keycloak');\n                        window.cordova.plugins.browsertab.close();\n                        var oauth = parseCallback(event.url);\n                        processCallback(oauth, promise);\n                    });\n                    window.cordova.plugins.browsertab.openUrl(registerUrl);\n                    return promise.promise;\n\n                },\n\n                accountManagement : function() {\n                    var accountUrl = kc.createAccountUrl();\n                    if (typeof accountUrl !== 'undefined') {\n                        window.cordova.plugins.browsertab.openUrl(accountUrl);\n                    } else {\n                        throw \"Not supported by the OIDC server\";\n                    }\n                },\n\n                redirectUri: function(options) {\n                    if (options && options.redirectUri) {\n                        return options.redirectUri;\n                    } else if (kc.redirectUri) {\n                        return kc.redirectUri;\n                    } else {\n                        return \"http://localhost\";\n                    }\n                }\n            }\n        }\n\n        throw 'invalid adapter type: ' + type;\n    }\n\n    const STORAGE_KEY_PREFIX = 'kc-callback-';\n\n    var LocalStorage = function() {\n        if (!(this instanceof LocalStorage)) {\n            return new LocalStorage();\n        }\n\n        localStorage.setItem('kc-test', 'test');\n        localStorage.removeItem('kc-test');\n\n        var cs = this;\n\n        /**\n         * Clears all values from local storage that are no longer valid.\n         */\n        function clearInvalidValues() {\n            const currentTime = Date.now();\n\n            for (const [key, value] of getStoredEntries()) {\n                // Attempt to parse the expiry time from the value.\n                const expiry = parseExpiry(value);\n\n                // Discard the value if it is malformed or expired.\n                if (expiry === null || expiry < currentTime) {\n                    localStorage.removeItem(key);\n                }\n            }\n        }\n\n        /**\n         * Clears all known values from local storage.\n         */\n        function clearAllValues() {\n            for (const [key] of getStoredEntries()) {\n                localStorage.removeItem(key);\n            }\n        }\n\n        /**\n         * Gets all entries stored in local storage that are known to be managed by this class.\n         * @returns {Array<[string, unknown]>} An array of key-value pairs.\n         */\n        function getStoredEntries() {\n            return Object.entries(localStorage).filter(([key]) => key.startsWith(STORAGE_KEY_PREFIX));\n        }\n\n        /**\n         * Parses the expiry time from a value stored in local storage.\n         * @param {unknown} value\n         * @returns {number | null} The expiry time in milliseconds, or `null` if the value is malformed.\n         */\n        function parseExpiry(value) {\n            let parsedValue;\n\n            // Attempt to parse the value as JSON.\n            try {\n                parsedValue = JSON.parse(value);\n            } catch (error) {\n                return null;\n            }\n\n            // Attempt to extract the 'expires' property.\n            if (isObject(parsedValue) && 'expires' in parsedValue && typeof parsedValue.expires === 'number') {\n                return parsedValue.expires;\n            }\n\n            return null;\n        }\n\n        cs.get = function(state) {\n            if (!state) {\n                return;\n            }\n\n            var key = STORAGE_KEY_PREFIX + state;\n            var value = localStorage.getItem(key);\n            if (value) {\n                localStorage.removeItem(key);\n                value = JSON.parse(value);\n            }\n\n            clearInvalidValues();\n            return value;\n        };\n\n        cs.add = function(state) {\n            clearInvalidValues();\n\n            const key = STORAGE_KEY_PREFIX + state.state;\n            const value = JSON.stringify({\n                ...state,\n                // Set the expiry time to 1 hour from now.\n                expires: Date.now() + (60 * 60 * 1000)\n            });\n\n            try {\n                localStorage.setItem(key, value);\n            } catch (error) {\n                // If the storage is full, clear all known values and try again.\n                clearAllValues();\n                localStorage.setItem(key, value);\n            }\n        };\n    };\n\n    var CookieStorage = function() {\n        if (!(this instanceof CookieStorage)) {\n            return new CookieStorage();\n        }\n\n        var cs = this;\n\n        cs.get = function(state) {\n            if (!state) {\n                return;\n            }\n\n            var value = getCookie(STORAGE_KEY_PREFIX + state);\n            setCookie(STORAGE_KEY_PREFIX + state, '', cookieExpiration(-100));\n            if (value) {\n                return JSON.parse(value);\n            }\n        };\n\n        cs.add = function(state) {\n            setCookie(STORAGE_KEY_PREFIX + state.state, JSON.stringify(state), cookieExpiration(60));\n        };\n\n        cs.removeItem = function(key) {\n            setCookie(key, '', cookieExpiration(-100));\n        };\n\n        var cookieExpiration = function (minutes) {\n            var exp = new Date();\n            exp.setTime(exp.getTime() + (minutes*60*1000));\n            return exp;\n        };\n\n        var getCookie = function (key) {\n            var name = key + '=';\n            var ca = document.cookie.split(';');\n            for (var i = 0; i < ca.length; i++) {\n                var c = ca[i];\n                while (c.charAt(0) == ' ') {\n                    c = c.substring(1);\n                }\n                if (c.indexOf(name) == 0) {\n                    return c.substring(name.length, c.length);\n                }\n            }\n            return '';\n        };\n\n        var setCookie = function (key, value, expirationDate) {\n            var cookie = key + '=' + value + '; '\n                + 'expires=' + expirationDate.toUTCString() + '; ';\n            document.cookie = cookie;\n        }\n    };\n\n    function createCallbackStorage() {\n        try {\n            return new LocalStorage();\n        } catch (err) {\n        }\n\n        return new CookieStorage();\n    }\n\n    function createLogger(fn) {\n        return function() {\n            if (kc.enableLogging) {\n                fn.apply(console, Array.prototype.slice.call(arguments));\n            }\n        };\n    }\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Keycloak);\n\n/**\n * @param {ArrayBuffer} bytes\n * @see https://developer.mozilla.org/en-US/docs/Glossary/Base64#the_unicode_problem\n */\nfunction bytesToBase64(bytes) {\n    const binString = String.fromCodePoint(...bytes);\n    return btoa(binString);\n}\n\n/**\n * @param {string} message\n * @see https://developer.mozilla.org/en-US/docs/Web/API/SubtleCrypto/digest#basic_example\n */\nasync function sha256Digest(message) {\n    const encoder = new TextEncoder();\n    const data = encoder.encode(message);\n\n    if (typeof crypto === \"undefined\" || typeof crypto.subtle === \"undefined\") {\n        throw new Error(\"Web Crypto API is not available.\");\n    }\n\n    return await crypto.subtle.digest(\"SHA-256\", data);\n}\n\n/**\n * @param {string} token\n */\nfunction decodeToken(token) {\n    const [header, payload] = token.split(\".\");\n\n    if (typeof payload !== \"string\") {\n        throw new Error(\"Unable to decode token, payload not found.\");\n    }\n\n    let decoded;\n\n    try {\n        decoded = base64UrlDecode(payload);\n    } catch (error) {\n        throw new Error(\"Unable to decode token, payload is not a valid Base64URL value.\", { cause: error });\n    }\n\n    try {\n        return JSON.parse(decoded);\n    } catch (error) {\n        throw new Error(\"Unable to decode token, payload is not a valid JSON value.\", { cause: error });\n    }\n}\n\n/**\n * @param {string} input\n */\nfunction base64UrlDecode(input) {\n    let output = input\n        .replaceAll(\"-\", \"+\")\n        .replaceAll(\"_\", \"/\");\n\n    switch (output.length % 4) {\n        case 0:\n            break;\n        case 2:\n            output += \"==\";\n            break;\n        case 3:\n            output += \"=\";\n            break;\n        default:\n            throw new Error(\"Input is not of the correct length.\");\n    }\n\n    try {\n        return b64DecodeUnicode(output);\n    } catch (error) {\n        return atob(output);\n    }\n}\n\n/**\n * @param {string} input\n */\nfunction b64DecodeUnicode(input) {\n    return decodeURIComponent(atob(input).replace(/(.)/g, (m, p) => {\n        let code = p.charCodeAt(0).toString(16).toUpperCase();\n\n        if (code.length < 2) {\n            code = \"0\" + code;\n        }\n\n        return \"%\" + code;\n    }));\n}\n\n/**\n * Check if the input is an object that can be operated on.\n * @param {unknown} input\n */\nfunction isObject(input) {\n    return typeof input === 'object' && input !== null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/keycloak-js/lib/keycloak.js\n");

/***/ })

};
;