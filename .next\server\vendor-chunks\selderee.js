"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/selderee";
exports.ids = ["vendor-chunks/selderee"];
exports.modules = {

/***/ "(rsc)/./node_modules/selderee/lib/selderee.cjs":
/*!************************************************!*\
  !*** ./node_modules/selderee/lib/selderee.cjs ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar parseley = __webpack_require__(/*! parseley */ \"(rsc)/./node_modules/parseley/lib/parseley.cjs\");\n\nfunction _interopNamespace(e) {\n    if (e && e.__esModule) return e;\n    var n = Object.create(null);\n    if (e) {\n        Object.keys(e).forEach(function (k) {\n            if (k !== 'default') {\n                var d = Object.getOwnPropertyDescriptor(e, k);\n                Object.defineProperty(n, k, d.get ? d : {\n                    enumerable: true,\n                    get: function () { return e[k]; }\n                });\n            }\n        });\n    }\n    n[\"default\"] = e;\n    return Object.freeze(n);\n}\n\nvar parseley__namespace = /*#__PURE__*/_interopNamespace(parseley);\n\nvar Ast = /*#__PURE__*/Object.freeze({\n    __proto__: null\n});\n\nvar Types = /*#__PURE__*/Object.freeze({\n    __proto__: null\n});\n\nconst treeify = (nodes) => '▽\\n' + treeifyArray(nodes, thinLines);\nconst thinLines = [['├─', '│ '], ['└─', '  ']];\nconst heavyLines = [['┠─', '┃ '], ['┖─', '  ']];\nconst doubleLines = [['╟─', '║ '], ['╙─', '  ']];\nfunction treeifyArray(nodes, tpl = heavyLines) {\n    return prefixItems(tpl, nodes.map(n => treeifyNode(n)));\n}\nfunction treeifyNode(node) {\n    switch (node.type) {\n        case 'terminal': {\n            const vctr = node.valueContainer;\n            return `◁ #${vctr.index} ${JSON.stringify(vctr.specificity)} ${vctr.value}`;\n        }\n        case 'tagName':\n            return `◻ Tag name\\n${treeifyArray(node.variants, doubleLines)}`;\n        case 'attrValue':\n            return `▣ Attr value: ${node.name}\\n${treeifyArray(node.matchers, doubleLines)}`;\n        case 'attrPresence':\n            return `◨ Attr presence: ${node.name}\\n${treeifyArray(node.cont)}`;\n        case 'pushElement':\n            return `◉ Push element: ${node.combinator}\\n${treeifyArray(node.cont, thinLines)}`;\n        case 'popElement':\n            return `◌ Pop element\\n${treeifyArray(node.cont, thinLines)}`;\n        case 'variant':\n            return `◇ = ${node.value}\\n${treeifyArray(node.cont)}`;\n        case 'matcher':\n            return `◈ ${node.matcher} \"${node.value}\"${node.modifier || ''}\\n${treeifyArray(node.cont)}`;\n    }\n}\nfunction prefixItems(tpl, items) {\n    return items\n        .map((item, i, { length }) => prefixItem(tpl, item, i === length - 1))\n        .join('\\n');\n}\nfunction prefixItem(tpl, item, tail = true) {\n    const tpl1 = tpl[tail ? 1 : 0];\n    return tpl1[0] + item.split('\\n').join('\\n' + tpl1[1]);\n}\n\nvar TreeifyBuilder = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    treeify: treeify\n});\n\nclass DecisionTree {\n    constructor(input) {\n        this.branches = weave(toAstTerminalPairs(input));\n    }\n    build(builder) {\n        return builder(this.branches);\n    }\n}\nfunction toAstTerminalPairs(array) {\n    const len = array.length;\n    const results = new Array(len);\n    for (let i = 0; i < len; i++) {\n        const [selectorString, val] = array[i];\n        const ast = preprocess(parseley__namespace.parse1(selectorString));\n        results[i] = {\n            ast: ast,\n            terminal: {\n                type: 'terminal',\n                valueContainer: { index: i, value: val, specificity: ast.specificity }\n            }\n        };\n    }\n    return results;\n}\nfunction preprocess(ast) {\n    reduceSelectorVariants(ast);\n    parseley__namespace.normalize(ast);\n    return ast;\n}\nfunction reduceSelectorVariants(ast) {\n    const newList = [];\n    ast.list.forEach(sel => {\n        switch (sel.type) {\n            case 'class':\n                newList.push({\n                    matcher: '~=',\n                    modifier: null,\n                    name: 'class',\n                    namespace: null,\n                    specificity: sel.specificity,\n                    type: 'attrValue',\n                    value: sel.name,\n                });\n                break;\n            case 'id':\n                newList.push({\n                    matcher: '=',\n                    modifier: null,\n                    name: 'id',\n                    namespace: null,\n                    specificity: sel.specificity,\n                    type: 'attrValue',\n                    value: sel.name,\n                });\n                break;\n            case 'combinator':\n                reduceSelectorVariants(sel.left);\n                newList.push(sel);\n                break;\n            case 'universal':\n                break;\n            default:\n                newList.push(sel);\n                break;\n        }\n    });\n    ast.list = newList;\n}\nfunction weave(items) {\n    const branches = [];\n    while (items.length) {\n        const topKind = findTopKey(items, (sel) => true, getSelectorKind);\n        const { matches, nonmatches, empty } = breakByKind(items, topKind);\n        items = nonmatches;\n        if (matches.length) {\n            branches.push(branchOfKind(topKind, matches));\n        }\n        if (empty.length) {\n            branches.push(...terminate(empty));\n        }\n    }\n    return branches;\n}\nfunction terminate(items) {\n    const results = [];\n    for (const item of items) {\n        const terminal = item.terminal;\n        if (terminal.type === 'terminal') {\n            results.push(terminal);\n        }\n        else {\n            const { matches, rest } = partition(terminal.cont, (node) => node.type === 'terminal');\n            matches.forEach((node) => results.push(node));\n            if (rest.length) {\n                terminal.cont = rest;\n                results.push(terminal);\n            }\n        }\n    }\n    return results;\n}\nfunction breakByKind(items, selectedKind) {\n    const matches = [];\n    const nonmatches = [];\n    const empty = [];\n    for (const item of items) {\n        const simpsels = item.ast.list;\n        if (simpsels.length) {\n            const isMatch = simpsels.some(node => getSelectorKind(node) === selectedKind);\n            (isMatch ? matches : nonmatches).push(item);\n        }\n        else {\n            empty.push(item);\n        }\n    }\n    return { matches, nonmatches, empty };\n}\nfunction getSelectorKind(sel) {\n    switch (sel.type) {\n        case 'attrPresence':\n            return `attrPresence ${sel.name}`;\n        case 'attrValue':\n            return `attrValue ${sel.name}`;\n        case 'combinator':\n            return `combinator ${sel.combinator}`;\n        default:\n            return sel.type;\n    }\n}\nfunction branchOfKind(kind, items) {\n    if (kind === 'tag') {\n        return tagNameBranch(items);\n    }\n    if (kind.startsWith('attrValue ')) {\n        return attrValueBranch(kind.substring(10), items);\n    }\n    if (kind.startsWith('attrPresence ')) {\n        return attrPresenceBranch(kind.substring(13), items);\n    }\n    if (kind === 'combinator >') {\n        return combinatorBranch('>', items);\n    }\n    if (kind === 'combinator +') {\n        return combinatorBranch('+', items);\n    }\n    throw new Error(`Unsupported selector kind: ${kind}`);\n}\nfunction tagNameBranch(items) {\n    const groups = spliceAndGroup(items, (x) => x.type === 'tag', (x) => x.name);\n    const variants = Object.entries(groups).map(([name, group]) => ({\n        type: 'variant',\n        value: name,\n        cont: weave(group.items)\n    }));\n    return {\n        type: 'tagName',\n        variants: variants\n    };\n}\nfunction attrPresenceBranch(name, items) {\n    for (const item of items) {\n        spliceSimpleSelector(item, (x) => (x.type === 'attrPresence') && (x.name === name));\n    }\n    return {\n        type: 'attrPresence',\n        name: name,\n        cont: weave(items)\n    };\n}\nfunction attrValueBranch(name, items) {\n    const groups = spliceAndGroup(items, (x) => (x.type === 'attrValue') && (x.name === name), (x) => `${x.matcher} ${x.modifier || ''} ${x.value}`);\n    const matchers = [];\n    for (const group of Object.values(groups)) {\n        const sel = group.oneSimpleSelector;\n        const predicate = getAttrPredicate(sel);\n        const continuation = weave(group.items);\n        matchers.push({\n            type: 'matcher',\n            matcher: sel.matcher,\n            modifier: sel.modifier,\n            value: sel.value,\n            predicate: predicate,\n            cont: continuation\n        });\n    }\n    return {\n        type: 'attrValue',\n        name: name,\n        matchers: matchers\n    };\n}\nfunction getAttrPredicate(sel) {\n    if (sel.modifier === 'i') {\n        const expected = sel.value.toLowerCase();\n        switch (sel.matcher) {\n            case '=':\n                return (actual) => expected === actual.toLowerCase();\n            case '~=':\n                return (actual) => actual.toLowerCase().split(/[ \\t]+/).includes(expected);\n            case '^=':\n                return (actual) => actual.toLowerCase().startsWith(expected);\n            case '$=':\n                return (actual) => actual.toLowerCase().endsWith(expected);\n            case '*=':\n                return (actual) => actual.toLowerCase().includes(expected);\n            case '|=':\n                return (actual) => {\n                    const lower = actual.toLowerCase();\n                    return (expected === lower) || (lower.startsWith(expected) && lower[expected.length] === '-');\n                };\n        }\n    }\n    else {\n        const expected = sel.value;\n        switch (sel.matcher) {\n            case '=':\n                return (actual) => expected === actual;\n            case '~=':\n                return (actual) => actual.split(/[ \\t]+/).includes(expected);\n            case '^=':\n                return (actual) => actual.startsWith(expected);\n            case '$=':\n                return (actual) => actual.endsWith(expected);\n            case '*=':\n                return (actual) => actual.includes(expected);\n            case '|=':\n                return (actual) => (expected === actual) || (actual.startsWith(expected) && actual[expected.length] === '-');\n        }\n    }\n}\nfunction combinatorBranch(combinator, items) {\n    const groups = spliceAndGroup(items, (x) => (x.type === 'combinator') && (x.combinator === combinator), (x) => parseley__namespace.serialize(x.left));\n    const leftItems = [];\n    for (const group of Object.values(groups)) {\n        const rightCont = weave(group.items);\n        const leftAst = group.oneSimpleSelector.left;\n        leftItems.push({\n            ast: leftAst,\n            terminal: { type: 'popElement', cont: rightCont }\n        });\n    }\n    return {\n        type: 'pushElement',\n        combinator: combinator,\n        cont: weave(leftItems)\n    };\n}\nfunction spliceAndGroup(items, predicate, keyCallback) {\n    const groups = {};\n    while (items.length) {\n        const bestKey = findTopKey(items, predicate, keyCallback);\n        const bestKeyPredicate = (sel) => predicate(sel) && keyCallback(sel) === bestKey;\n        const hasBestKeyPredicate = (item) => item.ast.list.some(bestKeyPredicate);\n        const { matches, rest } = partition1(items, hasBestKeyPredicate);\n        let oneSimpleSelector = null;\n        for (const item of matches) {\n            const splicedNode = spliceSimpleSelector(item, bestKeyPredicate);\n            if (!oneSimpleSelector) {\n                oneSimpleSelector = splicedNode;\n            }\n        }\n        if (oneSimpleSelector == null) {\n            throw new Error('No simple selector is found.');\n        }\n        groups[bestKey] = { oneSimpleSelector: oneSimpleSelector, items: matches };\n        items = rest;\n    }\n    return groups;\n}\nfunction spliceSimpleSelector(item, predicate) {\n    const simpsels = item.ast.list;\n    const matches = new Array(simpsels.length);\n    let firstIndex = -1;\n    for (let i = simpsels.length; i-- > 0;) {\n        if (predicate(simpsels[i])) {\n            matches[i] = true;\n            firstIndex = i;\n        }\n    }\n    if (firstIndex == -1) {\n        throw new Error(`Couldn't find the required simple selector.`);\n    }\n    const result = simpsels[firstIndex];\n    item.ast.list = simpsels.filter((sel, i) => !matches[i]);\n    return result;\n}\nfunction findTopKey(items, predicate, keyCallback) {\n    const candidates = {};\n    for (const item of items) {\n        const candidates1 = {};\n        for (const node of item.ast.list.filter(predicate)) {\n            candidates1[keyCallback(node)] = true;\n        }\n        for (const key of Object.keys(candidates1)) {\n            if (candidates[key]) {\n                candidates[key]++;\n            }\n            else {\n                candidates[key] = 1;\n            }\n        }\n    }\n    let topKind = '';\n    let topCounter = 0;\n    for (const entry of Object.entries(candidates)) {\n        if (entry[1] > topCounter) {\n            topKind = entry[0];\n            topCounter = entry[1];\n        }\n    }\n    return topKind;\n}\nfunction partition(src, predicate) {\n    const matches = [];\n    const rest = [];\n    for (const x of src) {\n        if (predicate(x)) {\n            matches.push(x);\n        }\n        else {\n            rest.push(x);\n        }\n    }\n    return { matches, rest };\n}\nfunction partition1(src, predicate) {\n    const matches = [];\n    const rest = [];\n    for (const x of src) {\n        if (predicate(x)) {\n            matches.push(x);\n        }\n        else {\n            rest.push(x);\n        }\n    }\n    return { matches, rest };\n}\n\nclass Picker {\n    constructor(f) {\n        this.f = f;\n    }\n    pickAll(el) {\n        return this.f(el);\n    }\n    pick1(el, preferFirst = false) {\n        const results = this.f(el);\n        const len = results.length;\n        if (len === 0) {\n            return null;\n        }\n        if (len === 1) {\n            return results[0].value;\n        }\n        const comparator = (preferFirst)\n            ? comparatorPreferFirst\n            : comparatorPreferLast;\n        let result = results[0];\n        for (let i = 1; i < len; i++) {\n            const next = results[i];\n            if (comparator(result, next)) {\n                result = next;\n            }\n        }\n        return result.value;\n    }\n}\nfunction comparatorPreferFirst(acc, next) {\n    const diff = parseley.compareSpecificity(next.specificity, acc.specificity);\n    return diff > 0 || (diff === 0 && next.index < acc.index);\n}\nfunction comparatorPreferLast(acc, next) {\n    const diff = parseley.compareSpecificity(next.specificity, acc.specificity);\n    return diff > 0 || (diff === 0 && next.index > acc.index);\n}\n\nexports.Ast = Ast;\nexports.DecisionTree = DecisionTree;\nexports.Picker = Picker;\nexports.Treeify = TreeifyBuilder;\nexports.Types = Types;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/selderee/lib/selderee.cjs\n");

/***/ })

};
;