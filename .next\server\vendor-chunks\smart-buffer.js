"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/smart-buffer";
exports.ids = ["vendor-chunks/smart-buffer"];
exports.modules = {

/***/ "(rsc)/./node_modules/smart-buffer/build/smartbuffer.js":
/*!********************************************************!*\
  !*** ./node_modules/smart-buffer/build/smartbuffer.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(rsc)/./node_modules/smart-buffer/build/utils.js\");\n// The default Buffer size if one is not provided.\nconst DEFAULT_SMARTBUFFER_SIZE = 4096;\n// The default string encoding to use for reading/writing strings.\nconst DEFAULT_SMARTBUFFER_ENCODING = 'utf8';\nclass SmartBuffer {\n    /**\n     * Creates a new SmartBuffer instance.\n     *\n     * @param options { SmartBufferOptions } The SmartBufferOptions to apply to this instance.\n     */\n    constructor(options) {\n        this.length = 0;\n        this._encoding = DEFAULT_SMARTBUFFER_ENCODING;\n        this._writeOffset = 0;\n        this._readOffset = 0;\n        if (SmartBuffer.isSmartBufferOptions(options)) {\n            // Checks for encoding\n            if (options.encoding) {\n                utils_1.checkEncoding(options.encoding);\n                this._encoding = options.encoding;\n            }\n            // Checks for initial size length\n            if (options.size) {\n                if (utils_1.isFiniteInteger(options.size) && options.size > 0) {\n                    this._buff = Buffer.allocUnsafe(options.size);\n                }\n                else {\n                    throw new Error(utils_1.ERRORS.INVALID_SMARTBUFFER_SIZE);\n                }\n                // Check for initial Buffer\n            }\n            else if (options.buff) {\n                if (Buffer.isBuffer(options.buff)) {\n                    this._buff = options.buff;\n                    this.length = options.buff.length;\n                }\n                else {\n                    throw new Error(utils_1.ERRORS.INVALID_SMARTBUFFER_BUFFER);\n                }\n            }\n            else {\n                this._buff = Buffer.allocUnsafe(DEFAULT_SMARTBUFFER_SIZE);\n            }\n        }\n        else {\n            // If something was passed but it's not a SmartBufferOptions object\n            if (typeof options !== 'undefined') {\n                throw new Error(utils_1.ERRORS.INVALID_SMARTBUFFER_OBJECT);\n            }\n            // Otherwise default to sane options\n            this._buff = Buffer.allocUnsafe(DEFAULT_SMARTBUFFER_SIZE);\n        }\n    }\n    /**\n     * Creates a new SmartBuffer instance with the provided internal Buffer size and optional encoding.\n     *\n     * @param size { Number } The size of the internal Buffer.\n     * @param encoding { String } The BufferEncoding to use for strings.\n     *\n     * @return { SmartBuffer }\n     */\n    static fromSize(size, encoding) {\n        return new this({\n            size: size,\n            encoding: encoding\n        });\n    }\n    /**\n     * Creates a new SmartBuffer instance with the provided Buffer and optional encoding.\n     *\n     * @param buffer { Buffer } The Buffer to use as the internal Buffer value.\n     * @param encoding { String } The BufferEncoding to use for strings.\n     *\n     * @return { SmartBuffer }\n     */\n    static fromBuffer(buff, encoding) {\n        return new this({\n            buff: buff,\n            encoding: encoding\n        });\n    }\n    /**\n     * Creates a new SmartBuffer instance with the provided SmartBufferOptions options.\n     *\n     * @param options { SmartBufferOptions } The options to use when creating the SmartBuffer instance.\n     */\n    static fromOptions(options) {\n        return new this(options);\n    }\n    /**\n     * Type checking function that determines if an object is a SmartBufferOptions object.\n     */\n    static isSmartBufferOptions(options) {\n        const castOptions = options;\n        return (castOptions &&\n            (castOptions.encoding !== undefined || castOptions.size !== undefined || castOptions.buff !== undefined));\n    }\n    // Signed integers\n    /**\n     * Reads an Int8 value from the current read position or an optionally provided offset.\n     *\n     * @param offset { Number } The offset to read data from (optional)\n     * @return { Number }\n     */\n    readInt8(offset) {\n        return this._readNumberValue(Buffer.prototype.readInt8, 1, offset);\n    }\n    /**\n     * Reads an Int16BE value from the current read position or an optionally provided offset.\n     *\n     * @param offset { Number } The offset to read data from (optional)\n     * @return { Number }\n     */\n    readInt16BE(offset) {\n        return this._readNumberValue(Buffer.prototype.readInt16BE, 2, offset);\n    }\n    /**\n     * Reads an Int16LE value from the current read position or an optionally provided offset.\n     *\n     * @param offset { Number } The offset to read data from (optional)\n     * @return { Number }\n     */\n    readInt16LE(offset) {\n        return this._readNumberValue(Buffer.prototype.readInt16LE, 2, offset);\n    }\n    /**\n     * Reads an Int32BE value from the current read position or an optionally provided offset.\n     *\n     * @param offset { Number } The offset to read data from (optional)\n     * @return { Number }\n     */\n    readInt32BE(offset) {\n        return this._readNumberValue(Buffer.prototype.readInt32BE, 4, offset);\n    }\n    /**\n     * Reads an Int32LE value from the current read position or an optionally provided offset.\n     *\n     * @param offset { Number } The offset to read data from (optional)\n     * @return { Number }\n     */\n    readInt32LE(offset) {\n        return this._readNumberValue(Buffer.prototype.readInt32LE, 4, offset);\n    }\n    /**\n     * Reads a BigInt64BE value from the current read position or an optionally provided offset.\n     *\n     * @param offset { Number } The offset to read data from (optional)\n     * @return { BigInt }\n     */\n    readBigInt64BE(offset) {\n        utils_1.bigIntAndBufferInt64Check('readBigInt64BE');\n        return this._readNumberValue(Buffer.prototype.readBigInt64BE, 8, offset);\n    }\n    /**\n     * Reads a BigInt64LE value from the current read position or an optionally provided offset.\n     *\n     * @param offset { Number } The offset to read data from (optional)\n     * @return { BigInt }\n     */\n    readBigInt64LE(offset) {\n        utils_1.bigIntAndBufferInt64Check('readBigInt64LE');\n        return this._readNumberValue(Buffer.prototype.readBigInt64LE, 8, offset);\n    }\n    /**\n     * Writes an Int8 value to the current write position (or at optional offset).\n     *\n     * @param value { Number } The value to write.\n     * @param offset { Number } The offset to write the value at.\n     *\n     * @return this\n     */\n    writeInt8(value, offset) {\n        this._writeNumberValue(Buffer.prototype.writeInt8, 1, value, offset);\n        return this;\n    }\n    /**\n     * Inserts an Int8 value at the given offset value.\n     *\n     * @param value { Number } The value to insert.\n     * @param offset { Number } The offset to insert the value at.\n     *\n     * @return this\n     */\n    insertInt8(value, offset) {\n        return this._insertNumberValue(Buffer.prototype.writeInt8, 1, value, offset);\n    }\n    /**\n     * Writes an Int16BE value to the current write position (or at optional offset).\n     *\n     * @param value { Number } The value to write.\n     * @param offset { Number } The offset to write the value at.\n     *\n     * @return this\n     */\n    writeInt16BE(value, offset) {\n        return this._writeNumberValue(Buffer.prototype.writeInt16BE, 2, value, offset);\n    }\n    /**\n     * Inserts an Int16BE value at the given offset value.\n     *\n     * @param value { Number } The value to insert.\n     * @param offset { Number } The offset to insert the value at.\n     *\n     * @return this\n     */\n    insertInt16BE(value, offset) {\n        return this._insertNumberValue(Buffer.prototype.writeInt16BE, 2, value, offset);\n    }\n    /**\n     * Writes an Int16LE value to the current write position (or at optional offset).\n     *\n     * @param value { Number } The value to write.\n     * @param offset { Number } The offset to write the value at.\n     *\n     * @return this\n     */\n    writeInt16LE(value, offset) {\n        return this._writeNumberValue(Buffer.prototype.writeInt16LE, 2, value, offset);\n    }\n    /**\n     * Inserts an Int16LE value at the given offset value.\n     *\n     * @param value { Number } The value to insert.\n     * @param offset { Number } The offset to insert the value at.\n     *\n     * @return this\n     */\n    insertInt16LE(value, offset) {\n        return this._insertNumberValue(Buffer.prototype.writeInt16LE, 2, value, offset);\n    }\n    /**\n     * Writes an Int32BE value to the current write position (or at optional offset).\n     *\n     * @param value { Number } The value to write.\n     * @param offset { Number } The offset to write the value at.\n     *\n     * @return this\n     */\n    writeInt32BE(value, offset) {\n        return this._writeNumberValue(Buffer.prototype.writeInt32BE, 4, value, offset);\n    }\n    /**\n     * Inserts an Int32BE value at the given offset value.\n     *\n     * @param value { Number } The value to insert.\n     * @param offset { Number } The offset to insert the value at.\n     *\n     * @return this\n     */\n    insertInt32BE(value, offset) {\n        return this._insertNumberValue(Buffer.prototype.writeInt32BE, 4, value, offset);\n    }\n    /**\n     * Writes an Int32LE value to the current write position (or at optional offset).\n     *\n     * @param value { Number } The value to write.\n     * @param offset { Number } The offset to write the value at.\n     *\n     * @return this\n     */\n    writeInt32LE(value, offset) {\n        return this._writeNumberValue(Buffer.prototype.writeInt32LE, 4, value, offset);\n    }\n    /**\n     * Inserts an Int32LE value at the given offset value.\n     *\n     * @param value { Number } The value to insert.\n     * @param offset { Number } The offset to insert the value at.\n     *\n     * @return this\n     */\n    insertInt32LE(value, offset) {\n        return this._insertNumberValue(Buffer.prototype.writeInt32LE, 4, value, offset);\n    }\n    /**\n     * Writes a BigInt64BE value to the current write position (or at optional offset).\n     *\n     * @param value { BigInt } The value to write.\n     * @param offset { Number } The offset to write the value at.\n     *\n     * @return this\n     */\n    writeBigInt64BE(value, offset) {\n        utils_1.bigIntAndBufferInt64Check('writeBigInt64BE');\n        return this._writeNumberValue(Buffer.prototype.writeBigInt64BE, 8, value, offset);\n    }\n    /**\n     * Inserts a BigInt64BE value at the given offset value.\n     *\n     * @param value { BigInt } The value to insert.\n     * @param offset { Number } The offset to insert the value at.\n     *\n     * @return this\n     */\n    insertBigInt64BE(value, offset) {\n        utils_1.bigIntAndBufferInt64Check('writeBigInt64BE');\n        return this._insertNumberValue(Buffer.prototype.writeBigInt64BE, 8, value, offset);\n    }\n    /**\n     * Writes a BigInt64LE value to the current write position (or at optional offset).\n     *\n     * @param value { BigInt } The value to write.\n     * @param offset { Number } The offset to write the value at.\n     *\n     * @return this\n     */\n    writeBigInt64LE(value, offset) {\n        utils_1.bigIntAndBufferInt64Check('writeBigInt64LE');\n        return this._writeNumberValue(Buffer.prototype.writeBigInt64LE, 8, value, offset);\n    }\n    /**\n     * Inserts a Int64LE value at the given offset value.\n     *\n     * @param value { BigInt } The value to insert.\n     * @param offset { Number } The offset to insert the value at.\n     *\n     * @return this\n     */\n    insertBigInt64LE(value, offset) {\n        utils_1.bigIntAndBufferInt64Check('writeBigInt64LE');\n        return this._insertNumberValue(Buffer.prototype.writeBigInt64LE, 8, value, offset);\n    }\n    // Unsigned Integers\n    /**\n     * Reads an UInt8 value from the current read position or an optionally provided offset.\n     *\n     * @param offset { Number } The offset to read data from (optional)\n     * @return { Number }\n     */\n    readUInt8(offset) {\n        return this._readNumberValue(Buffer.prototype.readUInt8, 1, offset);\n    }\n    /**\n     * Reads an UInt16BE value from the current read position or an optionally provided offset.\n     *\n     * @param offset { Number } The offset to read data from (optional)\n     * @return { Number }\n     */\n    readUInt16BE(offset) {\n        return this._readNumberValue(Buffer.prototype.readUInt16BE, 2, offset);\n    }\n    /**\n     * Reads an UInt16LE value from the current read position or an optionally provided offset.\n     *\n     * @param offset { Number } The offset to read data from (optional)\n     * @return { Number }\n     */\n    readUInt16LE(offset) {\n        return this._readNumberValue(Buffer.prototype.readUInt16LE, 2, offset);\n    }\n    /**\n     * Reads an UInt32BE value from the current read position or an optionally provided offset.\n     *\n     * @param offset { Number } The offset to read data from (optional)\n     * @return { Number }\n     */\n    readUInt32BE(offset) {\n        return this._readNumberValue(Buffer.prototype.readUInt32BE, 4, offset);\n    }\n    /**\n     * Reads an UInt32LE value from the current read position or an optionally provided offset.\n     *\n     * @param offset { Number } The offset to read data from (optional)\n     * @return { Number }\n     */\n    readUInt32LE(offset) {\n        return this._readNumberValue(Buffer.prototype.readUInt32LE, 4, offset);\n    }\n    /**\n     * Reads a BigUInt64BE value from the current read position or an optionally provided offset.\n     *\n     * @param offset { Number } The offset to read data from (optional)\n     * @return { BigInt }\n     */\n    readBigUInt64BE(offset) {\n        utils_1.bigIntAndBufferInt64Check('readBigUInt64BE');\n        return this._readNumberValue(Buffer.prototype.readBigUInt64BE, 8, offset);\n    }\n    /**\n     * Reads a BigUInt64LE value from the current read position or an optionally provided offset.\n     *\n     * @param offset { Number } The offset to read data from (optional)\n     * @return { BigInt }\n     */\n    readBigUInt64LE(offset) {\n        utils_1.bigIntAndBufferInt64Check('readBigUInt64LE');\n        return this._readNumberValue(Buffer.prototype.readBigUInt64LE, 8, offset);\n    }\n    /**\n     * Writes an UInt8 value to the current write position (or at optional offset).\n     *\n     * @param value { Number } The value to write.\n     * @param offset { Number } The offset to write the value at.\n     *\n     * @return this\n     */\n    writeUInt8(value, offset) {\n        return this._writeNumberValue(Buffer.prototype.writeUInt8, 1, value, offset);\n    }\n    /**\n     * Inserts an UInt8 value at the given offset value.\n     *\n     * @param value { Number } The value to insert.\n     * @param offset { Number } The offset to insert the value at.\n     *\n     * @return this\n     */\n    insertUInt8(value, offset) {\n        return this._insertNumberValue(Buffer.prototype.writeUInt8, 1, value, offset);\n    }\n    /**\n     * Writes an UInt16BE value to the current write position (or at optional offset).\n     *\n     * @param value { Number } The value to write.\n     * @param offset { Number } The offset to write the value at.\n     *\n     * @return this\n     */\n    writeUInt16BE(value, offset) {\n        return this._writeNumberValue(Buffer.prototype.writeUInt16BE, 2, value, offset);\n    }\n    /**\n     * Inserts an UInt16BE value at the given offset value.\n     *\n     * @param value { Number } The value to insert.\n     * @param offset { Number } The offset to insert the value at.\n     *\n     * @return this\n     */\n    insertUInt16BE(value, offset) {\n        return this._insertNumberValue(Buffer.prototype.writeUInt16BE, 2, value, offset);\n    }\n    /**\n     * Writes an UInt16LE value to the current write position (or at optional offset).\n     *\n     * @param value { Number } The value to write.\n     * @param offset { Number } The offset to write the value at.\n     *\n     * @return this\n     */\n    writeUInt16LE(value, offset) {\n        return this._writeNumberValue(Buffer.prototype.writeUInt16LE, 2, value, offset);\n    }\n    /**\n     * Inserts an UInt16LE value at the given offset value.\n     *\n     * @param value { Number } The value to insert.\n     * @param offset { Number } The offset to insert the value at.\n     *\n     * @return this\n     */\n    insertUInt16LE(value, offset) {\n        return this._insertNumberValue(Buffer.prototype.writeUInt16LE, 2, value, offset);\n    }\n    /**\n     * Writes an UInt32BE value to the current write position (or at optional offset).\n     *\n     * @param value { Number } The value to write.\n     * @param offset { Number } The offset to write the value at.\n     *\n     * @return this\n     */\n    writeUInt32BE(value, offset) {\n        return this._writeNumberValue(Buffer.prototype.writeUInt32BE, 4, value, offset);\n    }\n    /**\n     * Inserts an UInt32BE value at the given offset value.\n     *\n     * @param value { Number } The value to insert.\n     * @param offset { Number } The offset to insert the value at.\n     *\n     * @return this\n     */\n    insertUInt32BE(value, offset) {\n        return this._insertNumberValue(Buffer.prototype.writeUInt32BE, 4, value, offset);\n    }\n    /**\n     * Writes an UInt32LE value to the current write position (or at optional offset).\n     *\n     * @param value { Number } The value to write.\n     * @param offset { Number } The offset to write the value at.\n     *\n     * @return this\n     */\n    writeUInt32LE(value, offset) {\n        return this._writeNumberValue(Buffer.prototype.writeUInt32LE, 4, value, offset);\n    }\n    /**\n     * Inserts an UInt32LE value at the given offset value.\n     *\n     * @param value { Number } The value to insert.\n     * @param offset { Number } The offset to insert the value at.\n     *\n     * @return this\n     */\n    insertUInt32LE(value, offset) {\n        return this._insertNumberValue(Buffer.prototype.writeUInt32LE, 4, value, offset);\n    }\n    /**\n     * Writes a BigUInt64BE value to the current write position (or at optional offset).\n     *\n     * @param value { Number } The value to write.\n     * @param offset { Number } The offset to write the value at.\n     *\n     * @return this\n     */\n    writeBigUInt64BE(value, offset) {\n        utils_1.bigIntAndBufferInt64Check('writeBigUInt64BE');\n        return this._writeNumberValue(Buffer.prototype.writeBigUInt64BE, 8, value, offset);\n    }\n    /**\n     * Inserts a BigUInt64BE value at the given offset value.\n     *\n     * @param value { Number } The value to insert.\n     * @param offset { Number } The offset to insert the value at.\n     *\n     * @return this\n     */\n    insertBigUInt64BE(value, offset) {\n        utils_1.bigIntAndBufferInt64Check('writeBigUInt64BE');\n        return this._insertNumberValue(Buffer.prototype.writeBigUInt64BE, 8, value, offset);\n    }\n    /**\n     * Writes a BigUInt64LE value to the current write position (or at optional offset).\n     *\n     * @param value { Number } The value to write.\n     * @param offset { Number } The offset to write the value at.\n     *\n     * @return this\n     */\n    writeBigUInt64LE(value, offset) {\n        utils_1.bigIntAndBufferInt64Check('writeBigUInt64LE');\n        return this._writeNumberValue(Buffer.prototype.writeBigUInt64LE, 8, value, offset);\n    }\n    /**\n     * Inserts a BigUInt64LE value at the given offset value.\n     *\n     * @param value { Number } The value to insert.\n     * @param offset { Number } The offset to insert the value at.\n     *\n     * @return this\n     */\n    insertBigUInt64LE(value, offset) {\n        utils_1.bigIntAndBufferInt64Check('writeBigUInt64LE');\n        return this._insertNumberValue(Buffer.prototype.writeBigUInt64LE, 8, value, offset);\n    }\n    // Floating Point\n    /**\n     * Reads an FloatBE value from the current read position or an optionally provided offset.\n     *\n     * @param offset { Number } The offset to read data from (optional)\n     * @return { Number }\n     */\n    readFloatBE(offset) {\n        return this._readNumberValue(Buffer.prototype.readFloatBE, 4, offset);\n    }\n    /**\n     * Reads an FloatLE value from the current read position or an optionally provided offset.\n     *\n     * @param offset { Number } The offset to read data from (optional)\n     * @return { Number }\n     */\n    readFloatLE(offset) {\n        return this._readNumberValue(Buffer.prototype.readFloatLE, 4, offset);\n    }\n    /**\n     * Writes a FloatBE value to the current write position (or at optional offset).\n     *\n     * @param value { Number } The value to write.\n     * @param offset { Number } The offset to write the value at.\n     *\n     * @return this\n     */\n    writeFloatBE(value, offset) {\n        return this._writeNumberValue(Buffer.prototype.writeFloatBE, 4, value, offset);\n    }\n    /**\n     * Inserts a FloatBE value at the given offset value.\n     *\n     * @param value { Number } The value to insert.\n     * @param offset { Number } The offset to insert the value at.\n     *\n     * @return this\n     */\n    insertFloatBE(value, offset) {\n        return this._insertNumberValue(Buffer.prototype.writeFloatBE, 4, value, offset);\n    }\n    /**\n     * Writes a FloatLE value to the current write position (or at optional offset).\n     *\n     * @param value { Number } The value to write.\n     * @param offset { Number } The offset to write the value at.\n     *\n     * @return this\n     */\n    writeFloatLE(value, offset) {\n        return this._writeNumberValue(Buffer.prototype.writeFloatLE, 4, value, offset);\n    }\n    /**\n     * Inserts a FloatLE value at the given offset value.\n     *\n     * @param value { Number } The value to insert.\n     * @param offset { Number } The offset to insert the value at.\n     *\n     * @return this\n     */\n    insertFloatLE(value, offset) {\n        return this._insertNumberValue(Buffer.prototype.writeFloatLE, 4, value, offset);\n    }\n    // Double Floating Point\n    /**\n     * Reads an DoublEBE value from the current read position or an optionally provided offset.\n     *\n     * @param offset { Number } The offset to read data from (optional)\n     * @return { Number }\n     */\n    readDoubleBE(offset) {\n        return this._readNumberValue(Buffer.prototype.readDoubleBE, 8, offset);\n    }\n    /**\n     * Reads an DoubleLE value from the current read position or an optionally provided offset.\n     *\n     * @param offset { Number } The offset to read data from (optional)\n     * @return { Number }\n     */\n    readDoubleLE(offset) {\n        return this._readNumberValue(Buffer.prototype.readDoubleLE, 8, offset);\n    }\n    /**\n     * Writes a DoubleBE value to the current write position (or at optional offset).\n     *\n     * @param value { Number } The value to write.\n     * @param offset { Number } The offset to write the value at.\n     *\n     * @return this\n     */\n    writeDoubleBE(value, offset) {\n        return this._writeNumberValue(Buffer.prototype.writeDoubleBE, 8, value, offset);\n    }\n    /**\n     * Inserts a DoubleBE value at the given offset value.\n     *\n     * @param value { Number } The value to insert.\n     * @param offset { Number } The offset to insert the value at.\n     *\n     * @return this\n     */\n    insertDoubleBE(value, offset) {\n        return this._insertNumberValue(Buffer.prototype.writeDoubleBE, 8, value, offset);\n    }\n    /**\n     * Writes a DoubleLE value to the current write position (or at optional offset).\n     *\n     * @param value { Number } The value to write.\n     * @param offset { Number } The offset to write the value at.\n     *\n     * @return this\n     */\n    writeDoubleLE(value, offset) {\n        return this._writeNumberValue(Buffer.prototype.writeDoubleLE, 8, value, offset);\n    }\n    /**\n     * Inserts a DoubleLE value at the given offset value.\n     *\n     * @param value { Number } The value to insert.\n     * @param offset { Number } The offset to insert the value at.\n     *\n     * @return this\n     */\n    insertDoubleLE(value, offset) {\n        return this._insertNumberValue(Buffer.prototype.writeDoubleLE, 8, value, offset);\n    }\n    // Strings\n    /**\n     * Reads a String from the current read position.\n     *\n     * @param arg1 { Number | String } The number of bytes to read as a String, or the BufferEncoding to use for\n     *             the string (Defaults to instance level encoding).\n     * @param encoding { String } The BufferEncoding to use for the string (Defaults to instance level encoding).\n     *\n     * @return { String }\n     */\n    readString(arg1, encoding) {\n        let lengthVal;\n        // Length provided\n        if (typeof arg1 === 'number') {\n            utils_1.checkLengthValue(arg1);\n            lengthVal = Math.min(arg1, this.length - this._readOffset);\n        }\n        else {\n            encoding = arg1;\n            lengthVal = this.length - this._readOffset;\n        }\n        // Check encoding\n        if (typeof encoding !== 'undefined') {\n            utils_1.checkEncoding(encoding);\n        }\n        const value = this._buff.slice(this._readOffset, this._readOffset + lengthVal).toString(encoding || this._encoding);\n        this._readOffset += lengthVal;\n        return value;\n    }\n    /**\n     * Inserts a String\n     *\n     * @param value { String } The String value to insert.\n     * @param offset { Number } The offset to insert the string at.\n     * @param encoding { String } The BufferEncoding to use for writing strings (defaults to instance encoding).\n     *\n     * @return this\n     */\n    insertString(value, offset, encoding) {\n        utils_1.checkOffsetValue(offset);\n        return this._handleString(value, true, offset, encoding);\n    }\n    /**\n     * Writes a String\n     *\n     * @param value { String } The String value to write.\n     * @param arg2 { Number | String } The offset to write the string at, or the BufferEncoding to use.\n     * @param encoding { String } The BufferEncoding to use for writing strings (defaults to instance encoding).\n     *\n     * @return this\n     */\n    writeString(value, arg2, encoding) {\n        return this._handleString(value, false, arg2, encoding);\n    }\n    /**\n     * Reads a null-terminated String from the current read position.\n     *\n     * @param encoding { String } The BufferEncoding to use for the string (Defaults to instance level encoding).\n     *\n     * @return { String }\n     */\n    readStringNT(encoding) {\n        if (typeof encoding !== 'undefined') {\n            utils_1.checkEncoding(encoding);\n        }\n        // Set null character position to the end SmartBuffer instance.\n        let nullPos = this.length;\n        // Find next null character (if one is not found, default from above is used)\n        for (let i = this._readOffset; i < this.length; i++) {\n            if (this._buff[i] === 0x00) {\n                nullPos = i;\n                break;\n            }\n        }\n        // Read string value\n        const value = this._buff.slice(this._readOffset, nullPos);\n        // Increment internal Buffer read offset\n        this._readOffset = nullPos + 1;\n        return value.toString(encoding || this._encoding);\n    }\n    /**\n     * Inserts a null-terminated String.\n     *\n     * @param value { String } The String value to write.\n     * @param arg2 { Number | String } The offset to write the string to, or the BufferEncoding to use.\n     * @param encoding { String } The BufferEncoding to use for writing strings (defaults to instance encoding).\n     *\n     * @return this\n     */\n    insertStringNT(value, offset, encoding) {\n        utils_1.checkOffsetValue(offset);\n        // Write Values\n        this.insertString(value, offset, encoding);\n        this.insertUInt8(0x00, offset + value.length);\n        return this;\n    }\n    /**\n     * Writes a null-terminated String.\n     *\n     * @param value { String } The String value to write.\n     * @param arg2 { Number | String } The offset to write the string to, or the BufferEncoding to use.\n     * @param encoding { String } The BufferEncoding to use for writing strings (defaults to instance encoding).\n     *\n     * @return this\n     */\n    writeStringNT(value, arg2, encoding) {\n        // Write Values\n        this.writeString(value, arg2, encoding);\n        this.writeUInt8(0x00, typeof arg2 === 'number' ? arg2 + value.length : this.writeOffset);\n        return this;\n    }\n    // Buffers\n    /**\n     * Reads a Buffer from the internal read position.\n     *\n     * @param length { Number } The length of data to read as a Buffer.\n     *\n     * @return { Buffer }\n     */\n    readBuffer(length) {\n        if (typeof length !== 'undefined') {\n            utils_1.checkLengthValue(length);\n        }\n        const lengthVal = typeof length === 'number' ? length : this.length;\n        const endPoint = Math.min(this.length, this._readOffset + lengthVal);\n        // Read buffer value\n        const value = this._buff.slice(this._readOffset, endPoint);\n        // Increment internal Buffer read offset\n        this._readOffset = endPoint;\n        return value;\n    }\n    /**\n     * Writes a Buffer to the current write position.\n     *\n     * @param value { Buffer } The Buffer to write.\n     * @param offset { Number } The offset to write the Buffer to.\n     *\n     * @return this\n     */\n    insertBuffer(value, offset) {\n        utils_1.checkOffsetValue(offset);\n        return this._handleBuffer(value, true, offset);\n    }\n    /**\n     * Writes a Buffer to the current write position.\n     *\n     * @param value { Buffer } The Buffer to write.\n     * @param offset { Number } The offset to write the Buffer to.\n     *\n     * @return this\n     */\n    writeBuffer(value, offset) {\n        return this._handleBuffer(value, false, offset);\n    }\n    /**\n     * Reads a null-terminated Buffer from the current read poisiton.\n     *\n     * @return { Buffer }\n     */\n    readBufferNT() {\n        // Set null character position to the end SmartBuffer instance.\n        let nullPos = this.length;\n        // Find next null character (if one is not found, default from above is used)\n        for (let i = this._readOffset; i < this.length; i++) {\n            if (this._buff[i] === 0x00) {\n                nullPos = i;\n                break;\n            }\n        }\n        // Read value\n        const value = this._buff.slice(this._readOffset, nullPos);\n        // Increment internal Buffer read offset\n        this._readOffset = nullPos + 1;\n        return value;\n    }\n    /**\n     * Inserts a null-terminated Buffer.\n     *\n     * @param value { Buffer } The Buffer to write.\n     * @param offset { Number } The offset to write the Buffer to.\n     *\n     * @return this\n     */\n    insertBufferNT(value, offset) {\n        utils_1.checkOffsetValue(offset);\n        // Write Values\n        this.insertBuffer(value, offset);\n        this.insertUInt8(0x00, offset + value.length);\n        return this;\n    }\n    /**\n     * Writes a null-terminated Buffer.\n     *\n     * @param value { Buffer } The Buffer to write.\n     * @param offset { Number } The offset to write the Buffer to.\n     *\n     * @return this\n     */\n    writeBufferNT(value, offset) {\n        // Checks for valid numberic value;\n        if (typeof offset !== 'undefined') {\n            utils_1.checkOffsetValue(offset);\n        }\n        // Write Values\n        this.writeBuffer(value, offset);\n        this.writeUInt8(0x00, typeof offset === 'number' ? offset + value.length : this._writeOffset);\n        return this;\n    }\n    /**\n     * Clears the SmartBuffer instance to its original empty state.\n     */\n    clear() {\n        this._writeOffset = 0;\n        this._readOffset = 0;\n        this.length = 0;\n        return this;\n    }\n    /**\n     * Gets the remaining data left to be read from the SmartBuffer instance.\n     *\n     * @return { Number }\n     */\n    remaining() {\n        return this.length - this._readOffset;\n    }\n    /**\n     * Gets the current read offset value of the SmartBuffer instance.\n     *\n     * @return { Number }\n     */\n    get readOffset() {\n        return this._readOffset;\n    }\n    /**\n     * Sets the read offset value of the SmartBuffer instance.\n     *\n     * @param offset { Number } - The offset value to set.\n     */\n    set readOffset(offset) {\n        utils_1.checkOffsetValue(offset);\n        // Check for bounds.\n        utils_1.checkTargetOffset(offset, this);\n        this._readOffset = offset;\n    }\n    /**\n     * Gets the current write offset value of the SmartBuffer instance.\n     *\n     * @return { Number }\n     */\n    get writeOffset() {\n        return this._writeOffset;\n    }\n    /**\n     * Sets the write offset value of the SmartBuffer instance.\n     *\n     * @param offset { Number } - The offset value to set.\n     */\n    set writeOffset(offset) {\n        utils_1.checkOffsetValue(offset);\n        // Check for bounds.\n        utils_1.checkTargetOffset(offset, this);\n        this._writeOffset = offset;\n    }\n    /**\n     * Gets the currently set string encoding of the SmartBuffer instance.\n     *\n     * @return { BufferEncoding } The string Buffer encoding currently set.\n     */\n    get encoding() {\n        return this._encoding;\n    }\n    /**\n     * Sets the string encoding of the SmartBuffer instance.\n     *\n     * @param encoding { BufferEncoding } The string Buffer encoding to set.\n     */\n    set encoding(encoding) {\n        utils_1.checkEncoding(encoding);\n        this._encoding = encoding;\n    }\n    /**\n     * Gets the underlying internal Buffer. (This includes unmanaged data in the Buffer)\n     *\n     * @return { Buffer } The Buffer value.\n     */\n    get internalBuffer() {\n        return this._buff;\n    }\n    /**\n     * Gets the value of the internal managed Buffer (Includes managed data only)\n     *\n     * @param { Buffer }\n     */\n    toBuffer() {\n        return this._buff.slice(0, this.length);\n    }\n    /**\n     * Gets the String value of the internal managed Buffer\n     *\n     * @param encoding { String } The BufferEncoding to display the Buffer as (defaults to instance level encoding).\n     */\n    toString(encoding) {\n        const encodingVal = typeof encoding === 'string' ? encoding : this._encoding;\n        // Check for invalid encoding.\n        utils_1.checkEncoding(encodingVal);\n        return this._buff.toString(encodingVal, 0, this.length);\n    }\n    /**\n     * Destroys the SmartBuffer instance.\n     */\n    destroy() {\n        this.clear();\n        return this;\n    }\n    /**\n     * Handles inserting and writing strings.\n     *\n     * @param value { String } The String value to insert.\n     * @param isInsert { Boolean } True if inserting a string, false if writing.\n     * @param arg2 { Number | String } The offset to insert the string at, or the BufferEncoding to use.\n     * @param encoding { String } The BufferEncoding to use for writing strings (defaults to instance encoding).\n     */\n    _handleString(value, isInsert, arg3, encoding) {\n        let offsetVal = this._writeOffset;\n        let encodingVal = this._encoding;\n        // Check for offset\n        if (typeof arg3 === 'number') {\n            offsetVal = arg3;\n            // Check for encoding\n        }\n        else if (typeof arg3 === 'string') {\n            utils_1.checkEncoding(arg3);\n            encodingVal = arg3;\n        }\n        // Check for encoding (third param)\n        if (typeof encoding === 'string') {\n            utils_1.checkEncoding(encoding);\n            encodingVal = encoding;\n        }\n        // Calculate bytelength of string.\n        const byteLength = Buffer.byteLength(value, encodingVal);\n        // Ensure there is enough internal Buffer capacity.\n        if (isInsert) {\n            this.ensureInsertable(byteLength, offsetVal);\n        }\n        else {\n            this._ensureWriteable(byteLength, offsetVal);\n        }\n        // Write value\n        this._buff.write(value, offsetVal, byteLength, encodingVal);\n        // Increment internal Buffer write offset;\n        if (isInsert) {\n            this._writeOffset += byteLength;\n        }\n        else {\n            // If an offset was given, check to see if we wrote beyond the current writeOffset.\n            if (typeof arg3 === 'number') {\n                this._writeOffset = Math.max(this._writeOffset, offsetVal + byteLength);\n            }\n            else {\n                // If no offset was given, we wrote to the end of the SmartBuffer so increment writeOffset.\n                this._writeOffset += byteLength;\n            }\n        }\n        return this;\n    }\n    /**\n     * Handles writing or insert of a Buffer.\n     *\n     * @param value { Buffer } The Buffer to write.\n     * @param offset { Number } The offset to write the Buffer to.\n     */\n    _handleBuffer(value, isInsert, offset) {\n        const offsetVal = typeof offset === 'number' ? offset : this._writeOffset;\n        // Ensure there is enough internal Buffer capacity.\n        if (isInsert) {\n            this.ensureInsertable(value.length, offsetVal);\n        }\n        else {\n            this._ensureWriteable(value.length, offsetVal);\n        }\n        // Write buffer value\n        value.copy(this._buff, offsetVal);\n        // Increment internal Buffer write offset;\n        if (isInsert) {\n            this._writeOffset += value.length;\n        }\n        else {\n            // If an offset was given, check to see if we wrote beyond the current writeOffset.\n            if (typeof offset === 'number') {\n                this._writeOffset = Math.max(this._writeOffset, offsetVal + value.length);\n            }\n            else {\n                // If no offset was given, we wrote to the end of the SmartBuffer so increment writeOffset.\n                this._writeOffset += value.length;\n            }\n        }\n        return this;\n    }\n    /**\n     * Ensures that the internal Buffer is large enough to read data.\n     *\n     * @param length { Number } The length of the data that needs to be read.\n     * @param offset { Number } The offset of the data that needs to be read.\n     */\n    ensureReadable(length, offset) {\n        // Offset value defaults to managed read offset.\n        let offsetVal = this._readOffset;\n        // If an offset was provided, use it.\n        if (typeof offset !== 'undefined') {\n            // Checks for valid numberic value;\n            utils_1.checkOffsetValue(offset);\n            // Overide with custom offset.\n            offsetVal = offset;\n        }\n        // Checks if offset is below zero, or the offset+length offset is beyond the total length of the managed data.\n        if (offsetVal < 0 || offsetVal + length > this.length) {\n            throw new Error(utils_1.ERRORS.INVALID_READ_BEYOND_BOUNDS);\n        }\n    }\n    /**\n     * Ensures that the internal Buffer is large enough to insert data.\n     *\n     * @param dataLength { Number } The length of the data that needs to be written.\n     * @param offset { Number } The offset of the data to be written.\n     */\n    ensureInsertable(dataLength, offset) {\n        // Checks for valid numberic value;\n        utils_1.checkOffsetValue(offset);\n        // Ensure there is enough internal Buffer capacity.\n        this._ensureCapacity(this.length + dataLength);\n        // If an offset was provided and its not the very end of the buffer, copy data into appropriate location in regards to the offset.\n        if (offset < this.length) {\n            this._buff.copy(this._buff, offset + dataLength, offset, this._buff.length);\n        }\n        // Adjust tracked smart buffer length\n        if (offset + dataLength > this.length) {\n            this.length = offset + dataLength;\n        }\n        else {\n            this.length += dataLength;\n        }\n    }\n    /**\n     * Ensures that the internal Buffer is large enough to write data.\n     *\n     * @param dataLength { Number } The length of the data that needs to be written.\n     * @param offset { Number } The offset of the data to be written (defaults to writeOffset).\n     */\n    _ensureWriteable(dataLength, offset) {\n        const offsetVal = typeof offset === 'number' ? offset : this._writeOffset;\n        // Ensure enough capacity to write data.\n        this._ensureCapacity(offsetVal + dataLength);\n        // Adjust SmartBuffer length (if offset + length is larger than managed length, adjust length)\n        if (offsetVal + dataLength > this.length) {\n            this.length = offsetVal + dataLength;\n        }\n    }\n    /**\n     * Ensures that the internal Buffer is large enough to write at least the given amount of data.\n     *\n     * @param minLength { Number } The minimum length of the data needs to be written.\n     */\n    _ensureCapacity(minLength) {\n        const oldLength = this._buff.length;\n        if (minLength > oldLength) {\n            let data = this._buff;\n            let newLength = (oldLength * 3) / 2 + 1;\n            if (newLength < minLength) {\n                newLength = minLength;\n            }\n            this._buff = Buffer.allocUnsafe(newLength);\n            data.copy(this._buff, 0, 0, oldLength);\n        }\n    }\n    /**\n     * Reads a numeric number value using the provided function.\n     *\n     * @typeparam T { number | bigint } The type of the value to be read\n     *\n     * @param func { Function(offset: number) => number } The function to read data on the internal Buffer with.\n     * @param byteSize { Number } The number of bytes read.\n     * @param offset { Number } The offset to read from (optional). When this is not provided, the managed readOffset is used instead.\n     *\n     * @returns { T } the number value\n     */\n    _readNumberValue(func, byteSize, offset) {\n        this.ensureReadable(byteSize, offset);\n        // Call Buffer.readXXXX();\n        const value = func.call(this._buff, typeof offset === 'number' ? offset : this._readOffset);\n        // Adjust internal read offset if an optional read offset was not provided.\n        if (typeof offset === 'undefined') {\n            this._readOffset += byteSize;\n        }\n        return value;\n    }\n    /**\n     * Inserts a numeric number value based on the given offset and value.\n     *\n     * @typeparam T { number | bigint } The type of the value to be written\n     *\n     * @param func { Function(offset: T, offset?) => number} The function to write data on the internal Buffer with.\n     * @param byteSize { Number } The number of bytes written.\n     * @param value { T } The number value to write.\n     * @param offset { Number } the offset to write the number at (REQUIRED).\n     *\n     * @returns SmartBuffer this buffer\n     */\n    _insertNumberValue(func, byteSize, value, offset) {\n        // Check for invalid offset values.\n        utils_1.checkOffsetValue(offset);\n        // Ensure there is enough internal Buffer capacity. (raw offset is passed)\n        this.ensureInsertable(byteSize, offset);\n        // Call buffer.writeXXXX();\n        func.call(this._buff, value, offset);\n        // Adjusts internally managed write offset.\n        this._writeOffset += byteSize;\n        return this;\n    }\n    /**\n     * Writes a numeric number value based on the given offset and value.\n     *\n     * @typeparam T { number | bigint } The type of the value to be written\n     *\n     * @param func { Function(offset: T, offset?) => number} The function to write data on the internal Buffer with.\n     * @param byteSize { Number } The number of bytes written.\n     * @param value { T } The number value to write.\n     * @param offset { Number } the offset to write the number at (REQUIRED).\n     *\n     * @returns SmartBuffer this buffer\n     */\n    _writeNumberValue(func, byteSize, value, offset) {\n        // If an offset was provided, validate it.\n        if (typeof offset === 'number') {\n            // Check if we're writing beyond the bounds of the managed data.\n            if (offset < 0) {\n                throw new Error(utils_1.ERRORS.INVALID_WRITE_BEYOND_BOUNDS);\n            }\n            utils_1.checkOffsetValue(offset);\n        }\n        // Default to writeOffset if no offset value was given.\n        const offsetVal = typeof offset === 'number' ? offset : this._writeOffset;\n        // Ensure there is enough internal Buffer capacity. (raw offset is passed)\n        this._ensureWriteable(byteSize, offsetVal);\n        func.call(this._buff, value, offsetVal);\n        // If an offset was given, check to see if we wrote beyond the current writeOffset.\n        if (typeof offset === 'number') {\n            this._writeOffset = Math.max(this._writeOffset, offsetVal + byteSize);\n        }\n        else {\n            // If no numeric offset was given, we wrote to the end of the SmartBuffer so increment writeOffset.\n            this._writeOffset += byteSize;\n        }\n        return this;\n    }\n}\nexports.SmartBuffer = SmartBuffer;\n//# sourceMappingURL=smartbuffer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/smart-buffer/build/smartbuffer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/smart-buffer/build/utils.js":
/*!**************************************************!*\
  !*** ./node_modules/smart-buffer/build/utils.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst buffer_1 = __webpack_require__(/*! buffer */ \"buffer\");\n/**\n * Error strings\n */\nconst ERRORS = {\n    INVALID_ENCODING: 'Invalid encoding provided. Please specify a valid encoding the internal Node.js Buffer supports.',\n    INVALID_SMARTBUFFER_SIZE: 'Invalid size provided. Size must be a valid integer greater than zero.',\n    INVALID_SMARTBUFFER_BUFFER: 'Invalid Buffer provided in SmartBufferOptions.',\n    INVALID_SMARTBUFFER_OBJECT: 'Invalid SmartBufferOptions object supplied to SmartBuffer constructor or factory methods.',\n    INVALID_OFFSET: 'An invalid offset value was provided.',\n    INVALID_OFFSET_NON_NUMBER: 'An invalid offset value was provided. A numeric value is required.',\n    INVALID_LENGTH: 'An invalid length value was provided.',\n    INVALID_LENGTH_NON_NUMBER: 'An invalid length value was provived. A numeric value is required.',\n    INVALID_TARGET_OFFSET: 'Target offset is beyond the bounds of the internal SmartBuffer data.',\n    INVALID_TARGET_LENGTH: 'Specified length value moves cursor beyong the bounds of the internal SmartBuffer data.',\n    INVALID_READ_BEYOND_BOUNDS: 'Attempted to read beyond the bounds of the managed data.',\n    INVALID_WRITE_BEYOND_BOUNDS: 'Attempted to write beyond the bounds of the managed data.'\n};\nexports.ERRORS = ERRORS;\n/**\n * Checks if a given encoding is a valid Buffer encoding. (Throws an exception if check fails)\n *\n * @param { String } encoding The encoding string to check.\n */\nfunction checkEncoding(encoding) {\n    if (!buffer_1.Buffer.isEncoding(encoding)) {\n        throw new Error(ERRORS.INVALID_ENCODING);\n    }\n}\nexports.checkEncoding = checkEncoding;\n/**\n * Checks if a given number is a finite integer. (Throws an exception if check fails)\n *\n * @param { Number } value The number value to check.\n */\nfunction isFiniteInteger(value) {\n    return typeof value === 'number' && isFinite(value) && isInteger(value);\n}\nexports.isFiniteInteger = isFiniteInteger;\n/**\n * Checks if an offset/length value is valid. (Throws an exception if check fails)\n *\n * @param value The value to check.\n * @param offset True if checking an offset, false if checking a length.\n */\nfunction checkOffsetOrLengthValue(value, offset) {\n    if (typeof value === 'number') {\n        // Check for non finite/non integers\n        if (!isFiniteInteger(value) || value < 0) {\n            throw new Error(offset ? ERRORS.INVALID_OFFSET : ERRORS.INVALID_LENGTH);\n        }\n    }\n    else {\n        throw new Error(offset ? ERRORS.INVALID_OFFSET_NON_NUMBER : ERRORS.INVALID_LENGTH_NON_NUMBER);\n    }\n}\n/**\n * Checks if a length value is valid. (Throws an exception if check fails)\n *\n * @param { Number } length The value to check.\n */\nfunction checkLengthValue(length) {\n    checkOffsetOrLengthValue(length, false);\n}\nexports.checkLengthValue = checkLengthValue;\n/**\n * Checks if a offset value is valid. (Throws an exception if check fails)\n *\n * @param { Number } offset The value to check.\n */\nfunction checkOffsetValue(offset) {\n    checkOffsetOrLengthValue(offset, true);\n}\nexports.checkOffsetValue = checkOffsetValue;\n/**\n * Checks if a target offset value is out of bounds. (Throws an exception if check fails)\n *\n * @param { Number } offset The offset value to check.\n * @param { SmartBuffer } buff The SmartBuffer instance to check against.\n */\nfunction checkTargetOffset(offset, buff) {\n    if (offset < 0 || offset > buff.length) {\n        throw new Error(ERRORS.INVALID_TARGET_OFFSET);\n    }\n}\nexports.checkTargetOffset = checkTargetOffset;\n/**\n * Determines whether a given number is a integer.\n * @param value The number to check.\n */\nfunction isInteger(value) {\n    return typeof value === 'number' && isFinite(value) && Math.floor(value) === value;\n}\n/**\n * Throws if Node.js version is too low to support bigint\n */\nfunction bigIntAndBufferInt64Check(bufferMethod) {\n    if (typeof BigInt === 'undefined') {\n        throw new Error('Platform does not support JS BigInt type.');\n    }\n    if (typeof buffer_1.Buffer.prototype[bufferMethod] === 'undefined') {\n        throw new Error(`Platform does not support Buffer.prototype.${bufferMethod}.`);\n    }\n}\nexports.bigIntAndBufferInt64Check = bigIntAndBufferInt64Check;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/smart-buffer/build/utils.js\n");

/***/ })

};
;