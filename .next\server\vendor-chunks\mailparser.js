"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mailparser";
exports.ids = ["vendor-chunks/mailparser"];
exports.modules = {

/***/ "(rsc)/./node_modules/mailparser/index.js":
/*!******************************************!*\
  !*** ./node_modules/mailparser/index.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst MailParser = __webpack_require__(/*! ./lib/mail-parser */ \"(rsc)/./node_modules/mailparser/lib/mail-parser.js\");\nconst simpleParser = __webpack_require__(/*! ./lib/simple-parser */ \"(rsc)/./node_modules/mailparser/lib/simple-parser.js\");\n\nmodule.exports = {\n    MailParser,\n    simpleParser\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWFpbHBhcnNlci9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYixtQkFBbUIsbUJBQU8sQ0FBQyw2RUFBbUI7QUFDOUMscUJBQXFCLG1CQUFPLENBQUMsaUZBQXFCOztBQUVsRDtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXENvZGVcXG9uZS1tYWlsXFxub2RlX21vZHVsZXNcXG1haWxwYXJzZXJcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuY29uc3QgTWFpbFBhcnNlciA9IHJlcXVpcmUoJy4vbGliL21haWwtcGFyc2VyJyk7XG5jb25zdCBzaW1wbGVQYXJzZXIgPSByZXF1aXJlKCcuL2xpYi9zaW1wbGUtcGFyc2VyJyk7XG5cbm1vZHVsZS5leHBvcnRzID0ge1xuICAgIE1haWxQYXJzZXIsXG4gICAgc2ltcGxlUGFyc2VyXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mailparser/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mailparser/lib/mail-parser.js":
/*!****************************************************!*\
  !*** ./node_modules/mailparser/lib/mail-parser.js ***!
  \****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst mailsplit = __webpack_require__(/*! mailsplit */ \"(rsc)/./node_modules/mailparser/node_modules/mailsplit/index.js\");\nconst libmime = __webpack_require__(/*! libmime */ \"(rsc)/./node_modules/libmime/lib/libmime.js\");\nconst addressparser = __webpack_require__(/*! nodemailer/lib/addressparser */ \"(rsc)/./node_modules/mailparser/node_modules/nodemailer/lib/addressparser/index.js\");\nconst Transform = (__webpack_require__(/*! stream */ \"stream\").Transform);\nconst Splitter = mailsplit.Splitter;\nconst punycode = __webpack_require__(/*! punycode.js */ \"(rsc)/./node_modules/punycode.js/punycode.es6.js\");\nconst FlowedDecoder = __webpack_require__(/*! mailsplit/lib/flowed-decoder */ \"(rsc)/./node_modules/mailparser/node_modules/mailsplit/lib/flowed-decoder.js\");\nconst StreamHash = __webpack_require__(/*! ./stream-hash */ \"(rsc)/./node_modules/mailparser/lib/stream-hash.js\");\nconst iconv = __webpack_require__(/*! iconv-lite */ \"(rsc)/./node_modules/iconv-lite/lib/index.js\");\nconst { htmlToText } = __webpack_require__(/*! html-to-text */ \"(rsc)/./node_modules/html-to-text/lib/html-to-text.cjs\");\nconst he = __webpack_require__(/*! he */ \"(rsc)/./node_modules/he/he.js\");\nconst linkify = __webpack_require__(/*! linkify-it */ \"(rsc)/./node_modules/linkify-it/build/index.cjs.js\")();\nconst tlds = __webpack_require__(/*! tlds */ \"(rsc)/./node_modules/tlds/index.json\");\nconst encodingJapanese = __webpack_require__(/*! encoding-japanese */ \"(rsc)/./node_modules/encoding-japanese/src/index.js\");\n\nlinkify\n    .tlds(tlds) // Reload with full tlds list\n    .tlds('onion', true) // Add unofficial `.onion` domain\n    .add('git:', 'http:') // Add `git:` ptotocol as \"alias\"\n    .add('ftp:', null) // Disable `ftp:` ptotocol\n    .set({ fuzzyIP: true, fuzzyLink: true, fuzzyEmail: true });\n\n// twitter linkifier from\n// https://github.com/markdown-it/linkify-it#example-2-add-twitter-mentions-handler\nlinkify.add('@', {\n    validate(text, pos, self) {\n        let tail = text.slice(pos);\n\n        if (!self.re.twitter) {\n            self.re.twitter = new RegExp('^([a-zA-Z0-9_]){1,15}(?!_)(?=$|' + self.re.src_ZPCc + ')');\n        }\n        if (self.re.twitter.test(tail)) {\n            // Linkifier allows punctuation chars before prefix,\n            // but we additionally disable `@` (\"@@mention\" is invalid)\n            if (pos >= 2 && tail[pos - 2] === '@') {\n                return false;\n            }\n            return tail.match(self.re.twitter)[0].length;\n        }\n        return 0;\n    },\n    normalize(match) {\n        match.url = 'https://twitter.com/' + match.url.replace(/^@/, '');\n    }\n});\n\nclass IconvDecoder extends Transform {\n    constructor(Iconv, charset) {\n        super();\n\n        // Iconv throws error on ks_c_5601-1987 when it is mapped to EUC-KR\n        // https://github.com/bnoordhuis/node-iconv/issues/169\n        if (charset.toLowerCase() === 'ks_c_5601-1987') {\n            charset = 'CP949';\n        }\n        this.stream = new Iconv(charset, 'UTF-8//TRANSLIT//IGNORE');\n\n        this.inputEnded = false;\n        this.endCb = false;\n\n        this.stream.on('error', err => this.emit('error', err));\n        this.stream.on('data', chunk => this.push(chunk));\n        this.stream.on('end', () => {\n            this.inputEnded = true;\n            if (typeof this.endCb === 'function') {\n                this.endCb();\n            }\n        });\n    }\n\n    _transform(chunk, encoding, done) {\n        this.stream.write(chunk);\n        done();\n    }\n\n    _flush(done) {\n        this.endCb = done;\n        this.stream.end();\n    }\n}\n\nclass JPDecoder extends Transform {\n    constructor(charset) {\n        super();\n\n        this.charset = charset;\n        this.chunks = [];\n        this.chunklen = 0;\n    }\n\n    _transform(chunk, encoding, done) {\n        if (typeof chunk === 'string') {\n            chunk = Buffer.from(chunk, encoding);\n        }\n\n        this.chunks.push(chunk);\n        this.chunklen += chunk.length;\n        done();\n    }\n\n    _flush(done) {\n        let input = Buffer.concat(this.chunks, this.chunklen);\n        try {\n            let output = encodingJapanese.convert(input, {\n                to: 'UNICODE', // to_encoding\n                from: this.charset, // from_encoding\n                type: 'string'\n            });\n            if (typeof output === 'string') {\n                output = Buffer.from(output);\n            }\n            this.push(output);\n        } catch (err) {\n            // keep as is on errors\n            this.push(input);\n        }\n\n        done();\n    }\n}\n\nclass MailParser extends Transform {\n    constructor(config) {\n        super({\n            readableObjectMode: true,\n            writableObjectMode: false\n        });\n\n        this.options = config || {};\n        this.splitter = new Splitter(config);\n        this.finished = false;\n        this.waitingEnd = false;\n\n        this.headers = false;\n        this.headerLines = false;\n\n        this.endReceived = false;\n        this.reading = false;\n        this.hasFailed = false;\n\n        this.tree = false;\n        this.curnode = false;\n        this.waitUntilAttachmentEnd = false;\n        this.attachmentCallback = false;\n\n        this.hasHtml = false;\n        this.hasText = false;\n\n        this.text = false;\n        this.html = false;\n        this.textAsHtml = false;\n\n        this.attachmentList = [];\n\n        this.boundaries = [];\n\n        this.textTypes = ['text/plain', 'text/html'].concat(!this.options.keepDeliveryStatus ? 'message/delivery-status' : []);\n\n        this.decoder = this.getDecoder();\n\n        this.splitter.on('readable', () => {\n            if (this.reading) {\n                return false;\n            }\n            this.readData();\n        });\n\n        this.splitter.on('end', () => {\n            this.endReceived = true;\n            if (!this.reading) {\n                this.endStream();\n            }\n        });\n\n        this.splitter.on('error', err => {\n            this.hasFailed = true;\n            if (typeof this.waitingEnd === 'function') {\n                return this.waitingEnd(err);\n            }\n            this.emit('error', err);\n        });\n\n        this.libmime = new libmime.Libmime({ Iconv: this.options.Iconv });\n    }\n\n    getDecoder() {\n        if (this.options.Iconv) {\n            const Iconv = this.options.Iconv;\n            // create wrapper\n            return {\n                decodeStream(charset) {\n                    return new IconvDecoder(Iconv, charset);\n                }\n            };\n        } else {\n            return {\n                decodeStream(charset) {\n                    charset = (charset || 'ascii').toString().trim().toLowerCase();\n                    if (/^jis|^iso-?2022-?jp|^EUCJP/i.test(charset)) {\n                        // special case not supported by iconv-lite\n                        return new JPDecoder(charset);\n                    }\n\n                    return iconv.decodeStream(charset);\n                }\n            };\n        }\n    }\n\n    readData() {\n        if (this.hasFailed) {\n            return false;\n        }\n        this.reading = true;\n        let data = this.splitter.read();\n        if (data === null) {\n            this.reading = false;\n            if (this.endReceived) {\n                this.endStream();\n            }\n            return;\n        }\n\n        this.processChunk(data, err => {\n            if (err) {\n                if (typeof this.waitingEnd === 'function') {\n                    return this.waitingEnd(err);\n                }\n                return this.emit('error', err);\n            }\n            setImmediate(() => this.readData());\n        });\n    }\n\n    endStream() {\n        this.finished = true;\n\n        if (this.curnode && this.curnode.decoder) {\n            this.curnode.decoder.end();\n        }\n        if (typeof this.waitingEnd === 'function') {\n            this.waitingEnd();\n        }\n    }\n\n    _transform(chunk, encoding, done) {\n        if (!chunk || !chunk.length) {\n            return done();\n        }\n\n        if (this.splitter.write(chunk) === false) {\n            return this.splitter.once('drain', () => {\n                done();\n            });\n        } else {\n            return done();\n        }\n    }\n\n    _flush(done) {\n        setImmediate(() => this.splitter.end());\n        if (this.finished) {\n            return this.cleanup(done);\n        }\n        this.waitingEnd = () => {\n            this.cleanup(() => {\n                done();\n            });\n        };\n    }\n\n    cleanup(done) {\n        let finish = () => {\n            try {\n                let t = this.getTextContent();\n                this.push(t);\n            } catch (err) {\n                return this.emit('error', err);\n            }\n\n            done();\n        };\n\n        if (this.curnode && this.curnode.decoder && this.curnode.decoder.readable && !this.decoderEnded) {\n            (this.curnode.contentStream || this.curnode.decoder).once('end', () => {\n                finish();\n            });\n            this.curnode.decoder.end();\n        } else {\n            setImmediate(() => {\n                finish();\n            });\n        }\n    }\n\n    processHeaders(lines) {\n        let headers = new Map();\n        (lines || []).forEach(line => {\n            let key = line.key;\n            let value = ((this.libmime.decodeHeader(line.line) || {}).value || '').toString().trim();\n            value = Buffer.from(value, 'binary').toString();\n            switch (key) {\n                case 'content-type':\n                case 'content-disposition':\n                case 'dkim-signature':\n                    value = this.libmime.parseHeaderValue(value);\n                    if (value.value) {\n                        value.value = this.libmime.decodeWords(value.value);\n                    }\n                    Object.keys((value && value.params) || {}).forEach(key => {\n                        try {\n                            value.params[key] = this.libmime.decodeWords(value.params[key]);\n                        } catch (E) {\n                            // ignore, keep as is\n                        }\n                    });\n                    break;\n                case 'date': {\n                    let dateValue = new Date(value);\n                    if (isNaN(dateValue)) {\n                        // date parsing failed :S\n                        dateValue = new Date();\n                    }\n                    value = dateValue;\n                    break;\n                }\n                case 'subject':\n                    try {\n                        value = this.libmime.decodeWords(value);\n                    } catch (E) {\n                        // ignore, keep as is\n                    }\n                    break;\n                case 'references':\n                    try {\n                        value = this.libmime.decodeWords(value);\n                    } catch (E) {\n                        // ignore\n                    }\n                    value = value.split(/\\s+/).map(this.ensureMessageIDFormat);\n                    break;\n                case 'message-id':\n                case 'in-reply-to':\n                    try {\n                        value = this.libmime.decodeWords(value);\n                    } catch (E) {\n                        // ignore\n                    }\n                    value = this.ensureMessageIDFormat(value);\n                    break;\n                case 'priority':\n                case 'x-priority':\n                case 'x-msmail-priority':\n                case 'importance':\n                    key = 'priority';\n                    value = this.parsePriority(value);\n                    break;\n                case 'from':\n                case 'to':\n                case 'cc':\n                case 'bcc':\n                case 'sender':\n                case 'reply-to':\n                case 'delivered-to':\n                case 'return-path':\n                case 'disposition-notification-to':\n                    value = addressparser(value);\n                    this.decodeAddresses(value);\n                    value = {\n                        value,\n                        html: this.getAddressesHTML(value),\n                        text: this.getAddressesText(value)\n                    };\n                    break;\n            }\n\n            // handle list-* keys\n            if (key.substr(0, 5) === 'list-') {\n                value = this.parseListHeader(key.substr(5), value);\n                key = 'list';\n            }\n\n            if (value) {\n                if (!headers.has(key)) {\n                    headers.set(key, [].concat(value || []));\n                } else if (Array.isArray(value)) {\n                    headers.set(key, headers.get(key).concat(value));\n                } else {\n                    headers.get(key).push(value);\n                }\n            }\n        });\n\n        // keep only the first value\n        let singleKeys = [\n            'message-id',\n            'content-id',\n            'from',\n            'sender',\n            'in-reply-to',\n            'reply-to',\n            'subject',\n            'date',\n            'content-disposition',\n            'content-type',\n            'content-transfer-encoding',\n            'priority',\n            'mime-version',\n            'content-description',\n            'precedence',\n            'errors-to',\n            'disposition-notification-to'\n        ];\n\n        headers.forEach((value, key) => {\n            if (Array.isArray(value)) {\n                if (singleKeys.includes(key) && value.length) {\n                    headers.set(key, value[value.length - 1]);\n                } else if (value.length === 1) {\n                    headers.set(key, value[0]);\n                }\n            }\n\n            if (key === 'list') {\n                // normalize List-* headers\n                let listValue = {};\n                [].concat(value || []).forEach(val => {\n                    Object.keys(val || {}).forEach(listKey => {\n                        listValue[listKey] = val[listKey];\n                    });\n                });\n                headers.set(key, listValue);\n            }\n        });\n\n        return headers;\n    }\n\n    parseListHeader(key, value) {\n        let addresses = addressparser(value);\n        let response = {};\n        let data = addresses\n            .map(address => {\n                if (/^https?:/i.test(address.name)) {\n                    response.url = address.name;\n                } else if (address.name) {\n                    response.name = address.name;\n                }\n                if (/^mailto:/.test(address.address)) {\n                    response.mail = address.address.substr(7);\n                } else if (address.address && address.address.indexOf('@') < 0) {\n                    response.id = address.address;\n                } else if (address.address) {\n                    response.mail = address.address;\n                }\n                if (Object.keys(response).length) {\n                    return response;\n                }\n                return false;\n            })\n            .filter(address => address);\n        if (data.length) {\n            return {\n                [key]: response\n            };\n        }\n        return false;\n    }\n\n    parsePriority(value) {\n        value = value.toLowerCase().trim();\n        if (!isNaN(parseInt(value, 10))) {\n            // support \"X-Priority: 1 (Highest)\"\n            value = parseInt(value, 10) || 0;\n            if (value === 3) {\n                return 'normal';\n            } else if (value > 3) {\n                return 'low';\n            } else {\n                return 'high';\n            }\n        } else {\n            switch (value) {\n                case 'non-urgent':\n                case 'low':\n                    return 'low';\n                case 'urgent':\n                case 'high':\n                    return 'high';\n            }\n        }\n        return 'normal';\n    }\n\n    ensureMessageIDFormat(value) {\n        if (!value.length) {\n            return false;\n        }\n\n        if (value.charAt(0) !== '<') {\n            value = '<' + value;\n        }\n\n        if (value.charAt(value.length - 1) !== '>') {\n            value += '>';\n        }\n\n        return value;\n    }\n\n    decodeAddresses(addresses) {\n        let processedAddress = new WeakSet();\n        for (let i = 0; i < addresses.length; i++) {\n            let address = addresses[i];\n            address.name = (address.name || '').toString().trim();\n\n            if (!address.address && /^(=\\?([^?]+)\\?[Bb]\\?[^?]*\\?=)(\\s*=\\?([^?]+)\\?[Bb]\\?[^?]*\\?=)*$/.test(address.name) && !processedAddress.has(address)) {\n                let parsed = addressparser(this.libmime.decodeWords(address.name));\n                if (parsed.length) {\n                    parsed.forEach(entry => {\n                        processedAddress.add(entry);\n                        addresses.push(entry);\n                    });\n                }\n\n                // remove current element\n                addresses.splice(i, 1);\n                i--;\n                continue;\n            }\n\n            if (address.name) {\n                try {\n                    address.name = this.libmime.decodeWords(address.name);\n                } catch (E) {\n                    //ignore, keep as is\n                }\n            }\n            if (/@xn--/.test(address.address)) {\n                try {\n                    address.address =\n                        address.address.substr(0, address.address.lastIndexOf('@') + 1) +\n                        punycode.toUnicode(address.address.substr(address.address.lastIndexOf('@') + 1));\n                } catch (E) {\n                    // Not a valid punycode string; keep as is\n                }\n            }\n            if (address.group) {\n                this.decodeAddresses(address.group);\n            }\n        }\n    }\n\n    createNode(node) {\n        let contentType = node.contentType;\n        let disposition = node.disposition;\n        let encoding = node.encoding;\n        let charset = node.charset;\n\n        if (!contentType && node.root) {\n            contentType = 'text/plain';\n        }\n\n        let newNode = {\n            node,\n            headerLines: node.headers.lines,\n            headers: this.processHeaders(node.headers.getList()),\n            contentType,\n            children: []\n        };\n\n        if (!/^multipart\\//i.test(contentType)) {\n            if (disposition && !['attachment', 'inline'].includes(disposition)) {\n                disposition = 'attachment';\n            }\n\n            if (!disposition && !this.textTypes.includes(contentType)) {\n                newNode.disposition = 'attachment';\n            } else {\n                newNode.disposition = disposition || 'inline';\n            }\n\n            newNode.isAttachment = !this.textTypes.includes(contentType) || newNode.disposition !== 'inline';\n\n            newNode.encoding = ['quoted-printable', 'base64'].includes(encoding) ? encoding : 'binary';\n\n            if (charset) {\n                newNode.charset = charset;\n            }\n\n            let decoder = node.getDecoder();\n            decoder.on('end', () => {\n                this.decoderEnded = true;\n            });\n            newNode.decoder = decoder;\n        }\n\n        if (node.root) {\n            this.headers = newNode.headers;\n            this.headerLines = newNode.headerLines;\n        }\n\n        // find location in tree\n\n        if (!this.tree) {\n            newNode.root = true;\n            this.curnode = this.tree = newNode;\n            return newNode;\n        }\n\n        // immediate child of root node\n        if (!this.curnode.parent) {\n            newNode.parent = this.curnode;\n            this.curnode.children.push(newNode);\n            this.curnode = newNode;\n            return newNode;\n        }\n\n        // siblings\n        if (this.curnode.parent.node === node.parentNode) {\n            newNode.parent = this.curnode.parent;\n            this.curnode.parent.children.push(newNode);\n            this.curnode = newNode;\n            return newNode;\n        }\n\n        // first child\n        if (this.curnode.node === node.parentNode) {\n            newNode.parent = this.curnode;\n            this.curnode.children.push(newNode);\n            this.curnode = newNode;\n            return newNode;\n        }\n\n        // move up\n        let parentNode = this.curnode;\n        while ((parentNode = parentNode.parent)) {\n            if (parentNode.node === node.parentNode) {\n                newNode.parent = parentNode;\n                parentNode.children.push(newNode);\n                this.curnode = newNode;\n                return newNode;\n            }\n        }\n\n        // should never happen, can't detect parent\n        this.curnode = newNode;\n        return newNode;\n    }\n\n    getTextContent() {\n        let text = [];\n        let html = [];\n        let processNode = (alternative, level, node) => {\n            if (node.showMeta) {\n                let meta = ['From', 'Subject', 'Date', 'To', 'Cc', 'Bcc']\n                    .map(fkey => {\n                        let key = fkey.toLowerCase();\n                        if (!node.headers.has(key)) {\n                            return false;\n                        }\n                        let value = node.headers.get(key);\n                        if (!value) {\n                            return false;\n                        }\n                        return {\n                            key: fkey,\n                            value: Array.isArray(value) ? value[value.length - 1] : value\n                        };\n                    })\n                    .filter(entry => entry);\n                if (this.hasHtml) {\n                    html.push(\n                        '<table class=\"mp_head\">' +\n                            meta\n                                .map(entry => {\n                                    let value = entry.value;\n                                    switch (entry.key) {\n                                        case 'From':\n                                        case 'To':\n                                        case 'Cc':\n                                        case 'Bcc':\n                                            value = value.html;\n                                            break;\n                                        case 'Date':\n                                            value = this.options.formatDateString ? this.options.formatDateString(value) : value.toUTCString();\n                                            break;\n                                        case 'Subject':\n                                            value = '<strong>' + he.encode(value) + '</strong>';\n                                            break;\n                                        default:\n                                            value = he.encode(value);\n                                    }\n\n                                    return '<tr><td class=\"mp_head_key\">' + he.encode(entry.key) + ':</td><td class=\"mp_head_value\">' + value + '<td></tr>';\n                                })\n                                .join('\\n') +\n                            '<table>'\n                    );\n                }\n                if (this.hasText) {\n                    text.push(\n                        '\\n' +\n                            meta\n                                .map(entry => {\n                                    let value = entry.value;\n                                    switch (entry.key) {\n                                        case 'From':\n                                        case 'To':\n                                        case 'Cc':\n                                        case 'Bcc':\n                                            value = value.text;\n                                            break;\n                                        case 'Date':\n                                            value = this.options.formatDateString ? this.options.formatDateString(value) : value.toUTCString();\n                                            break;\n                                    }\n                                    return entry.key + ': ' + value;\n                                })\n                                .join('\\n') +\n                            '\\n'\n                    );\n                }\n            }\n            if (node.textContent) {\n                if (node.contentType === 'text/plain') {\n                    text.push(node.textContent);\n                    if (!alternative && this.hasHtml) {\n                        html.push(this.textToHtml(node.textContent));\n                    }\n                } else if (node.contentType === 'message/delivery-status' && !this.options.keepDeliveryStatus) {\n                    text.push(node.textContent);\n                    if (!alternative && this.hasHtml) {\n                        html.push(this.textToHtml(node.textContent));\n                    }\n                } else if (node.contentType === 'text/html') {\n                    let failedToParseHtml = false;\n                    if ((!alternative && this.hasText) || (node.root && !this.hasText)) {\n                        if (this.options.skipHtmlToText) {\n                            text.push('');\n                        } else if (node.textContent.length > this.options.maxHtmlLengthToParse) {\n                            this.emit('error', new Error(`HTML too long for parsing ${node.textContent.length} bytes`));\n                            text.push('Invalid HTML content (too long)');\n                            failedToParseHtml = true;\n                        } else {\n                            try {\n                                text.push(htmlToText(node.textContent));\n                            } catch (err) {\n                                this.emit('error', new Error('Failed to parse HTML'));\n                                text.push('Invalid HTML content');\n                                failedToParseHtml = true;\n                            }\n                        }\n                    }\n                    if (!failedToParseHtml) {\n                        html.push(node.textContent);\n                    }\n                }\n            }\n            alternative = alternative || node.contentType === 'multipart/alternative';\n            if (node.children) {\n                node.children.forEach(subNode => {\n                    processNode(alternative, level + 1, subNode);\n                });\n            }\n        };\n\n        processNode(false, 0, this.tree);\n\n        let response = {\n            type: 'text'\n        };\n        if (html.length) {\n            this.html = response.html = html.join('<br/>\\n');\n        }\n        if (text.length) {\n            this.text = response.text = text.join('\\n');\n            this.textAsHtml = response.textAsHtml = text.map(part => this.textToHtml(part)).join('<br/>\\n');\n        }\n        return response;\n    }\n\n    processChunk(data, done) {\n        let partId = null;\n        if (data._parentBoundary) {\n            partId = this._getPartId(data._parentBoundary);\n        }\n        switch (data.type) {\n            case 'node': {\n                let node = this.createNode(data);\n                if (node === this.tree) {\n                    ['subject', 'references', 'date', 'to', 'from', 'to', 'cc', 'bcc', 'message-id', 'in-reply-to', 'reply-to'].forEach(key => {\n                        if (node.headers.has(key)) {\n                            this[key.replace(/-([a-z])/g, (m, c) => c.toUpperCase())] = node.headers.get(key);\n                        }\n                    });\n                    this.emit('headers', node.headers);\n\n                    if (node.headerLines) {\n                        this.emit('headerLines', node.headerLines);\n                    }\n                }\n\n                if (data.contentType === 'message/rfc822' && data.messageNode) {\n                    break;\n                }\n\n                if (data.parentNode && data.parentNode.contentType === 'message/rfc822') {\n                    node.showMeta = true;\n                }\n\n                if (node.isAttachment) {\n                    let contentType = node.contentType;\n                    if (node.contentType === 'application/octet-stream' && data.filename) {\n                        contentType = this.libmime.detectMimeType(data.filename) || 'application/octet-stream';\n                    }\n\n                    let attachment = {\n                        type: 'attachment',\n                        content: null,\n                        contentType,\n                        partId,\n                        release: () => {\n                            attachment.release = null;\n                            if (this.waitUntilAttachmentEnd && typeof this.attachmentCallback === 'function') {\n                                setImmediate(this.attachmentCallback);\n                            }\n                            this.attachmentCallback = false;\n                            this.waitUntilAttachmentEnd = false;\n                        }\n                    };\n\n                    let algo = this.options.checksumAlgo || 'md5';\n                    let hasher = new StreamHash(attachment, algo);\n                    node.decoder.on('error', err => {\n                        hasher.emit('error', err);\n                    });\n\n                    node.decoder.on('readable', () => {\n                        let chunk;\n\n                        while ((chunk = node.decoder.read()) !== null) {\n                            hasher.write(chunk);\n                        }\n                    });\n\n                    node.decoder.once('end', () => {\n                        hasher.end();\n                    });\n\n                    //node.decoder.pipe(hasher);\n                    attachment.content = hasher;\n\n                    this.waitUntilAttachmentEnd = true;\n                    if (data.disposition) {\n                        attachment.contentDisposition = data.disposition;\n                    }\n\n                    if (data.filename) {\n                        attachment.filename = data.filename;\n                    }\n\n                    if (node.headers.has('content-id')) {\n                        attachment.contentId = [].concat(node.headers.get('content-id') || []).shift();\n                        attachment.cid = attachment.contentId.trim().replace(/^<|>$/g, '').trim();\n                        // check if the attachment is \"related\" to text content like an embedded image etc\n                        let parentNode = node;\n                        while ((parentNode = parentNode.parent)) {\n                            if (parentNode.contentType === 'multipart/related') {\n                                attachment.related = true;\n                            }\n                        }\n                    }\n\n                    attachment.headers = node.headers;\n                    this.push(attachment);\n                    this.attachmentList.push(attachment);\n                } else if (node.disposition === 'inline') {\n                    let chunks = [];\n                    let chunklen = 0;\n                    node.contentStream = node.decoder;\n\n                    if (node.contentType === 'text/plain') {\n                        this.hasText = true;\n                    } else if (node.contentType === 'text/html') {\n                        this.hasHtml = true;\n                    } else if (node.contentType === 'message/delivery-status' && !this.options.keepDeliveryStatus) {\n                        this.hasText = true;\n                    }\n\n                    if (node.node.flowed) {\n                        let contentStream = node.contentStream;\n                        let flowDecoder = new FlowedDecoder({\n                            delSp: node.node.delSp\n                        });\n                        contentStream.on('error', err => {\n                            flowDecoder.emit('error', err);\n                        });\n                        contentStream.pipe(flowDecoder);\n                        node.contentStream = flowDecoder;\n                    }\n\n                    let charset = node.charset || 'utf-8';\n                    //charset = charset || 'windows-1257';\n\n                    if (!['ascii', 'usascii', 'utf8'].includes(charset.toLowerCase().replace(/[^a-z0-9]+/g, ''))) {\n                        try {\n                            let contentStream = node.contentStream;\n                            let decodeStream = this.decoder.decodeStream(charset);\n                            contentStream.on('error', err => {\n                                decodeStream.emit('error', err);\n                            });\n                            contentStream.pipe(decodeStream);\n                            node.contentStream = decodeStream;\n                        } catch (E) {\n                            // do not decode charset\n                        }\n                    }\n\n                    node.contentStream.on('readable', () => {\n                        let chunk;\n                        while ((chunk = node.contentStream.read()) !== null) {\n                            if (typeof chunk === 'string') {\n                                chunk = Buffer.from(chunk);\n                            }\n                            chunks.push(chunk);\n                            chunklen += chunk.length;\n                        }\n                    });\n\n                    node.contentStream.once('end', () => {\n                        node.textContent = Buffer.concat(chunks, chunklen).toString().replace(/\\r?\\n/g, '\\n');\n                    });\n\n                    node.contentStream.once('error', err => {\n                        this.emit('error', err);\n                    });\n                }\n\n                break;\n            }\n\n            case 'data':\n                if (this.curnode && this.curnode.decoder) {\n                    this.curnode.decoder.end();\n                }\n\n                if (this.waitUntilAttachmentEnd) {\n                    this.attachmentCallback = done;\n                    return;\n                }\n\n                // multipart message structure\n                // this is not related to any specific 'node' block as it includes\n                // everything between the end of some node body and between the next header\n                //process.stdout.write(data.value);\n                break;\n\n            case 'body':\n                if (this.curnode && this.curnode.decoder && this.curnode.decoder.writable) {\n                    if (this.curnode.decoder.write(data.value) === false) {\n                        return this.curnode.decoder.once('drain', done);\n                    }\n                }\n\n                // Leaf element body. Includes the body for the last 'node' block. You might\n                // have several 'body' calls for a single 'node' block\n                //process.stdout.write(data.value);\n                break;\n        }\n\n        setImmediate(done);\n    }\n\n    _getPartId(parentBoundary) {\n        let boundaryIndex = this.boundaries.findIndex(item => item.name === parentBoundary);\n        if (boundaryIndex === -1) {\n            this.boundaries.push({ name: parentBoundary, count: 1 });\n            boundaryIndex = this.boundaries.length - 1;\n        } else {\n            this.boundaries[boundaryIndex].count++;\n        }\n        let partId = '1';\n        for (let i = 0; i <= boundaryIndex; i++) {\n            if (i === 0) partId = this.boundaries[i].count.toString();\n            else partId += '.' + this.boundaries[i].count.toString();\n        }\n        return partId;\n    }\n\n    getAddressesHTML(value) {\n        let formatSingleLevel = addresses =>\n            addresses\n                .map(address => {\n                    let str = '<span class=\"mp_address_group\">';\n                    if (address.name) {\n                        str += '<span class=\"mp_address_name\">' + he.encode(address.name) + (address.group ? ': ' : '') + '</span>';\n                    }\n                    if (address.address) {\n                        let link = '<a href=\"mailto:' + he.encode(address.address) + '\" class=\"mp_address_email\">' + he.encode(address.address) + '</a>';\n                        if (address.name) {\n                            str += ' &lt;' + link + '&gt;';\n                        } else {\n                            str += link;\n                        }\n                    }\n                    if (address.group) {\n                        str += formatSingleLevel(address.group) + ';';\n                    }\n                    return str + '</span>';\n                })\n                .join(', ');\n        return formatSingleLevel([].concat(value || []));\n    }\n\n    getAddressesText(value) {\n        let formatSingleLevel = addresses =>\n            addresses\n                .map(address => {\n                    let str = '';\n                    if (address.name) {\n                        str += `\"${address.name}\"` + (address.group ? ': ' : '');\n                    }\n                    if (address.address) {\n                        let link = address.address;\n                        if (address.name) {\n                            str += ' <' + link + '>';\n                        } else {\n                            str += link;\n                        }\n                    }\n                    if (address.group) {\n                        str += formatSingleLevel(address.group) + ';';\n                    }\n                    return str;\n                })\n                .join(', ');\n        return formatSingleLevel([].concat(value || []));\n    }\n\n    updateImageLinks(replaceCallback, done) {\n        if (!this.html) {\n            return setImmediate(() => done(null, false));\n        }\n\n        let cids = new Map();\n        let html = (this.html || '').toString();\n\n        if (this.options.skipImageLinks) {\n            return done(null, html);\n        }\n\n        html.replace(/\\bcid:([^'\"\\s]{1,256})/g, (match, cid) => {\n            for (let i = 0, len = this.attachmentList.length; i < len; i++) {\n                if (this.attachmentList[i].cid === cid && /^image\\/[\\w]+$/i.test(this.attachmentList[i].contentType)) {\n                    cids.set(cid, {\n                        attachment: this.attachmentList[i]\n                    });\n                    break;\n                }\n            }\n            return match;\n        });\n\n        let cidList = [];\n        cids.forEach(entry => {\n            cidList.push(entry);\n        });\n\n        let pos = 0;\n        let processNext = () => {\n            if (pos >= cidList.length) {\n                html = html.replace(/\\bcid:([^'\"\\s]{1,256})/g, (match, cid) => {\n                    if (cids.has(cid) && cids.get(cid).url) {\n                        return cids.get(cid).url;\n                    }\n                    return match;\n                });\n\n                return done(null, html);\n            }\n            let entry = cidList[pos++];\n            replaceCallback(entry.attachment, (err, url) => {\n                if (err) {\n                    return setImmediate(() => done(err));\n                }\n                entry.url = url;\n                setImmediate(processNext);\n            });\n        };\n\n        setImmediate(processNext);\n    }\n\n    textToHtml(str) {\n        if (this.options.skipTextToHtml) {\n            return '';\n        }\n        str = (str || '').toString();\n        let encoded;\n\n        let linkified = false;\n        if (!this.options.skipTextLinks) {\n            try {\n                if (linkify.pretest(str)) {\n                    linkified = true;\n                    let links = linkify.match(str) || [];\n                    let result = [];\n                    let last = 0;\n\n                    links.forEach(link => {\n                        if (last < link.index) {\n                            let textPart = he\n                                // encode special chars\n                                .encode(str.slice(last, link.index), {\n                                    useNamedReferences: true\n                                });\n                            result.push(textPart);\n                        }\n\n                        result.push(`<a href=\"${link.url}\">${link.text}</a>`);\n\n                        last = link.lastIndex;\n                    });\n\n                    let textPart = he\n                        // encode special chars\n                        .encode(str.slice(last), {\n                            useNamedReferences: true\n                        });\n                    result.push(textPart);\n\n                    encoded = result.join('');\n                }\n            } catch (E) {\n                // failed, don't linkify\n            }\n        }\n\n        if (!linkified) {\n            encoded = he\n                // encode special chars\n                .encode(str, {\n                    useNamedReferences: true\n                });\n        }\n\n        let text =\n            '<p>' +\n            encoded\n                .replace(/\\r?\\n/g, '\\n')\n                .trim() // normalize line endings\n                .replace(/[ \\t]+$/gm, '')\n                .trim() // trim empty line endings\n                .replace(/\\n\\n+/g, '</p><p>')\n                .trim() // insert <p> to multiple linebreaks\n                .replace(/\\n/g, '<br/>') + // insert <br> to single linebreaks\n            '</p>';\n\n        return text;\n    }\n}\n\nmodule.exports = MailParser;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWFpbHBhcnNlci9saWIvbWFpbC1wYXJzZXIuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsa0JBQWtCLG1CQUFPLENBQUMsa0ZBQVc7QUFDckMsZ0JBQWdCLG1CQUFPLENBQUMsNERBQVM7QUFDakMsc0JBQXNCLG1CQUFPLENBQUMsd0hBQThCO0FBQzVELGtCQUFrQix1REFBMkI7QUFDN0M7QUFDQSxpQkFBaUIsbUJBQU8sQ0FBQyxxRUFBYTtBQUN0QyxzQkFBc0IsbUJBQU8sQ0FBQyxrSEFBOEI7QUFDNUQsbUJBQW1CLG1CQUFPLENBQUMseUVBQWU7QUFDMUMsY0FBYyxtQkFBTyxDQUFDLGdFQUFZO0FBQ2xDLFFBQVEsYUFBYSxFQUFFLG1CQUFPLENBQUMsNEVBQWM7QUFDN0MsV0FBVyxtQkFBTyxDQUFDLHlDQUFJO0FBQ3ZCLGdCQUFnQixtQkFBTyxDQUFDLHNFQUFZO0FBQ3BDLGFBQWEsbUJBQU8sQ0FBQyxrREFBTTtBQUMzQix5QkFBeUIsbUJBQU8sQ0FBQyw4RUFBbUI7O0FBRXBEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLGtEQUFrRDs7QUFFN0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLDBEQUEwRCxLQUFLO0FBQy9EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLENBQUM7O0FBRUQ7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7O0FBRVQ7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7O0FBRUE7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7O0FBRVQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7O0FBRVQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUzs7QUFFVCw2Q0FBNkMsMkJBQTJCO0FBQ3hFOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2IsVUFBVTtBQUNWO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9FQUFvRTtBQUNwRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2REFBNkQ7QUFDN0Q7QUFDQTtBQUNBLDBCQUEwQjtBQUMxQjtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQjtBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0I7QUFDbEI7QUFDQSxrQkFBa0I7QUFDbEI7QUFDQTtBQUNBO0FBQ0EsU0FBUzs7QUFFVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlDQUF5QztBQUN6QztBQUNBLHFCQUFxQjtBQUNyQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBLFNBQVM7O0FBRVQ7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQjtBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQjtBQUNsQjtBQUNBLGtCQUFrQjtBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSx3QkFBd0Isc0JBQXNCO0FBQzlDO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0I7QUFDbEIsb0RBQW9EO0FBQ3BEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTs7QUFFQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsaUNBQWlDO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0I7QUFDbEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0I7QUFDbEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEI7QUFDMUIsc0ZBQXNGLHlCQUF5QjtBQUMvRztBQUNBO0FBQ0EsMEJBQTBCO0FBQzFCO0FBQ0E7QUFDQSw4QkFBOEI7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCOztBQUVyQjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjs7QUFFckI7QUFDQTtBQUNBLHFCQUFxQjs7QUFFckI7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0I7QUFDbEI7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxzQkFBc0I7QUFDdEI7QUFDQSxzQkFBc0I7QUFDdEI7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0EseUJBQXlCO0FBQ3pCO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZCQUE2QjtBQUM3QjtBQUNBO0FBQ0EsMEJBQTBCO0FBQzFCO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7O0FBRXJCO0FBQ0E7QUFDQSxxQkFBcUI7O0FBRXJCO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsbUNBQW1DLGdDQUFnQztBQUNuRTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQSx3QkFBd0Isb0JBQW9CO0FBQzVDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlDQUF5QyxnQkFBZ0I7QUFDekQsMEJBQTBCO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0VBQW9FO0FBQ3BFO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1DQUFtQyxhQUFhO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEI7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvRUFBb0U7QUFDcEU7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUEscUNBQXFDLE1BQU07QUFDM0MsOERBQThELFNBQVM7QUFDdkU7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUzs7QUFFVDtBQUNBO0FBQ0E7QUFDQSxTQUFTOztBQUVUO0FBQ0E7QUFDQTtBQUNBLG9EQUFvRCxNQUFNO0FBQzFEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCOztBQUVqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUM7QUFDakM7QUFDQTs7QUFFQSxnREFBZ0QsU0FBUyxJQUFJLFVBQVU7O0FBRXZFO0FBQ0EscUJBQXFCOztBQUVyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QjtBQUN6Qjs7QUFFQTtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJDOlxcQ29kZVxcb25lLW1haWxcXG5vZGVfbW9kdWxlc1xcbWFpbHBhcnNlclxcbGliXFxtYWlsLXBhcnNlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmNvbnN0IG1haWxzcGxpdCA9IHJlcXVpcmUoJ21haWxzcGxpdCcpO1xuY29uc3QgbGlibWltZSA9IHJlcXVpcmUoJ2xpYm1pbWUnKTtcbmNvbnN0IGFkZHJlc3NwYXJzZXIgPSByZXF1aXJlKCdub2RlbWFpbGVyL2xpYi9hZGRyZXNzcGFyc2VyJyk7XG5jb25zdCBUcmFuc2Zvcm0gPSByZXF1aXJlKCdzdHJlYW0nKS5UcmFuc2Zvcm07XG5jb25zdCBTcGxpdHRlciA9IG1haWxzcGxpdC5TcGxpdHRlcjtcbmNvbnN0IHB1bnljb2RlID0gcmVxdWlyZSgncHVueWNvZGUuanMnKTtcbmNvbnN0IEZsb3dlZERlY29kZXIgPSByZXF1aXJlKCdtYWlsc3BsaXQvbGliL2Zsb3dlZC1kZWNvZGVyJyk7XG5jb25zdCBTdHJlYW1IYXNoID0gcmVxdWlyZSgnLi9zdHJlYW0taGFzaCcpO1xuY29uc3QgaWNvbnYgPSByZXF1aXJlKCdpY29udi1saXRlJyk7XG5jb25zdCB7IGh0bWxUb1RleHQgfSA9IHJlcXVpcmUoJ2h0bWwtdG8tdGV4dCcpO1xuY29uc3QgaGUgPSByZXF1aXJlKCdoZScpO1xuY29uc3QgbGlua2lmeSA9IHJlcXVpcmUoJ2xpbmtpZnktaXQnKSgpO1xuY29uc3QgdGxkcyA9IHJlcXVpcmUoJ3RsZHMnKTtcbmNvbnN0IGVuY29kaW5nSmFwYW5lc2UgPSByZXF1aXJlKCdlbmNvZGluZy1qYXBhbmVzZScpO1xuXG5saW5raWZ5XG4gICAgLnRsZHModGxkcykgLy8gUmVsb2FkIHdpdGggZnVsbCB0bGRzIGxpc3RcbiAgICAudGxkcygnb25pb24nLCB0cnVlKSAvLyBBZGQgdW5vZmZpY2lhbCBgLm9uaW9uYCBkb21haW5cbiAgICAuYWRkKCdnaXQ6JywgJ2h0dHA6JykgLy8gQWRkIGBnaXQ6YCBwdG90b2NvbCBhcyBcImFsaWFzXCJcbiAgICAuYWRkKCdmdHA6JywgbnVsbCkgLy8gRGlzYWJsZSBgZnRwOmAgcHRvdG9jb2xcbiAgICAuc2V0KHsgZnV6enlJUDogdHJ1ZSwgZnV6enlMaW5rOiB0cnVlLCBmdXp6eUVtYWlsOiB0cnVlIH0pO1xuXG4vLyB0d2l0dGVyIGxpbmtpZmllciBmcm9tXG4vLyBodHRwczovL2dpdGh1Yi5jb20vbWFya2Rvd24taXQvbGlua2lmeS1pdCNleGFtcGxlLTItYWRkLXR3aXR0ZXItbWVudGlvbnMtaGFuZGxlclxubGlua2lmeS5hZGQoJ0AnLCB7XG4gICAgdmFsaWRhdGUodGV4dCwgcG9zLCBzZWxmKSB7XG4gICAgICAgIGxldCB0YWlsID0gdGV4dC5zbGljZShwb3MpO1xuXG4gICAgICAgIGlmICghc2VsZi5yZS50d2l0dGVyKSB7XG4gICAgICAgICAgICBzZWxmLnJlLnR3aXR0ZXIgPSBuZXcgUmVnRXhwKCdeKFthLXpBLVowLTlfXSl7MSwxNX0oPyFfKSg/PSR8JyArIHNlbGYucmUuc3JjX1pQQ2MgKyAnKScpO1xuICAgICAgICB9XG4gICAgICAgIGlmIChzZWxmLnJlLnR3aXR0ZXIudGVzdCh0YWlsKSkge1xuICAgICAgICAgICAgLy8gTGlua2lmaWVyIGFsbG93cyBwdW5jdHVhdGlvbiBjaGFycyBiZWZvcmUgcHJlZml4LFxuICAgICAgICAgICAgLy8gYnV0IHdlIGFkZGl0aW9uYWxseSBkaXNhYmxlIGBAYCAoXCJAQG1lbnRpb25cIiBpcyBpbnZhbGlkKVxuICAgICAgICAgICAgaWYgKHBvcyA+PSAyICYmIHRhaWxbcG9zIC0gMl0gPT09ICdAJykge1xuICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiB0YWlsLm1hdGNoKHNlbGYucmUudHdpdHRlcilbMF0ubGVuZ3RoO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiAwO1xuICAgIH0sXG4gICAgbm9ybWFsaXplKG1hdGNoKSB7XG4gICAgICAgIG1hdGNoLnVybCA9ICdodHRwczovL3R3aXR0ZXIuY29tLycgKyBtYXRjaC51cmwucmVwbGFjZSgvXkAvLCAnJyk7XG4gICAgfVxufSk7XG5cbmNsYXNzIEljb252RGVjb2RlciBleHRlbmRzIFRyYW5zZm9ybSB7XG4gICAgY29uc3RydWN0b3IoSWNvbnYsIGNoYXJzZXQpIHtcbiAgICAgICAgc3VwZXIoKTtcblxuICAgICAgICAvLyBJY29udiB0aHJvd3MgZXJyb3Igb24ga3NfY181NjAxLTE5ODcgd2hlbiBpdCBpcyBtYXBwZWQgdG8gRVVDLUtSXG4gICAgICAgIC8vIGh0dHBzOi8vZ2l0aHViLmNvbS9ibm9vcmRodWlzL25vZGUtaWNvbnYvaXNzdWVzLzE2OVxuICAgICAgICBpZiAoY2hhcnNldC50b0xvd2VyQ2FzZSgpID09PSAna3NfY181NjAxLTE5ODcnKSB7XG4gICAgICAgICAgICBjaGFyc2V0ID0gJ0NQOTQ5JztcbiAgICAgICAgfVxuICAgICAgICB0aGlzLnN0cmVhbSA9IG5ldyBJY29udihjaGFyc2V0LCAnVVRGLTgvL1RSQU5TTElULy9JR05PUkUnKTtcblxuICAgICAgICB0aGlzLmlucHV0RW5kZWQgPSBmYWxzZTtcbiAgICAgICAgdGhpcy5lbmRDYiA9IGZhbHNlO1xuXG4gICAgICAgIHRoaXMuc3RyZWFtLm9uKCdlcnJvcicsIGVyciA9PiB0aGlzLmVtaXQoJ2Vycm9yJywgZXJyKSk7XG4gICAgICAgIHRoaXMuc3RyZWFtLm9uKCdkYXRhJywgY2h1bmsgPT4gdGhpcy5wdXNoKGNodW5rKSk7XG4gICAgICAgIHRoaXMuc3RyZWFtLm9uKCdlbmQnLCAoKSA9PiB7XG4gICAgICAgICAgICB0aGlzLmlucHV0RW5kZWQgPSB0cnVlO1xuICAgICAgICAgICAgaWYgKHR5cGVvZiB0aGlzLmVuZENiID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5lbmRDYigpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICB9XG5cbiAgICBfdHJhbnNmb3JtKGNodW5rLCBlbmNvZGluZywgZG9uZSkge1xuICAgICAgICB0aGlzLnN0cmVhbS53cml0ZShjaHVuayk7XG4gICAgICAgIGRvbmUoKTtcbiAgICB9XG5cbiAgICBfZmx1c2goZG9uZSkge1xuICAgICAgICB0aGlzLmVuZENiID0gZG9uZTtcbiAgICAgICAgdGhpcy5zdHJlYW0uZW5kKCk7XG4gICAgfVxufVxuXG5jbGFzcyBKUERlY29kZXIgZXh0ZW5kcyBUcmFuc2Zvcm0ge1xuICAgIGNvbnN0cnVjdG9yKGNoYXJzZXQpIHtcbiAgICAgICAgc3VwZXIoKTtcblxuICAgICAgICB0aGlzLmNoYXJzZXQgPSBjaGFyc2V0O1xuICAgICAgICB0aGlzLmNodW5rcyA9IFtdO1xuICAgICAgICB0aGlzLmNodW5rbGVuID0gMDtcbiAgICB9XG5cbiAgICBfdHJhbnNmb3JtKGNodW5rLCBlbmNvZGluZywgZG9uZSkge1xuICAgICAgICBpZiAodHlwZW9mIGNodW5rID09PSAnc3RyaW5nJykge1xuICAgICAgICAgICAgY2h1bmsgPSBCdWZmZXIuZnJvbShjaHVuaywgZW5jb2RpbmcpO1xuICAgICAgICB9XG5cbiAgICAgICAgdGhpcy5jaHVua3MucHVzaChjaHVuayk7XG4gICAgICAgIHRoaXMuY2h1bmtsZW4gKz0gY2h1bmsubGVuZ3RoO1xuICAgICAgICBkb25lKCk7XG4gICAgfVxuXG4gICAgX2ZsdXNoKGRvbmUpIHtcbiAgICAgICAgbGV0IGlucHV0ID0gQnVmZmVyLmNvbmNhdCh0aGlzLmNodW5rcywgdGhpcy5jaHVua2xlbik7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICBsZXQgb3V0cHV0ID0gZW5jb2RpbmdKYXBhbmVzZS5jb252ZXJ0KGlucHV0LCB7XG4gICAgICAgICAgICAgICAgdG86ICdVTklDT0RFJywgLy8gdG9fZW5jb2RpbmdcbiAgICAgICAgICAgICAgICBmcm9tOiB0aGlzLmNoYXJzZXQsIC8vIGZyb21fZW5jb2RpbmdcbiAgICAgICAgICAgICAgICB0eXBlOiAnc3RyaW5nJ1xuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBpZiAodHlwZW9mIG91dHB1dCA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgICAgICAgICBvdXRwdXQgPSBCdWZmZXIuZnJvbShvdXRwdXQpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdGhpcy5wdXNoKG91dHB1dCk7XG4gICAgICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgICAgICAgLy8ga2VlcCBhcyBpcyBvbiBlcnJvcnNcbiAgICAgICAgICAgIHRoaXMucHVzaChpbnB1dCk7XG4gICAgICAgIH1cblxuICAgICAgICBkb25lKCk7XG4gICAgfVxufVxuXG5jbGFzcyBNYWlsUGFyc2VyIGV4dGVuZHMgVHJhbnNmb3JtIHtcbiAgICBjb25zdHJ1Y3Rvcihjb25maWcpIHtcbiAgICAgICAgc3VwZXIoe1xuICAgICAgICAgICAgcmVhZGFibGVPYmplY3RNb2RlOiB0cnVlLFxuICAgICAgICAgICAgd3JpdGFibGVPYmplY3RNb2RlOiBmYWxzZVxuICAgICAgICB9KTtcblxuICAgICAgICB0aGlzLm9wdGlvbnMgPSBjb25maWcgfHwge307XG4gICAgICAgIHRoaXMuc3BsaXR0ZXIgPSBuZXcgU3BsaXR0ZXIoY29uZmlnKTtcbiAgICAgICAgdGhpcy5maW5pc2hlZCA9IGZhbHNlO1xuICAgICAgICB0aGlzLndhaXRpbmdFbmQgPSBmYWxzZTtcblxuICAgICAgICB0aGlzLmhlYWRlcnMgPSBmYWxzZTtcbiAgICAgICAgdGhpcy5oZWFkZXJMaW5lcyA9IGZhbHNlO1xuXG4gICAgICAgIHRoaXMuZW5kUmVjZWl2ZWQgPSBmYWxzZTtcbiAgICAgICAgdGhpcy5yZWFkaW5nID0gZmFsc2U7XG4gICAgICAgIHRoaXMuaGFzRmFpbGVkID0gZmFsc2U7XG5cbiAgICAgICAgdGhpcy50cmVlID0gZmFsc2U7XG4gICAgICAgIHRoaXMuY3Vybm9kZSA9IGZhbHNlO1xuICAgICAgICB0aGlzLndhaXRVbnRpbEF0dGFjaG1lbnRFbmQgPSBmYWxzZTtcbiAgICAgICAgdGhpcy5hdHRhY2htZW50Q2FsbGJhY2sgPSBmYWxzZTtcblxuICAgICAgICB0aGlzLmhhc0h0bWwgPSBmYWxzZTtcbiAgICAgICAgdGhpcy5oYXNUZXh0ID0gZmFsc2U7XG5cbiAgICAgICAgdGhpcy50ZXh0ID0gZmFsc2U7XG4gICAgICAgIHRoaXMuaHRtbCA9IGZhbHNlO1xuICAgICAgICB0aGlzLnRleHRBc0h0bWwgPSBmYWxzZTtcblxuICAgICAgICB0aGlzLmF0dGFjaG1lbnRMaXN0ID0gW107XG5cbiAgICAgICAgdGhpcy5ib3VuZGFyaWVzID0gW107XG5cbiAgICAgICAgdGhpcy50ZXh0VHlwZXMgPSBbJ3RleHQvcGxhaW4nLCAndGV4dC9odG1sJ10uY29uY2F0KCF0aGlzLm9wdGlvbnMua2VlcERlbGl2ZXJ5U3RhdHVzID8gJ21lc3NhZ2UvZGVsaXZlcnktc3RhdHVzJyA6IFtdKTtcblxuICAgICAgICB0aGlzLmRlY29kZXIgPSB0aGlzLmdldERlY29kZXIoKTtcblxuICAgICAgICB0aGlzLnNwbGl0dGVyLm9uKCdyZWFkYWJsZScsICgpID0+IHtcbiAgICAgICAgICAgIGlmICh0aGlzLnJlYWRpbmcpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0aGlzLnJlYWREYXRhKCk7XG4gICAgICAgIH0pO1xuXG4gICAgICAgIHRoaXMuc3BsaXR0ZXIub24oJ2VuZCcsICgpID0+IHtcbiAgICAgICAgICAgIHRoaXMuZW5kUmVjZWl2ZWQgPSB0cnVlO1xuICAgICAgICAgICAgaWYgKCF0aGlzLnJlYWRpbmcpIHtcbiAgICAgICAgICAgICAgICB0aGlzLmVuZFN0cmVhbSgpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9KTtcblxuICAgICAgICB0aGlzLnNwbGl0dGVyLm9uKCdlcnJvcicsIGVyciA9PiB7XG4gICAgICAgICAgICB0aGlzLmhhc0ZhaWxlZCA9IHRydWU7XG4gICAgICAgICAgICBpZiAodHlwZW9mIHRoaXMud2FpdGluZ0VuZCA9PT0gJ2Z1bmN0aW9uJykge1xuICAgICAgICAgICAgICAgIHJldHVybiB0aGlzLndhaXRpbmdFbmQoZXJyKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHRoaXMuZW1pdCgnZXJyb3InLCBlcnIpO1xuICAgICAgICB9KTtcblxuICAgICAgICB0aGlzLmxpYm1pbWUgPSBuZXcgbGlibWltZS5MaWJtaW1lKHsgSWNvbnY6IHRoaXMub3B0aW9ucy5JY29udiB9KTtcbiAgICB9XG5cbiAgICBnZXREZWNvZGVyKCkge1xuICAgICAgICBpZiAodGhpcy5vcHRpb25zLkljb252KSB7XG4gICAgICAgICAgICBjb25zdCBJY29udiA9IHRoaXMub3B0aW9ucy5JY29udjtcbiAgICAgICAgICAgIC8vIGNyZWF0ZSB3cmFwcGVyXG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIGRlY29kZVN0cmVhbShjaGFyc2V0KSB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBuZXcgSWNvbnZEZWNvZGVyKEljb252LCBjaGFyc2V0KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9O1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBkZWNvZGVTdHJlYW0oY2hhcnNldCkge1xuICAgICAgICAgICAgICAgICAgICBjaGFyc2V0ID0gKGNoYXJzZXQgfHwgJ2FzY2lpJykudG9TdHJpbmcoKS50cmltKCkudG9Mb3dlckNhc2UoKTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKC9eamlzfF5pc28tPzIwMjItP2pwfF5FVUNKUC9pLnRlc3QoY2hhcnNldCkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIHNwZWNpYWwgY2FzZSBub3Qgc3VwcG9ydGVkIGJ5IGljb252LWxpdGVcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBuZXcgSlBEZWNvZGVyKGNoYXJzZXQpO1xuICAgICAgICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGljb252LmRlY29kZVN0cmVhbShjaGFyc2V0KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgcmVhZERhdGEoKSB7XG4gICAgICAgIGlmICh0aGlzLmhhc0ZhaWxlZCkge1xuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMucmVhZGluZyA9IHRydWU7XG4gICAgICAgIGxldCBkYXRhID0gdGhpcy5zcGxpdHRlci5yZWFkKCk7XG4gICAgICAgIGlmIChkYXRhID09PSBudWxsKSB7XG4gICAgICAgICAgICB0aGlzLnJlYWRpbmcgPSBmYWxzZTtcbiAgICAgICAgICAgIGlmICh0aGlzLmVuZFJlY2VpdmVkKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5lbmRTdHJlYW0oKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuXG4gICAgICAgIHRoaXMucHJvY2Vzc0NodW5rKGRhdGEsIGVyciA9PiB7XG4gICAgICAgICAgICBpZiAoZXJyKSB7XG4gICAgICAgICAgICAgICAgaWYgKHR5cGVvZiB0aGlzLndhaXRpbmdFbmQgPT09ICdmdW5jdGlvbicpIHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRoaXMud2FpdGluZ0VuZChlcnIpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICByZXR1cm4gdGhpcy5lbWl0KCdlcnJvcicsIGVycik7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBzZXRJbW1lZGlhdGUoKCkgPT4gdGhpcy5yZWFkRGF0YSgpKTtcbiAgICAgICAgfSk7XG4gICAgfVxuXG4gICAgZW5kU3RyZWFtKCkge1xuICAgICAgICB0aGlzLmZpbmlzaGVkID0gdHJ1ZTtcblxuICAgICAgICBpZiAodGhpcy5jdXJub2RlICYmIHRoaXMuY3Vybm9kZS5kZWNvZGVyKSB7XG4gICAgICAgICAgICB0aGlzLmN1cm5vZGUuZGVjb2Rlci5lbmQoKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHRoaXMud2FpdGluZ0VuZCA9PT0gJ2Z1bmN0aW9uJykge1xuICAgICAgICAgICAgdGhpcy53YWl0aW5nRW5kKCk7XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICBfdHJhbnNmb3JtKGNodW5rLCBlbmNvZGluZywgZG9uZSkge1xuICAgICAgICBpZiAoIWNodW5rIHx8ICFjaHVuay5sZW5ndGgpIHtcbiAgICAgICAgICAgIHJldHVybiBkb25lKCk7XG4gICAgICAgIH1cblxuICAgICAgICBpZiAodGhpcy5zcGxpdHRlci53cml0ZShjaHVuaykgPT09IGZhbHNlKSB7XG4gICAgICAgICAgICByZXR1cm4gdGhpcy5zcGxpdHRlci5vbmNlKCdkcmFpbicsICgpID0+IHtcbiAgICAgICAgICAgICAgICBkb25lKCk7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIHJldHVybiBkb25lKCk7XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICBfZmx1c2goZG9uZSkge1xuICAgICAgICBzZXRJbW1lZGlhdGUoKCkgPT4gdGhpcy5zcGxpdHRlci5lbmQoKSk7XG4gICAgICAgIGlmICh0aGlzLmZpbmlzaGVkKSB7XG4gICAgICAgICAgICByZXR1cm4gdGhpcy5jbGVhbnVwKGRvbmUpO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMud2FpdGluZ0VuZCA9ICgpID0+IHtcbiAgICAgICAgICAgIHRoaXMuY2xlYW51cCgoKSA9PiB7XG4gICAgICAgICAgICAgICAgZG9uZSgpO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgIH07XG4gICAgfVxuXG4gICAgY2xlYW51cChkb25lKSB7XG4gICAgICAgIGxldCBmaW5pc2ggPSAoKSA9PiB7XG4gICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgIGxldCB0ID0gdGhpcy5nZXRUZXh0Q29udGVudCgpO1xuICAgICAgICAgICAgICAgIHRoaXMucHVzaCh0KTtcbiAgICAgICAgICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgICAgICAgICAgIHJldHVybiB0aGlzLmVtaXQoJ2Vycm9yJywgZXJyKTtcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgZG9uZSgpO1xuICAgICAgICB9O1xuXG4gICAgICAgIGlmICh0aGlzLmN1cm5vZGUgJiYgdGhpcy5jdXJub2RlLmRlY29kZXIgJiYgdGhpcy5jdXJub2RlLmRlY29kZXIucmVhZGFibGUgJiYgIXRoaXMuZGVjb2RlckVuZGVkKSB7XG4gICAgICAgICAgICAodGhpcy5jdXJub2RlLmNvbnRlbnRTdHJlYW0gfHwgdGhpcy5jdXJub2RlLmRlY29kZXIpLm9uY2UoJ2VuZCcsICgpID0+IHtcbiAgICAgICAgICAgICAgICBmaW5pc2goKTtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgdGhpcy5jdXJub2RlLmRlY29kZXIuZW5kKCk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBzZXRJbW1lZGlhdGUoKCkgPT4ge1xuICAgICAgICAgICAgICAgIGZpbmlzaCgpO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICBwcm9jZXNzSGVhZGVycyhsaW5lcykge1xuICAgICAgICBsZXQgaGVhZGVycyA9IG5ldyBNYXAoKTtcbiAgICAgICAgKGxpbmVzIHx8IFtdKS5mb3JFYWNoKGxpbmUgPT4ge1xuICAgICAgICAgICAgbGV0IGtleSA9IGxpbmUua2V5O1xuICAgICAgICAgICAgbGV0IHZhbHVlID0gKCh0aGlzLmxpYm1pbWUuZGVjb2RlSGVhZGVyKGxpbmUubGluZSkgfHwge30pLnZhbHVlIHx8ICcnKS50b1N0cmluZygpLnRyaW0oKTtcbiAgICAgICAgICAgIHZhbHVlID0gQnVmZmVyLmZyb20odmFsdWUsICdiaW5hcnknKS50b1N0cmluZygpO1xuICAgICAgICAgICAgc3dpdGNoIChrZXkpIHtcbiAgICAgICAgICAgICAgICBjYXNlICdjb250ZW50LXR5cGUnOlxuICAgICAgICAgICAgICAgIGNhc2UgJ2NvbnRlbnQtZGlzcG9zaXRpb24nOlxuICAgICAgICAgICAgICAgIGNhc2UgJ2RraW0tc2lnbmF0dXJlJzpcbiAgICAgICAgICAgICAgICAgICAgdmFsdWUgPSB0aGlzLmxpYm1pbWUucGFyc2VIZWFkZXJWYWx1ZSh2YWx1ZSk7XG4gICAgICAgICAgICAgICAgICAgIGlmICh2YWx1ZS52YWx1ZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWUudmFsdWUgPSB0aGlzLmxpYm1pbWUuZGVjb2RlV29yZHModmFsdWUudmFsdWUpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIE9iamVjdC5rZXlzKCh2YWx1ZSAmJiB2YWx1ZS5wYXJhbXMpIHx8IHt9KS5mb3JFYWNoKGtleSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlLnBhcmFtc1trZXldID0gdGhpcy5saWJtaW1lLmRlY29kZVdvcmRzKHZhbHVlLnBhcmFtc1trZXldKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH0gY2F0Y2ggKEUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBpZ25vcmUsIGtlZXAgYXMgaXNcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIGNhc2UgJ2RhdGUnOiB7XG4gICAgICAgICAgICAgICAgICAgIGxldCBkYXRlVmFsdWUgPSBuZXcgRGF0ZSh2YWx1ZSk7XG4gICAgICAgICAgICAgICAgICAgIGlmIChpc05hTihkYXRlVmFsdWUpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBkYXRlIHBhcnNpbmcgZmFpbGVkIDpTXG4gICAgICAgICAgICAgICAgICAgICAgICBkYXRlVmFsdWUgPSBuZXcgRGF0ZSgpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIHZhbHVlID0gZGF0ZVZhbHVlO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY2FzZSAnc3ViamVjdCc6XG4gICAgICAgICAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZSA9IHRoaXMubGlibWltZS5kZWNvZGVXb3Jkcyh2YWx1ZSk7XG4gICAgICAgICAgICAgICAgICAgIH0gY2F0Y2ggKEUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIGlnbm9yZSwga2VlcCBhcyBpc1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIGNhc2UgJ3JlZmVyZW5jZXMnOlxuICAgICAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWUgPSB0aGlzLmxpYm1pbWUuZGVjb2RlV29yZHModmFsdWUpO1xuICAgICAgICAgICAgICAgICAgICB9IGNhdGNoIChFKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBpZ25vcmVcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB2YWx1ZSA9IHZhbHVlLnNwbGl0KC9cXHMrLykubWFwKHRoaXMuZW5zdXJlTWVzc2FnZUlERm9ybWF0KTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgY2FzZSAnbWVzc2FnZS1pZCc6XG4gICAgICAgICAgICAgICAgY2FzZSAnaW4tcmVwbHktdG8nOlxuICAgICAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWUgPSB0aGlzLmxpYm1pbWUuZGVjb2RlV29yZHModmFsdWUpO1xuICAgICAgICAgICAgICAgICAgICB9IGNhdGNoIChFKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBpZ25vcmVcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB2YWx1ZSA9IHRoaXMuZW5zdXJlTWVzc2FnZUlERm9ybWF0KHZhbHVlKTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgY2FzZSAncHJpb3JpdHknOlxuICAgICAgICAgICAgICAgIGNhc2UgJ3gtcHJpb3JpdHknOlxuICAgICAgICAgICAgICAgIGNhc2UgJ3gtbXNtYWlsLXByaW9yaXR5JzpcbiAgICAgICAgICAgICAgICBjYXNlICdpbXBvcnRhbmNlJzpcbiAgICAgICAgICAgICAgICAgICAga2V5ID0gJ3ByaW9yaXR5JztcbiAgICAgICAgICAgICAgICAgICAgdmFsdWUgPSB0aGlzLnBhcnNlUHJpb3JpdHkodmFsdWUpO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICBjYXNlICdmcm9tJzpcbiAgICAgICAgICAgICAgICBjYXNlICd0byc6XG4gICAgICAgICAgICAgICAgY2FzZSAnY2MnOlxuICAgICAgICAgICAgICAgIGNhc2UgJ2JjYyc6XG4gICAgICAgICAgICAgICAgY2FzZSAnc2VuZGVyJzpcbiAgICAgICAgICAgICAgICBjYXNlICdyZXBseS10byc6XG4gICAgICAgICAgICAgICAgY2FzZSAnZGVsaXZlcmVkLXRvJzpcbiAgICAgICAgICAgICAgICBjYXNlICdyZXR1cm4tcGF0aCc6XG4gICAgICAgICAgICAgICAgY2FzZSAnZGlzcG9zaXRpb24tbm90aWZpY2F0aW9uLXRvJzpcbiAgICAgICAgICAgICAgICAgICAgdmFsdWUgPSBhZGRyZXNzcGFyc2VyKHZhbHVlKTtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5kZWNvZGVBZGRyZXNzZXModmFsdWUpO1xuICAgICAgICAgICAgICAgICAgICB2YWx1ZSA9IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlLFxuICAgICAgICAgICAgICAgICAgICAgICAgaHRtbDogdGhpcy5nZXRBZGRyZXNzZXNIVE1MKHZhbHVlKSxcbiAgICAgICAgICAgICAgICAgICAgICAgIHRleHQ6IHRoaXMuZ2V0QWRkcmVzc2VzVGV4dCh2YWx1ZSlcbiAgICAgICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIC8vIGhhbmRsZSBsaXN0LSoga2V5c1xuICAgICAgICAgICAgaWYgKGtleS5zdWJzdHIoMCwgNSkgPT09ICdsaXN0LScpIHtcbiAgICAgICAgICAgICAgICB2YWx1ZSA9IHRoaXMucGFyc2VMaXN0SGVhZGVyKGtleS5zdWJzdHIoNSksIHZhbHVlKTtcbiAgICAgICAgICAgICAgICBrZXkgPSAnbGlzdCc7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIGlmICh2YWx1ZSkge1xuICAgICAgICAgICAgICAgIGlmICghaGVhZGVycy5oYXMoa2V5KSkge1xuICAgICAgICAgICAgICAgICAgICBoZWFkZXJzLnNldChrZXksIFtdLmNvbmNhdCh2YWx1ZSB8fCBbXSkpO1xuICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAoQXJyYXkuaXNBcnJheSh2YWx1ZSkpIHtcbiAgICAgICAgICAgICAgICAgICAgaGVhZGVycy5zZXQoa2V5LCBoZWFkZXJzLmdldChrZXkpLmNvbmNhdCh2YWx1ZSkpO1xuICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIGhlYWRlcnMuZ2V0KGtleSkucHVzaCh2YWx1ZSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9KTtcblxuICAgICAgICAvLyBrZWVwIG9ubHkgdGhlIGZpcnN0IHZhbHVlXG4gICAgICAgIGxldCBzaW5nbGVLZXlzID0gW1xuICAgICAgICAgICAgJ21lc3NhZ2UtaWQnLFxuICAgICAgICAgICAgJ2NvbnRlbnQtaWQnLFxuICAgICAgICAgICAgJ2Zyb20nLFxuICAgICAgICAgICAgJ3NlbmRlcicsXG4gICAgICAgICAgICAnaW4tcmVwbHktdG8nLFxuICAgICAgICAgICAgJ3JlcGx5LXRvJyxcbiAgICAgICAgICAgICdzdWJqZWN0JyxcbiAgICAgICAgICAgICdkYXRlJyxcbiAgICAgICAgICAgICdjb250ZW50LWRpc3Bvc2l0aW9uJyxcbiAgICAgICAgICAgICdjb250ZW50LXR5cGUnLFxuICAgICAgICAgICAgJ2NvbnRlbnQtdHJhbnNmZXItZW5jb2RpbmcnLFxuICAgICAgICAgICAgJ3ByaW9yaXR5JyxcbiAgICAgICAgICAgICdtaW1lLXZlcnNpb24nLFxuICAgICAgICAgICAgJ2NvbnRlbnQtZGVzY3JpcHRpb24nLFxuICAgICAgICAgICAgJ3ByZWNlZGVuY2UnLFxuICAgICAgICAgICAgJ2Vycm9ycy10bycsXG4gICAgICAgICAgICAnZGlzcG9zaXRpb24tbm90aWZpY2F0aW9uLXRvJ1xuICAgICAgICBdO1xuXG4gICAgICAgIGhlYWRlcnMuZm9yRWFjaCgodmFsdWUsIGtleSkgPT4ge1xuICAgICAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkodmFsdWUpKSB7XG4gICAgICAgICAgICAgICAgaWYgKHNpbmdsZUtleXMuaW5jbHVkZXMoa2V5KSAmJiB2YWx1ZS5sZW5ndGgpIHtcbiAgICAgICAgICAgICAgICAgICAgaGVhZGVycy5zZXQoa2V5LCB2YWx1ZVt2YWx1ZS5sZW5ndGggLSAxXSk7XG4gICAgICAgICAgICAgICAgfSBlbHNlIGlmICh2YWx1ZS5sZW5ndGggPT09IDEpIHtcbiAgICAgICAgICAgICAgICAgICAgaGVhZGVycy5zZXQoa2V5LCB2YWx1ZVswXSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICBpZiAoa2V5ID09PSAnbGlzdCcpIHtcbiAgICAgICAgICAgICAgICAvLyBub3JtYWxpemUgTGlzdC0qIGhlYWRlcnNcbiAgICAgICAgICAgICAgICBsZXQgbGlzdFZhbHVlID0ge307XG4gICAgICAgICAgICAgICAgW10uY29uY2F0KHZhbHVlIHx8IFtdKS5mb3JFYWNoKHZhbCA9PiB7XG4gICAgICAgICAgICAgICAgICAgIE9iamVjdC5rZXlzKHZhbCB8fCB7fSkuZm9yRWFjaChsaXN0S2V5ID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGxpc3RWYWx1ZVtsaXN0S2V5XSA9IHZhbFtsaXN0S2V5XTtcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgaGVhZGVycy5zZXQoa2V5LCBsaXN0VmFsdWUpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9KTtcblxuICAgICAgICByZXR1cm4gaGVhZGVycztcbiAgICB9XG5cbiAgICBwYXJzZUxpc3RIZWFkZXIoa2V5LCB2YWx1ZSkge1xuICAgICAgICBsZXQgYWRkcmVzc2VzID0gYWRkcmVzc3BhcnNlcih2YWx1ZSk7XG4gICAgICAgIGxldCByZXNwb25zZSA9IHt9O1xuICAgICAgICBsZXQgZGF0YSA9IGFkZHJlc3Nlc1xuICAgICAgICAgICAgLm1hcChhZGRyZXNzID0+IHtcbiAgICAgICAgICAgICAgICBpZiAoL15odHRwcz86L2kudGVzdChhZGRyZXNzLm5hbWUpKSB7XG4gICAgICAgICAgICAgICAgICAgIHJlc3BvbnNlLnVybCA9IGFkZHJlc3MubmFtZTtcbiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKGFkZHJlc3MubmFtZSkge1xuICAgICAgICAgICAgICAgICAgICByZXNwb25zZS5uYW1lID0gYWRkcmVzcy5uYW1lO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAoL15tYWlsdG86Ly50ZXN0KGFkZHJlc3MuYWRkcmVzcykpIHtcbiAgICAgICAgICAgICAgICAgICAgcmVzcG9uc2UubWFpbCA9IGFkZHJlc3MuYWRkcmVzcy5zdWJzdHIoNyk7XG4gICAgICAgICAgICAgICAgfSBlbHNlIGlmIChhZGRyZXNzLmFkZHJlc3MgJiYgYWRkcmVzcy5hZGRyZXNzLmluZGV4T2YoJ0AnKSA8IDApIHtcbiAgICAgICAgICAgICAgICAgICAgcmVzcG9uc2UuaWQgPSBhZGRyZXNzLmFkZHJlc3M7XG4gICAgICAgICAgICAgICAgfSBlbHNlIGlmIChhZGRyZXNzLmFkZHJlc3MpIHtcbiAgICAgICAgICAgICAgICAgICAgcmVzcG9uc2UubWFpbCA9IGFkZHJlc3MuYWRkcmVzcztcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKE9iamVjdC5rZXlzKHJlc3BvbnNlKS5sZW5ndGgpIHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHJlc3BvbnNlO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgICAgICB9KVxuICAgICAgICAgICAgLmZpbHRlcihhZGRyZXNzID0+IGFkZHJlc3MpO1xuICAgICAgICBpZiAoZGF0YS5sZW5ndGgpIHtcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgW2tleV06IHJlc3BvbnNlXG4gICAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG5cbiAgICBwYXJzZVByaW9yaXR5KHZhbHVlKSB7XG4gICAgICAgIHZhbHVlID0gdmFsdWUudG9Mb3dlckNhc2UoKS50cmltKCk7XG4gICAgICAgIGlmICghaXNOYU4ocGFyc2VJbnQodmFsdWUsIDEwKSkpIHtcbiAgICAgICAgICAgIC8vIHN1cHBvcnQgXCJYLVByaW9yaXR5OiAxIChIaWdoZXN0KVwiXG4gICAgICAgICAgICB2YWx1ZSA9IHBhcnNlSW50KHZhbHVlLCAxMCkgfHwgMDtcbiAgICAgICAgICAgIGlmICh2YWx1ZSA9PT0gMykge1xuICAgICAgICAgICAgICAgIHJldHVybiAnbm9ybWFsJztcbiAgICAgICAgICAgIH0gZWxzZSBpZiAodmFsdWUgPiAzKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuICdsb3cnO1xuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gJ2hpZ2gnO1xuICAgICAgICAgICAgfVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgc3dpdGNoICh2YWx1ZSkge1xuICAgICAgICAgICAgICAgIGNhc2UgJ25vbi11cmdlbnQnOlxuICAgICAgICAgICAgICAgIGNhc2UgJ2xvdyc6XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiAnbG93JztcbiAgICAgICAgICAgICAgICBjYXNlICd1cmdlbnQnOlxuICAgICAgICAgICAgICAgIGNhc2UgJ2hpZ2gnOlxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gJ2hpZ2gnO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIHJldHVybiAnbm9ybWFsJztcbiAgICB9XG5cbiAgICBlbnN1cmVNZXNzYWdlSURGb3JtYXQodmFsdWUpIHtcbiAgICAgICAgaWYgKCF2YWx1ZS5sZW5ndGgpIHtcbiAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgfVxuXG4gICAgICAgIGlmICh2YWx1ZS5jaGFyQXQoMCkgIT09ICc8Jykge1xuICAgICAgICAgICAgdmFsdWUgPSAnPCcgKyB2YWx1ZTtcbiAgICAgICAgfVxuXG4gICAgICAgIGlmICh2YWx1ZS5jaGFyQXQodmFsdWUubGVuZ3RoIC0gMSkgIT09ICc+Jykge1xuICAgICAgICAgICAgdmFsdWUgKz0gJz4nO1xuICAgICAgICB9XG5cbiAgICAgICAgcmV0dXJuIHZhbHVlO1xuICAgIH1cblxuICAgIGRlY29kZUFkZHJlc3NlcyhhZGRyZXNzZXMpIHtcbiAgICAgICAgbGV0IHByb2Nlc3NlZEFkZHJlc3MgPSBuZXcgV2Vha1NldCgpO1xuICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGFkZHJlc3Nlcy5sZW5ndGg7IGkrKykge1xuICAgICAgICAgICAgbGV0IGFkZHJlc3MgPSBhZGRyZXNzZXNbaV07XG4gICAgICAgICAgICBhZGRyZXNzLm5hbWUgPSAoYWRkcmVzcy5uYW1lIHx8ICcnKS50b1N0cmluZygpLnRyaW0oKTtcblxuICAgICAgICAgICAgaWYgKCFhZGRyZXNzLmFkZHJlc3MgJiYgL14oPVxcPyhbXj9dKylcXD9bQmJdXFw/W14/XSpcXD89KShcXHMqPVxcPyhbXj9dKylcXD9bQmJdXFw/W14/XSpcXD89KSokLy50ZXN0KGFkZHJlc3MubmFtZSkgJiYgIXByb2Nlc3NlZEFkZHJlc3MuaGFzKGFkZHJlc3MpKSB7XG4gICAgICAgICAgICAgICAgbGV0IHBhcnNlZCA9IGFkZHJlc3NwYXJzZXIodGhpcy5saWJtaW1lLmRlY29kZVdvcmRzKGFkZHJlc3MubmFtZSkpO1xuICAgICAgICAgICAgICAgIGlmIChwYXJzZWQubGVuZ3RoKSB7XG4gICAgICAgICAgICAgICAgICAgIHBhcnNlZC5mb3JFYWNoKGVudHJ5ID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHByb2Nlc3NlZEFkZHJlc3MuYWRkKGVudHJ5KTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGFkZHJlc3Nlcy5wdXNoKGVudHJ5KTtcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAgICAgLy8gcmVtb3ZlIGN1cnJlbnQgZWxlbWVudFxuICAgICAgICAgICAgICAgIGFkZHJlc3Nlcy5zcGxpY2UoaSwgMSk7XG4gICAgICAgICAgICAgICAgaS0tO1xuICAgICAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICBpZiAoYWRkcmVzcy5uYW1lKSB7XG4gICAgICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICAgICAgYWRkcmVzcy5uYW1lID0gdGhpcy5saWJtaW1lLmRlY29kZVdvcmRzKGFkZHJlc3MubmFtZSk7XG4gICAgICAgICAgICAgICAgfSBjYXRjaCAoRSkge1xuICAgICAgICAgICAgICAgICAgICAvL2lnbm9yZSwga2VlcCBhcyBpc1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICgvQHhuLS0vLnRlc3QoYWRkcmVzcy5hZGRyZXNzKSkge1xuICAgICAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgICAgIGFkZHJlc3MuYWRkcmVzcyA9XG4gICAgICAgICAgICAgICAgICAgICAgICBhZGRyZXNzLmFkZHJlc3Muc3Vic3RyKDAsIGFkZHJlc3MuYWRkcmVzcy5sYXN0SW5kZXhPZignQCcpICsgMSkgK1xuICAgICAgICAgICAgICAgICAgICAgICAgcHVueWNvZGUudG9Vbmljb2RlKGFkZHJlc3MuYWRkcmVzcy5zdWJzdHIoYWRkcmVzcy5hZGRyZXNzLmxhc3RJbmRleE9mKCdAJykgKyAxKSk7XG4gICAgICAgICAgICAgICAgfSBjYXRjaCAoRSkge1xuICAgICAgICAgICAgICAgICAgICAvLyBOb3QgYSB2YWxpZCBwdW55Y29kZSBzdHJpbmc7IGtlZXAgYXMgaXNcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoYWRkcmVzcy5ncm91cCkge1xuICAgICAgICAgICAgICAgIHRoaXMuZGVjb2RlQWRkcmVzc2VzKGFkZHJlc3MuZ3JvdXApO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgY3JlYXRlTm9kZShub2RlKSB7XG4gICAgICAgIGxldCBjb250ZW50VHlwZSA9IG5vZGUuY29udGVudFR5cGU7XG4gICAgICAgIGxldCBkaXNwb3NpdGlvbiA9IG5vZGUuZGlzcG9zaXRpb247XG4gICAgICAgIGxldCBlbmNvZGluZyA9IG5vZGUuZW5jb2Rpbmc7XG4gICAgICAgIGxldCBjaGFyc2V0ID0gbm9kZS5jaGFyc2V0O1xuXG4gICAgICAgIGlmICghY29udGVudFR5cGUgJiYgbm9kZS5yb290KSB7XG4gICAgICAgICAgICBjb250ZW50VHlwZSA9ICd0ZXh0L3BsYWluJztcbiAgICAgICAgfVxuXG4gICAgICAgIGxldCBuZXdOb2RlID0ge1xuICAgICAgICAgICAgbm9kZSxcbiAgICAgICAgICAgIGhlYWRlckxpbmVzOiBub2RlLmhlYWRlcnMubGluZXMsXG4gICAgICAgICAgICBoZWFkZXJzOiB0aGlzLnByb2Nlc3NIZWFkZXJzKG5vZGUuaGVhZGVycy5nZXRMaXN0KCkpLFxuICAgICAgICAgICAgY29udGVudFR5cGUsXG4gICAgICAgICAgICBjaGlsZHJlbjogW11cbiAgICAgICAgfTtcblxuICAgICAgICBpZiAoIS9ebXVsdGlwYXJ0XFwvL2kudGVzdChjb250ZW50VHlwZSkpIHtcbiAgICAgICAgICAgIGlmIChkaXNwb3NpdGlvbiAmJiAhWydhdHRhY2htZW50JywgJ2lubGluZSddLmluY2x1ZGVzKGRpc3Bvc2l0aW9uKSkge1xuICAgICAgICAgICAgICAgIGRpc3Bvc2l0aW9uID0gJ2F0dGFjaG1lbnQnO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICBpZiAoIWRpc3Bvc2l0aW9uICYmICF0aGlzLnRleHRUeXBlcy5pbmNsdWRlcyhjb250ZW50VHlwZSkpIHtcbiAgICAgICAgICAgICAgICBuZXdOb2RlLmRpc3Bvc2l0aW9uID0gJ2F0dGFjaG1lbnQnO1xuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICBuZXdOb2RlLmRpc3Bvc2l0aW9uID0gZGlzcG9zaXRpb24gfHwgJ2lubGluZSc7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIG5ld05vZGUuaXNBdHRhY2htZW50ID0gIXRoaXMudGV4dFR5cGVzLmluY2x1ZGVzKGNvbnRlbnRUeXBlKSB8fCBuZXdOb2RlLmRpc3Bvc2l0aW9uICE9PSAnaW5saW5lJztcblxuICAgICAgICAgICAgbmV3Tm9kZS5lbmNvZGluZyA9IFsncXVvdGVkLXByaW50YWJsZScsICdiYXNlNjQnXS5pbmNsdWRlcyhlbmNvZGluZykgPyBlbmNvZGluZyA6ICdiaW5hcnknO1xuXG4gICAgICAgICAgICBpZiAoY2hhcnNldCkge1xuICAgICAgICAgICAgICAgIG5ld05vZGUuY2hhcnNldCA9IGNoYXJzZXQ7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIGxldCBkZWNvZGVyID0gbm9kZS5nZXREZWNvZGVyKCk7XG4gICAgICAgICAgICBkZWNvZGVyLm9uKCdlbmQnLCAoKSA9PiB7XG4gICAgICAgICAgICAgICAgdGhpcy5kZWNvZGVyRW5kZWQgPSB0cnVlO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBuZXdOb2RlLmRlY29kZXIgPSBkZWNvZGVyO1xuICAgICAgICB9XG5cbiAgICAgICAgaWYgKG5vZGUucm9vdCkge1xuICAgICAgICAgICAgdGhpcy5oZWFkZXJzID0gbmV3Tm9kZS5oZWFkZXJzO1xuICAgICAgICAgICAgdGhpcy5oZWFkZXJMaW5lcyA9IG5ld05vZGUuaGVhZGVyTGluZXM7XG4gICAgICAgIH1cblxuICAgICAgICAvLyBmaW5kIGxvY2F0aW9uIGluIHRyZWVcblxuICAgICAgICBpZiAoIXRoaXMudHJlZSkge1xuICAgICAgICAgICAgbmV3Tm9kZS5yb290ID0gdHJ1ZTtcbiAgICAgICAgICAgIHRoaXMuY3Vybm9kZSA9IHRoaXMudHJlZSA9IG5ld05vZGU7XG4gICAgICAgICAgICByZXR1cm4gbmV3Tm9kZTtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIGltbWVkaWF0ZSBjaGlsZCBvZiByb290IG5vZGVcbiAgICAgICAgaWYgKCF0aGlzLmN1cm5vZGUucGFyZW50KSB7XG4gICAgICAgICAgICBuZXdOb2RlLnBhcmVudCA9IHRoaXMuY3Vybm9kZTtcbiAgICAgICAgICAgIHRoaXMuY3Vybm9kZS5jaGlsZHJlbi5wdXNoKG5ld05vZGUpO1xuICAgICAgICAgICAgdGhpcy5jdXJub2RlID0gbmV3Tm9kZTtcbiAgICAgICAgICAgIHJldHVybiBuZXdOb2RlO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gc2libGluZ3NcbiAgICAgICAgaWYgKHRoaXMuY3Vybm9kZS5wYXJlbnQubm9kZSA9PT0gbm9kZS5wYXJlbnROb2RlKSB7XG4gICAgICAgICAgICBuZXdOb2RlLnBhcmVudCA9IHRoaXMuY3Vybm9kZS5wYXJlbnQ7XG4gICAgICAgICAgICB0aGlzLmN1cm5vZGUucGFyZW50LmNoaWxkcmVuLnB1c2gobmV3Tm9kZSk7XG4gICAgICAgICAgICB0aGlzLmN1cm5vZGUgPSBuZXdOb2RlO1xuICAgICAgICAgICAgcmV0dXJuIG5ld05vZGU7XG4gICAgICAgIH1cblxuICAgICAgICAvLyBmaXJzdCBjaGlsZFxuICAgICAgICBpZiAodGhpcy5jdXJub2RlLm5vZGUgPT09IG5vZGUucGFyZW50Tm9kZSkge1xuICAgICAgICAgICAgbmV3Tm9kZS5wYXJlbnQgPSB0aGlzLmN1cm5vZGU7XG4gICAgICAgICAgICB0aGlzLmN1cm5vZGUuY2hpbGRyZW4ucHVzaChuZXdOb2RlKTtcbiAgICAgICAgICAgIHRoaXMuY3Vybm9kZSA9IG5ld05vZGU7XG4gICAgICAgICAgICByZXR1cm4gbmV3Tm9kZTtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIG1vdmUgdXBcbiAgICAgICAgbGV0IHBhcmVudE5vZGUgPSB0aGlzLmN1cm5vZGU7XG4gICAgICAgIHdoaWxlICgocGFyZW50Tm9kZSA9IHBhcmVudE5vZGUucGFyZW50KSkge1xuICAgICAgICAgICAgaWYgKHBhcmVudE5vZGUubm9kZSA9PT0gbm9kZS5wYXJlbnROb2RlKSB7XG4gICAgICAgICAgICAgICAgbmV3Tm9kZS5wYXJlbnQgPSBwYXJlbnROb2RlO1xuICAgICAgICAgICAgICAgIHBhcmVudE5vZGUuY2hpbGRyZW4ucHVzaChuZXdOb2RlKTtcbiAgICAgICAgICAgICAgICB0aGlzLmN1cm5vZGUgPSBuZXdOb2RlO1xuICAgICAgICAgICAgICAgIHJldHVybiBuZXdOb2RlO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG5cbiAgICAgICAgLy8gc2hvdWxkIG5ldmVyIGhhcHBlbiwgY2FuJ3QgZGV0ZWN0IHBhcmVudFxuICAgICAgICB0aGlzLmN1cm5vZGUgPSBuZXdOb2RlO1xuICAgICAgICByZXR1cm4gbmV3Tm9kZTtcbiAgICB9XG5cbiAgICBnZXRUZXh0Q29udGVudCgpIHtcbiAgICAgICAgbGV0IHRleHQgPSBbXTtcbiAgICAgICAgbGV0IGh0bWwgPSBbXTtcbiAgICAgICAgbGV0IHByb2Nlc3NOb2RlID0gKGFsdGVybmF0aXZlLCBsZXZlbCwgbm9kZSkgPT4ge1xuICAgICAgICAgICAgaWYgKG5vZGUuc2hvd01ldGEpIHtcbiAgICAgICAgICAgICAgICBsZXQgbWV0YSA9IFsnRnJvbScsICdTdWJqZWN0JywgJ0RhdGUnLCAnVG8nLCAnQ2MnLCAnQmNjJ11cbiAgICAgICAgICAgICAgICAgICAgLm1hcChma2V5ID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGxldCBrZXkgPSBma2V5LnRvTG93ZXJDYXNlKCk7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoIW5vZGUuaGVhZGVycy5oYXMoa2V5KSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIGxldCB2YWx1ZSA9IG5vZGUuaGVhZGVycy5nZXQoa2V5KTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmICghdmFsdWUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleTogZmtleSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogQXJyYXkuaXNBcnJheSh2YWx1ZSkgPyB2YWx1ZVt2YWx1ZS5sZW5ndGggLSAxXSA6IHZhbHVlXG4gICAgICAgICAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgICAgICAgICAuZmlsdGVyKGVudHJ5ID0+IGVudHJ5KTtcbiAgICAgICAgICAgICAgICBpZiAodGhpcy5oYXNIdG1sKSB7XG4gICAgICAgICAgICAgICAgICAgIGh0bWwucHVzaChcbiAgICAgICAgICAgICAgICAgICAgICAgICc8dGFibGUgY2xhc3M9XCJtcF9oZWFkXCI+JyArXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbWV0YVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAubWFwKGVudHJ5ID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxldCB2YWx1ZSA9IGVudHJ5LnZhbHVlO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3dpdGNoIChlbnRyeS5rZXkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYXNlICdGcm9tJzpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYXNlICdUbyc6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2FzZSAnQ2MnOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNhc2UgJ0JjYyc6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlID0gdmFsdWUuaHRtbDtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2FzZSAnRGF0ZSc6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlID0gdGhpcy5vcHRpb25zLmZvcm1hdERhdGVTdHJpbmcgPyB0aGlzLm9wdGlvbnMuZm9ybWF0RGF0ZVN0cmluZyh2YWx1ZSkgOiB2YWx1ZS50b1VUQ1N0cmluZygpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYXNlICdTdWJqZWN0JzpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWUgPSAnPHN0cm9uZz4nICsgaGUuZW5jb2RlKHZhbHVlKSArICc8L3N0cm9uZz4nO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZSA9IGhlLmVuY29kZSh2YWx1ZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiAnPHRyPjx0ZCBjbGFzcz1cIm1wX2hlYWRfa2V5XCI+JyArIGhlLmVuY29kZShlbnRyeS5rZXkpICsgJzo8L3RkPjx0ZCBjbGFzcz1cIm1wX2hlYWRfdmFsdWVcIj4nICsgdmFsdWUgKyAnPHRkPjwvdHI+JztcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLmpvaW4oJ1xcbicpICtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAnPHRhYmxlPidcbiAgICAgICAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKHRoaXMuaGFzVGV4dCkge1xuICAgICAgICAgICAgICAgICAgICB0ZXh0LnB1c2goXG4gICAgICAgICAgICAgICAgICAgICAgICAnXFxuJyArXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbWV0YVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAubWFwKGVudHJ5ID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxldCB2YWx1ZSA9IGVudHJ5LnZhbHVlO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3dpdGNoIChlbnRyeS5rZXkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYXNlICdGcm9tJzpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYXNlICdUbyc6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2FzZSAnQ2MnOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNhc2UgJ0JjYyc6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlID0gdmFsdWUudGV4dDtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2FzZSAnRGF0ZSc6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlID0gdGhpcy5vcHRpb25zLmZvcm1hdERhdGVTdHJpbmcgPyB0aGlzLm9wdGlvbnMuZm9ybWF0RGF0ZVN0cmluZyh2YWx1ZSkgOiB2YWx1ZS50b1VUQ1N0cmluZygpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBlbnRyeS5rZXkgKyAnOiAnICsgdmFsdWU7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5qb2luKCdcXG4nKSArXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgJ1xcbidcbiAgICAgICAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAobm9kZS50ZXh0Q29udGVudCkge1xuICAgICAgICAgICAgICAgIGlmIChub2RlLmNvbnRlbnRUeXBlID09PSAndGV4dC9wbGFpbicpIHtcbiAgICAgICAgICAgICAgICAgICAgdGV4dC5wdXNoKG5vZGUudGV4dENvbnRlbnQpO1xuICAgICAgICAgICAgICAgICAgICBpZiAoIWFsdGVybmF0aXZlICYmIHRoaXMuaGFzSHRtbCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgaHRtbC5wdXNoKHRoaXMudGV4dFRvSHRtbChub2RlLnRleHRDb250ZW50KSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKG5vZGUuY29udGVudFR5cGUgPT09ICdtZXNzYWdlL2RlbGl2ZXJ5LXN0YXR1cycgJiYgIXRoaXMub3B0aW9ucy5rZWVwRGVsaXZlcnlTdGF0dXMpIHtcbiAgICAgICAgICAgICAgICAgICAgdGV4dC5wdXNoKG5vZGUudGV4dENvbnRlbnQpO1xuICAgICAgICAgICAgICAgICAgICBpZiAoIWFsdGVybmF0aXZlICYmIHRoaXMuaGFzSHRtbCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgaHRtbC5wdXNoKHRoaXMudGV4dFRvSHRtbChub2RlLnRleHRDb250ZW50KSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKG5vZGUuY29udGVudFR5cGUgPT09ICd0ZXh0L2h0bWwnKSB7XG4gICAgICAgICAgICAgICAgICAgIGxldCBmYWlsZWRUb1BhcnNlSHRtbCA9IGZhbHNlO1xuICAgICAgICAgICAgICAgICAgICBpZiAoKCFhbHRlcm5hdGl2ZSAmJiB0aGlzLmhhc1RleHQpIHx8IChub2RlLnJvb3QgJiYgIXRoaXMuaGFzVGV4dCkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmICh0aGlzLm9wdGlvbnMuc2tpcEh0bWxUb1RleHQpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0ZXh0LnB1c2goJycpO1xuICAgICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIGlmIChub2RlLnRleHRDb250ZW50Lmxlbmd0aCA+IHRoaXMub3B0aW9ucy5tYXhIdG1sTGVuZ3RoVG9QYXJzZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZW1pdCgnZXJyb3InLCBuZXcgRXJyb3IoYEhUTUwgdG9vIGxvbmcgZm9yIHBhcnNpbmcgJHtub2RlLnRleHRDb250ZW50Lmxlbmd0aH0gYnl0ZXNgKSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGV4dC5wdXNoKCdJbnZhbGlkIEhUTUwgY29udGVudCAodG9vIGxvbmcpJyk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZmFpbGVkVG9QYXJzZUh0bWwgPSB0cnVlO1xuICAgICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0ZXh0LnB1c2goaHRtbFRvVGV4dChub2RlLnRleHRDb250ZW50KSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZW1pdCgnZXJyb3InLCBuZXcgRXJyb3IoJ0ZhaWxlZCB0byBwYXJzZSBIVE1MJykpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0ZXh0LnB1c2goJ0ludmFsaWQgSFRNTCBjb250ZW50Jyk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZhaWxlZFRvUGFyc2VIdG1sID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgaWYgKCFmYWlsZWRUb1BhcnNlSHRtbCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgaHRtbC5wdXNoKG5vZGUudGV4dENvbnRlbnQpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgYWx0ZXJuYXRpdmUgPSBhbHRlcm5hdGl2ZSB8fCBub2RlLmNvbnRlbnRUeXBlID09PSAnbXVsdGlwYXJ0L2FsdGVybmF0aXZlJztcbiAgICAgICAgICAgIGlmIChub2RlLmNoaWxkcmVuKSB7XG4gICAgICAgICAgICAgICAgbm9kZS5jaGlsZHJlbi5mb3JFYWNoKHN1Yk5vZGUgPT4ge1xuICAgICAgICAgICAgICAgICAgICBwcm9jZXNzTm9kZShhbHRlcm5hdGl2ZSwgbGV2ZWwgKyAxLCBzdWJOb2RlKTtcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfTtcblxuICAgICAgICBwcm9jZXNzTm9kZShmYWxzZSwgMCwgdGhpcy50cmVlKTtcblxuICAgICAgICBsZXQgcmVzcG9uc2UgPSB7XG4gICAgICAgICAgICB0eXBlOiAndGV4dCdcbiAgICAgICAgfTtcbiAgICAgICAgaWYgKGh0bWwubGVuZ3RoKSB7XG4gICAgICAgICAgICB0aGlzLmh0bWwgPSByZXNwb25zZS5odG1sID0gaHRtbC5qb2luKCc8YnIvPlxcbicpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0ZXh0Lmxlbmd0aCkge1xuICAgICAgICAgICAgdGhpcy50ZXh0ID0gcmVzcG9uc2UudGV4dCA9IHRleHQuam9pbignXFxuJyk7XG4gICAgICAgICAgICB0aGlzLnRleHRBc0h0bWwgPSByZXNwb25zZS50ZXh0QXNIdG1sID0gdGV4dC5tYXAocGFydCA9PiB0aGlzLnRleHRUb0h0bWwocGFydCkpLmpvaW4oJzxici8+XFxuJyk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHJlc3BvbnNlO1xuICAgIH1cblxuICAgIHByb2Nlc3NDaHVuayhkYXRhLCBkb25lKSB7XG4gICAgICAgIGxldCBwYXJ0SWQgPSBudWxsO1xuICAgICAgICBpZiAoZGF0YS5fcGFyZW50Qm91bmRhcnkpIHtcbiAgICAgICAgICAgIHBhcnRJZCA9IHRoaXMuX2dldFBhcnRJZChkYXRhLl9wYXJlbnRCb3VuZGFyeSk7XG4gICAgICAgIH1cbiAgICAgICAgc3dpdGNoIChkYXRhLnR5cGUpIHtcbiAgICAgICAgICAgIGNhc2UgJ25vZGUnOiB7XG4gICAgICAgICAgICAgICAgbGV0IG5vZGUgPSB0aGlzLmNyZWF0ZU5vZGUoZGF0YSk7XG4gICAgICAgICAgICAgICAgaWYgKG5vZGUgPT09IHRoaXMudHJlZSkge1xuICAgICAgICAgICAgICAgICAgICBbJ3N1YmplY3QnLCAncmVmZXJlbmNlcycsICdkYXRlJywgJ3RvJywgJ2Zyb20nLCAndG8nLCAnY2MnLCAnYmNjJywgJ21lc3NhZ2UtaWQnLCAnaW4tcmVwbHktdG8nLCAncmVwbHktdG8nXS5mb3JFYWNoKGtleSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAobm9kZS5oZWFkZXJzLmhhcyhrZXkpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpc1trZXkucmVwbGFjZSgvLShbYS16XSkvZywgKG0sIGMpID0+IGMudG9VcHBlckNhc2UoKSldID0gbm9kZS5oZWFkZXJzLmdldChrZXkpO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5lbWl0KCdoZWFkZXJzJywgbm9kZS5oZWFkZXJzKTtcblxuICAgICAgICAgICAgICAgICAgICBpZiAobm9kZS5oZWFkZXJMaW5lcykge1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5lbWl0KCdoZWFkZXJMaW5lcycsIG5vZGUuaGVhZGVyTGluZXMpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAgICAgaWYgKGRhdGEuY29udGVudFR5cGUgPT09ICdtZXNzYWdlL3JmYzgyMicgJiYgZGF0YS5tZXNzYWdlTm9kZSkge1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgICBpZiAoZGF0YS5wYXJlbnROb2RlICYmIGRhdGEucGFyZW50Tm9kZS5jb250ZW50VHlwZSA9PT0gJ21lc3NhZ2UvcmZjODIyJykge1xuICAgICAgICAgICAgICAgICAgICBub2RlLnNob3dNZXRhID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgICBpZiAobm9kZS5pc0F0dGFjaG1lbnQpIHtcbiAgICAgICAgICAgICAgICAgICAgbGV0IGNvbnRlbnRUeXBlID0gbm9kZS5jb250ZW50VHlwZTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKG5vZGUuY29udGVudFR5cGUgPT09ICdhcHBsaWNhdGlvbi9vY3RldC1zdHJlYW0nICYmIGRhdGEuZmlsZW5hbWUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnRlbnRUeXBlID0gdGhpcy5saWJtaW1lLmRldGVjdE1pbWVUeXBlKGRhdGEuZmlsZW5hbWUpIHx8ICdhcHBsaWNhdGlvbi9vY3RldC1zdHJlYW0nO1xuICAgICAgICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgICAgICAgbGV0IGF0dGFjaG1lbnQgPSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB0eXBlOiAnYXR0YWNobWVudCcsXG4gICAgICAgICAgICAgICAgICAgICAgICBjb250ZW50OiBudWxsLFxuICAgICAgICAgICAgICAgICAgICAgICAgY29udGVudFR5cGUsXG4gICAgICAgICAgICAgICAgICAgICAgICBwYXJ0SWQsXG4gICAgICAgICAgICAgICAgICAgICAgICByZWxlYXNlOiAoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYXR0YWNobWVudC5yZWxlYXNlID0gbnVsbDtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAodGhpcy53YWl0VW50aWxBdHRhY2htZW50RW5kICYmIHR5cGVvZiB0aGlzLmF0dGFjaG1lbnRDYWxsYmFjayA9PT0gJ2Z1bmN0aW9uJykge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRJbW1lZGlhdGUodGhpcy5hdHRhY2htZW50Q2FsbGJhY2spO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmF0dGFjaG1lbnRDYWxsYmFjayA9IGZhbHNlO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMud2FpdFVudGlsQXR0YWNobWVudEVuZCA9IGZhbHNlO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB9O1xuXG4gICAgICAgICAgICAgICAgICAgIGxldCBhbGdvID0gdGhpcy5vcHRpb25zLmNoZWNrc3VtQWxnbyB8fCAnbWQ1JztcbiAgICAgICAgICAgICAgICAgICAgbGV0IGhhc2hlciA9IG5ldyBTdHJlYW1IYXNoKGF0dGFjaG1lbnQsIGFsZ28pO1xuICAgICAgICAgICAgICAgICAgICBub2RlLmRlY29kZXIub24oJ2Vycm9yJywgZXJyID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGhhc2hlci5lbWl0KCdlcnJvcicsIGVycik7XG4gICAgICAgICAgICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgICAgICAgICAgIG5vZGUuZGVjb2Rlci5vbigncmVhZGFibGUnLCAoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBsZXQgY2h1bms7XG5cbiAgICAgICAgICAgICAgICAgICAgICAgIHdoaWxlICgoY2h1bmsgPSBub2RlLmRlY29kZXIucmVhZCgpKSAhPT0gbnVsbCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhc2hlci53cml0ZShjaHVuayk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgICAgICAgICAgIG5vZGUuZGVjb2Rlci5vbmNlKCdlbmQnLCAoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBoYXNoZXIuZW5kKCk7XG4gICAgICAgICAgICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgICAgICAgICAgIC8vbm9kZS5kZWNvZGVyLnBpcGUoaGFzaGVyKTtcbiAgICAgICAgICAgICAgICAgICAgYXR0YWNobWVudC5jb250ZW50ID0gaGFzaGVyO1xuXG4gICAgICAgICAgICAgICAgICAgIHRoaXMud2FpdFVudGlsQXR0YWNobWVudEVuZCA9IHRydWU7XG4gICAgICAgICAgICAgICAgICAgIGlmIChkYXRhLmRpc3Bvc2l0aW9uKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBhdHRhY2htZW50LmNvbnRlbnREaXNwb3NpdGlvbiA9IGRhdGEuZGlzcG9zaXRpb247XG4gICAgICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgICAgICBpZiAoZGF0YS5maWxlbmFtZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgYXR0YWNobWVudC5maWxlbmFtZSA9IGRhdGEuZmlsZW5hbWU7XG4gICAgICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgICAgICBpZiAobm9kZS5oZWFkZXJzLmhhcygnY29udGVudC1pZCcpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBhdHRhY2htZW50LmNvbnRlbnRJZCA9IFtdLmNvbmNhdChub2RlLmhlYWRlcnMuZ2V0KCdjb250ZW50LWlkJykgfHwgW10pLnNoaWZ0KCk7XG4gICAgICAgICAgICAgICAgICAgICAgICBhdHRhY2htZW50LmNpZCA9IGF0dGFjaG1lbnQuY29udGVudElkLnRyaW0oKS5yZXBsYWNlKC9ePHw+JC9nLCAnJykudHJpbSgpO1xuICAgICAgICAgICAgICAgICAgICAgICAgLy8gY2hlY2sgaWYgdGhlIGF0dGFjaG1lbnQgaXMgXCJyZWxhdGVkXCIgdG8gdGV4dCBjb250ZW50IGxpa2UgYW4gZW1iZWRkZWQgaW1hZ2UgZXRjXG4gICAgICAgICAgICAgICAgICAgICAgICBsZXQgcGFyZW50Tm9kZSA9IG5vZGU7XG4gICAgICAgICAgICAgICAgICAgICAgICB3aGlsZSAoKHBhcmVudE5vZGUgPSBwYXJlbnROb2RlLnBhcmVudCkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAocGFyZW50Tm9kZS5jb250ZW50VHlwZSA9PT0gJ211bHRpcGFydC9yZWxhdGVkJykge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhdHRhY2htZW50LnJlbGF0ZWQgPSB0cnVlO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAgICAgICAgIGF0dGFjaG1lbnQuaGVhZGVycyA9IG5vZGUuaGVhZGVycztcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5wdXNoKGF0dGFjaG1lbnQpO1xuICAgICAgICAgICAgICAgICAgICB0aGlzLmF0dGFjaG1lbnRMaXN0LnB1c2goYXR0YWNobWVudCk7XG4gICAgICAgICAgICAgICAgfSBlbHNlIGlmIChub2RlLmRpc3Bvc2l0aW9uID09PSAnaW5saW5lJykge1xuICAgICAgICAgICAgICAgICAgICBsZXQgY2h1bmtzID0gW107XG4gICAgICAgICAgICAgICAgICAgIGxldCBjaHVua2xlbiA9IDA7XG4gICAgICAgICAgICAgICAgICAgIG5vZGUuY29udGVudFN0cmVhbSA9IG5vZGUuZGVjb2RlcjtcblxuICAgICAgICAgICAgICAgICAgICBpZiAobm9kZS5jb250ZW50VHlwZSA9PT0gJ3RleHQvcGxhaW4nKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmhhc1RleHQgPSB0cnVlO1xuICAgICAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKG5vZGUuY29udGVudFR5cGUgPT09ICd0ZXh0L2h0bWwnKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmhhc0h0bWwgPSB0cnVlO1xuICAgICAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKG5vZGUuY29udGVudFR5cGUgPT09ICdtZXNzYWdlL2RlbGl2ZXJ5LXN0YXR1cycgJiYgIXRoaXMub3B0aW9ucy5rZWVwRGVsaXZlcnlTdGF0dXMpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuaGFzVGV4dCA9IHRydWU7XG4gICAgICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgICAgICBpZiAobm9kZS5ub2RlLmZsb3dlZCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgbGV0IGNvbnRlbnRTdHJlYW0gPSBub2RlLmNvbnRlbnRTdHJlYW07XG4gICAgICAgICAgICAgICAgICAgICAgICBsZXQgZmxvd0RlY29kZXIgPSBuZXcgRmxvd2VkRGVjb2Rlcih7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGVsU3A6IG5vZGUubm9kZS5kZWxTcFxuICAgICAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb250ZW50U3RyZWFtLm9uKCdlcnJvcicsIGVyciA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZmxvd0RlY29kZXIuZW1pdCgnZXJyb3InLCBlcnIpO1xuICAgICAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb250ZW50U3RyZWFtLnBpcGUoZmxvd0RlY29kZXIpO1xuICAgICAgICAgICAgICAgICAgICAgICAgbm9kZS5jb250ZW50U3RyZWFtID0gZmxvd0RlY29kZXI7XG4gICAgICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgICAgICBsZXQgY2hhcnNldCA9IG5vZGUuY2hhcnNldCB8fCAndXRmLTgnO1xuICAgICAgICAgICAgICAgICAgICAvL2NoYXJzZXQgPSBjaGFyc2V0IHx8ICd3aW5kb3dzLTEyNTcnO1xuXG4gICAgICAgICAgICAgICAgICAgIGlmICghWydhc2NpaScsICd1c2FzY2lpJywgJ3V0ZjgnXS5pbmNsdWRlcyhjaGFyc2V0LnRvTG93ZXJDYXNlKCkucmVwbGFjZSgvW15hLXowLTldKy9nLCAnJykpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxldCBjb250ZW50U3RyZWFtID0gbm9kZS5jb250ZW50U3RyZWFtO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxldCBkZWNvZGVTdHJlYW0gPSB0aGlzLmRlY29kZXIuZGVjb2RlU3RyZWFtKGNoYXJzZXQpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnRlbnRTdHJlYW0ub24oJ2Vycm9yJywgZXJyID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGVjb2RlU3RyZWFtLmVtaXQoJ2Vycm9yJywgZXJyKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb250ZW50U3RyZWFtLnBpcGUoZGVjb2RlU3RyZWFtKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBub2RlLmNvbnRlbnRTdHJlYW0gPSBkZWNvZGVTdHJlYW07XG4gICAgICAgICAgICAgICAgICAgICAgICB9IGNhdGNoIChFKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gZG8gbm90IGRlY29kZSBjaGFyc2V0XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgICAgICBub2RlLmNvbnRlbnRTdHJlYW0ub24oJ3JlYWRhYmxlJywgKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgbGV0IGNodW5rO1xuICAgICAgICAgICAgICAgICAgICAgICAgd2hpbGUgKChjaHVuayA9IG5vZGUuY29udGVudFN0cmVhbS5yZWFkKCkpICE9PSBudWxsKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHR5cGVvZiBjaHVuayA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2h1bmsgPSBCdWZmZXIuZnJvbShjaHVuayk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNodW5rcy5wdXNoKGNodW5rKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjaHVua2xlbiArPSBjaHVuay5sZW5ndGg7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgICAgICAgICAgIG5vZGUuY29udGVudFN0cmVhbS5vbmNlKCdlbmQnLCAoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBub2RlLnRleHRDb250ZW50ID0gQnVmZmVyLmNvbmNhdChjaHVua3MsIGNodW5rbGVuKS50b1N0cmluZygpLnJlcGxhY2UoL1xccj9cXG4vZywgJ1xcbicpO1xuICAgICAgICAgICAgICAgICAgICB9KTtcblxuICAgICAgICAgICAgICAgICAgICBub2RlLmNvbnRlbnRTdHJlYW0ub25jZSgnZXJyb3InLCBlcnIgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5lbWl0KCdlcnJvcicsIGVycik7XG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICBjYXNlICdkYXRhJzpcbiAgICAgICAgICAgICAgICBpZiAodGhpcy5jdXJub2RlICYmIHRoaXMuY3Vybm9kZS5kZWNvZGVyKSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuY3Vybm9kZS5kZWNvZGVyLmVuZCgpO1xuICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgIGlmICh0aGlzLndhaXRVbnRpbEF0dGFjaG1lbnRFbmQpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5hdHRhY2htZW50Q2FsbGJhY2sgPSBkb25lO1xuICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAgICAgLy8gbXVsdGlwYXJ0IG1lc3NhZ2Ugc3RydWN0dXJlXG4gICAgICAgICAgICAgICAgLy8gdGhpcyBpcyBub3QgcmVsYXRlZCB0byBhbnkgc3BlY2lmaWMgJ25vZGUnIGJsb2NrIGFzIGl0IGluY2x1ZGVzXG4gICAgICAgICAgICAgICAgLy8gZXZlcnl0aGluZyBiZXR3ZWVuIHRoZSBlbmQgb2Ygc29tZSBub2RlIGJvZHkgYW5kIGJldHdlZW4gdGhlIG5leHQgaGVhZGVyXG4gICAgICAgICAgICAgICAgLy9wcm9jZXNzLnN0ZG91dC53cml0ZShkYXRhLnZhbHVlKTtcbiAgICAgICAgICAgICAgICBicmVhaztcblxuICAgICAgICAgICAgY2FzZSAnYm9keSc6XG4gICAgICAgICAgICAgICAgaWYgKHRoaXMuY3Vybm9kZSAmJiB0aGlzLmN1cm5vZGUuZGVjb2RlciAmJiB0aGlzLmN1cm5vZGUuZGVjb2Rlci53cml0YWJsZSkge1xuICAgICAgICAgICAgICAgICAgICBpZiAodGhpcy5jdXJub2RlLmRlY29kZXIud3JpdGUoZGF0YS52YWx1ZSkgPT09IGZhbHNlKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gdGhpcy5jdXJub2RlLmRlY29kZXIub25jZSgnZHJhaW4nLCBkb25lKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgIC8vIExlYWYgZWxlbWVudCBib2R5LiBJbmNsdWRlcyB0aGUgYm9keSBmb3IgdGhlIGxhc3QgJ25vZGUnIGJsb2NrLiBZb3UgbWlnaHRcbiAgICAgICAgICAgICAgICAvLyBoYXZlIHNldmVyYWwgJ2JvZHknIGNhbGxzIGZvciBhIHNpbmdsZSAnbm9kZScgYmxvY2tcbiAgICAgICAgICAgICAgICAvL3Byb2Nlc3Muc3Rkb3V0LndyaXRlKGRhdGEudmFsdWUpO1xuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICB9XG5cbiAgICAgICAgc2V0SW1tZWRpYXRlKGRvbmUpO1xuICAgIH1cblxuICAgIF9nZXRQYXJ0SWQocGFyZW50Qm91bmRhcnkpIHtcbiAgICAgICAgbGV0IGJvdW5kYXJ5SW5kZXggPSB0aGlzLmJvdW5kYXJpZXMuZmluZEluZGV4KGl0ZW0gPT4gaXRlbS5uYW1lID09PSBwYXJlbnRCb3VuZGFyeSk7XG4gICAgICAgIGlmIChib3VuZGFyeUluZGV4ID09PSAtMSkge1xuICAgICAgICAgICAgdGhpcy5ib3VuZGFyaWVzLnB1c2goeyBuYW1lOiBwYXJlbnRCb3VuZGFyeSwgY291bnQ6IDEgfSk7XG4gICAgICAgICAgICBib3VuZGFyeUluZGV4ID0gdGhpcy5ib3VuZGFyaWVzLmxlbmd0aCAtIDE7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICB0aGlzLmJvdW5kYXJpZXNbYm91bmRhcnlJbmRleF0uY291bnQrKztcbiAgICAgICAgfVxuICAgICAgICBsZXQgcGFydElkID0gJzEnO1xuICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8PSBib3VuZGFyeUluZGV4OyBpKyspIHtcbiAgICAgICAgICAgIGlmIChpID09PSAwKSBwYXJ0SWQgPSB0aGlzLmJvdW5kYXJpZXNbaV0uY291bnQudG9TdHJpbmcoKTtcbiAgICAgICAgICAgIGVsc2UgcGFydElkICs9ICcuJyArIHRoaXMuYm91bmRhcmllc1tpXS5jb3VudC50b1N0cmluZygpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBwYXJ0SWQ7XG4gICAgfVxuXG4gICAgZ2V0QWRkcmVzc2VzSFRNTCh2YWx1ZSkge1xuICAgICAgICBsZXQgZm9ybWF0U2luZ2xlTGV2ZWwgPSBhZGRyZXNzZXMgPT5cbiAgICAgICAgICAgIGFkZHJlc3Nlc1xuICAgICAgICAgICAgICAgIC5tYXAoYWRkcmVzcyA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGxldCBzdHIgPSAnPHNwYW4gY2xhc3M9XCJtcF9hZGRyZXNzX2dyb3VwXCI+JztcbiAgICAgICAgICAgICAgICAgICAgaWYgKGFkZHJlc3MubmFtZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgc3RyICs9ICc8c3BhbiBjbGFzcz1cIm1wX2FkZHJlc3NfbmFtZVwiPicgKyBoZS5lbmNvZGUoYWRkcmVzcy5uYW1lKSArIChhZGRyZXNzLmdyb3VwID8gJzogJyA6ICcnKSArICc8L3NwYW4+JztcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBpZiAoYWRkcmVzcy5hZGRyZXNzKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBsZXQgbGluayA9ICc8YSBocmVmPVwibWFpbHRvOicgKyBoZS5lbmNvZGUoYWRkcmVzcy5hZGRyZXNzKSArICdcIiBjbGFzcz1cIm1wX2FkZHJlc3NfZW1haWxcIj4nICsgaGUuZW5jb2RlKGFkZHJlc3MuYWRkcmVzcykgKyAnPC9hPic7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoYWRkcmVzcy5uYW1lKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3RyICs9ICcgJmx0OycgKyBsaW5rICsgJyZndDsnO1xuICAgICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHIgKz0gbGluaztcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBpZiAoYWRkcmVzcy5ncm91cCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgc3RyICs9IGZvcm1hdFNpbmdsZUxldmVsKGFkZHJlc3MuZ3JvdXApICsgJzsnO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBzdHIgKyAnPC9zcGFuPic7XG4gICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICAgICAuam9pbignLCAnKTtcbiAgICAgICAgcmV0dXJuIGZvcm1hdFNpbmdsZUxldmVsKFtdLmNvbmNhdCh2YWx1ZSB8fCBbXSkpO1xuICAgIH1cblxuICAgIGdldEFkZHJlc3Nlc1RleHQodmFsdWUpIHtcbiAgICAgICAgbGV0IGZvcm1hdFNpbmdsZUxldmVsID0gYWRkcmVzc2VzID0+XG4gICAgICAgICAgICBhZGRyZXNzZXNcbiAgICAgICAgICAgICAgICAubWFwKGFkZHJlc3MgPT4ge1xuICAgICAgICAgICAgICAgICAgICBsZXQgc3RyID0gJyc7XG4gICAgICAgICAgICAgICAgICAgIGlmIChhZGRyZXNzLm5hbWUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHN0ciArPSBgXCIke2FkZHJlc3MubmFtZX1cImAgKyAoYWRkcmVzcy5ncm91cCA/ICc6ICcgOiAnJyk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgaWYgKGFkZHJlc3MuYWRkcmVzcykge1xuICAgICAgICAgICAgICAgICAgICAgICAgbGV0IGxpbmsgPSBhZGRyZXNzLmFkZHJlc3M7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoYWRkcmVzcy5uYW1lKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3RyICs9ICcgPCcgKyBsaW5rICsgJz4nO1xuICAgICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHIgKz0gbGluaztcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBpZiAoYWRkcmVzcy5ncm91cCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgc3RyICs9IGZvcm1hdFNpbmdsZUxldmVsKGFkZHJlc3MuZ3JvdXApICsgJzsnO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBzdHI7XG4gICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICAgICAuam9pbignLCAnKTtcbiAgICAgICAgcmV0dXJuIGZvcm1hdFNpbmdsZUxldmVsKFtdLmNvbmNhdCh2YWx1ZSB8fCBbXSkpO1xuICAgIH1cblxuICAgIHVwZGF0ZUltYWdlTGlua3MocmVwbGFjZUNhbGxiYWNrLCBkb25lKSB7XG4gICAgICAgIGlmICghdGhpcy5odG1sKSB7XG4gICAgICAgICAgICByZXR1cm4gc2V0SW1tZWRpYXRlKCgpID0+IGRvbmUobnVsbCwgZmFsc2UpKTtcbiAgICAgICAgfVxuXG4gICAgICAgIGxldCBjaWRzID0gbmV3IE1hcCgpO1xuICAgICAgICBsZXQgaHRtbCA9ICh0aGlzLmh0bWwgfHwgJycpLnRvU3RyaW5nKCk7XG5cbiAgICAgICAgaWYgKHRoaXMub3B0aW9ucy5za2lwSW1hZ2VMaW5rcykge1xuICAgICAgICAgICAgcmV0dXJuIGRvbmUobnVsbCwgaHRtbCk7XG4gICAgICAgIH1cblxuICAgICAgICBodG1sLnJlcGxhY2UoL1xcYmNpZDooW14nXCJcXHNdezEsMjU2fSkvZywgKG1hdGNoLCBjaWQpID0+IHtcbiAgICAgICAgICAgIGZvciAobGV0IGkgPSAwLCBsZW4gPSB0aGlzLmF0dGFjaG1lbnRMaXN0Lmxlbmd0aDsgaSA8IGxlbjsgaSsrKSB7XG4gICAgICAgICAgICAgICAgaWYgKHRoaXMuYXR0YWNobWVudExpc3RbaV0uY2lkID09PSBjaWQgJiYgL15pbWFnZVxcL1tcXHddKyQvaS50ZXN0KHRoaXMuYXR0YWNobWVudExpc3RbaV0uY29udGVudFR5cGUpKSB7XG4gICAgICAgICAgICAgICAgICAgIGNpZHMuc2V0KGNpZCwge1xuICAgICAgICAgICAgICAgICAgICAgICAgYXR0YWNobWVudDogdGhpcy5hdHRhY2htZW50TGlzdFtpXVxuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIG1hdGNoO1xuICAgICAgICB9KTtcblxuICAgICAgICBsZXQgY2lkTGlzdCA9IFtdO1xuICAgICAgICBjaWRzLmZvckVhY2goZW50cnkgPT4ge1xuICAgICAgICAgICAgY2lkTGlzdC5wdXNoKGVudHJ5KTtcbiAgICAgICAgfSk7XG5cbiAgICAgICAgbGV0IHBvcyA9IDA7XG4gICAgICAgIGxldCBwcm9jZXNzTmV4dCA9ICgpID0+IHtcbiAgICAgICAgICAgIGlmIChwb3MgPj0gY2lkTGlzdC5sZW5ndGgpIHtcbiAgICAgICAgICAgICAgICBodG1sID0gaHRtbC5yZXBsYWNlKC9cXGJjaWQ6KFteJ1wiXFxzXXsxLDI1Nn0pL2csIChtYXRjaCwgY2lkKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGlmIChjaWRzLmhhcyhjaWQpICYmIGNpZHMuZ2V0KGNpZCkudXJsKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gY2lkcy5nZXQoY2lkKS51cmw7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIG1hdGNoO1xuICAgICAgICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgICAgICAgcmV0dXJuIGRvbmUobnVsbCwgaHRtbCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBsZXQgZW50cnkgPSBjaWRMaXN0W3BvcysrXTtcbiAgICAgICAgICAgIHJlcGxhY2VDYWxsYmFjayhlbnRyeS5hdHRhY2htZW50LCAoZXJyLCB1cmwpID0+IHtcbiAgICAgICAgICAgICAgICBpZiAoZXJyKSB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBzZXRJbW1lZGlhdGUoKCkgPT4gZG9uZShlcnIpKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZW50cnkudXJsID0gdXJsO1xuICAgICAgICAgICAgICAgIHNldEltbWVkaWF0ZShwcm9jZXNzTmV4dCk7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfTtcblxuICAgICAgICBzZXRJbW1lZGlhdGUocHJvY2Vzc05leHQpO1xuICAgIH1cblxuICAgIHRleHRUb0h0bWwoc3RyKSB7XG4gICAgICAgIGlmICh0aGlzLm9wdGlvbnMuc2tpcFRleHRUb0h0bWwpIHtcbiAgICAgICAgICAgIHJldHVybiAnJztcbiAgICAgICAgfVxuICAgICAgICBzdHIgPSAoc3RyIHx8ICcnKS50b1N0cmluZygpO1xuICAgICAgICBsZXQgZW5jb2RlZDtcblxuICAgICAgICBsZXQgbGlua2lmaWVkID0gZmFsc2U7XG4gICAgICAgIGlmICghdGhpcy5vcHRpb25zLnNraXBUZXh0TGlua3MpIHtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgaWYgKGxpbmtpZnkucHJldGVzdChzdHIpKSB7XG4gICAgICAgICAgICAgICAgICAgIGxpbmtpZmllZCA9IHRydWU7XG4gICAgICAgICAgICAgICAgICAgIGxldCBsaW5rcyA9IGxpbmtpZnkubWF0Y2goc3RyKSB8fCBbXTtcbiAgICAgICAgICAgICAgICAgICAgbGV0IHJlc3VsdCA9IFtdO1xuICAgICAgICAgICAgICAgICAgICBsZXQgbGFzdCA9IDA7XG5cbiAgICAgICAgICAgICAgICAgICAgbGlua3MuZm9yRWFjaChsaW5rID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChsYXN0IDwgbGluay5pbmRleCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxldCB0ZXh0UGFydCA9IGhlXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIGVuY29kZSBzcGVjaWFsIGNoYXJzXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5lbmNvZGUoc3RyLnNsaWNlKGxhc3QsIGxpbmsuaW5kZXgpLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB1c2VOYW1lZFJlZmVyZW5jZXM6IHRydWVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVzdWx0LnB1c2godGV4dFBhcnQpO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAgICAgICAgICAgICByZXN1bHQucHVzaChgPGEgaHJlZj1cIiR7bGluay51cmx9XCI+JHtsaW5rLnRleHR9PC9hPmApO1xuXG4gICAgICAgICAgICAgICAgICAgICAgICBsYXN0ID0gbGluay5sYXN0SW5kZXg7XG4gICAgICAgICAgICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgICAgICAgICAgIGxldCB0ZXh0UGFydCA9IGhlXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBlbmNvZGUgc3BlY2lhbCBjaGFyc1xuICAgICAgICAgICAgICAgICAgICAgICAgLmVuY29kZShzdHIuc2xpY2UobGFzdCksIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB1c2VOYW1lZFJlZmVyZW5jZXM6IHRydWVcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICByZXN1bHQucHVzaCh0ZXh0UGFydCk7XG5cbiAgICAgICAgICAgICAgICAgICAgZW5jb2RlZCA9IHJlc3VsdC5qb2luKCcnKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9IGNhdGNoIChFKSB7XG4gICAgICAgICAgICAgICAgLy8gZmFpbGVkLCBkb24ndCBsaW5raWZ5XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICBpZiAoIWxpbmtpZmllZCkge1xuICAgICAgICAgICAgZW5jb2RlZCA9IGhlXG4gICAgICAgICAgICAgICAgLy8gZW5jb2RlIHNwZWNpYWwgY2hhcnNcbiAgICAgICAgICAgICAgICAuZW5jb2RlKHN0ciwge1xuICAgICAgICAgICAgICAgICAgICB1c2VOYW1lZFJlZmVyZW5jZXM6IHRydWVcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuXG4gICAgICAgIGxldCB0ZXh0ID1cbiAgICAgICAgICAgICc8cD4nICtcbiAgICAgICAgICAgIGVuY29kZWRcbiAgICAgICAgICAgICAgICAucmVwbGFjZSgvXFxyP1xcbi9nLCAnXFxuJylcbiAgICAgICAgICAgICAgICAudHJpbSgpIC8vIG5vcm1hbGl6ZSBsaW5lIGVuZGluZ3NcbiAgICAgICAgICAgICAgICAucmVwbGFjZSgvWyBcXHRdKyQvZ20sICcnKVxuICAgICAgICAgICAgICAgIC50cmltKCkgLy8gdHJpbSBlbXB0eSBsaW5lIGVuZGluZ3NcbiAgICAgICAgICAgICAgICAucmVwbGFjZSgvXFxuXFxuKy9nLCAnPC9wPjxwPicpXG4gICAgICAgICAgICAgICAgLnRyaW0oKSAvLyBpbnNlcnQgPHA+IHRvIG11bHRpcGxlIGxpbmVicmVha3NcbiAgICAgICAgICAgICAgICAucmVwbGFjZSgvXFxuL2csICc8YnIvPicpICsgLy8gaW5zZXJ0IDxicj4gdG8gc2luZ2xlIGxpbmVicmVha3NcbiAgICAgICAgICAgICc8L3A+JztcblxuICAgICAgICByZXR1cm4gdGV4dDtcbiAgICB9XG59XG5cbm1vZHVsZS5leHBvcnRzID0gTWFpbFBhcnNlcjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mailparser/lib/mail-parser.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mailparser/lib/simple-parser.js":
/*!******************************************************!*\
  !*** ./node_modules/mailparser/lib/simple-parser.js ***!
  \******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst MailParser = __webpack_require__(/*! ./mail-parser.js */ \"(rsc)/./node_modules/mailparser/lib/mail-parser.js\");\n\nmodule.exports = (input, options, callback) => {\n    if (input === null || input === undefined) {\n        throw new TypeError('Input cannot be null or undefined.');\n    }\n\n    if (!callback && typeof options === 'function') {\n        callback = options;\n        options = false;\n    }\n\n    let promise;\n    if (!callback) {\n        promise = new Promise((resolve, reject) => {\n            callback = callbackPromise(resolve, reject);\n        });\n    }\n\n    options = options || {};\n    let keepCidLinks = !!options.keepCidLinks;\n\n    let mail = {\n        attachments: []\n    };\n\n    let parser = new MailParser(options);\n\n    parser.on('error', err => {\n        callback(err);\n    });\n\n    parser.on('headers', headers => {\n        mail.headers = headers;\n        mail.headerLines = parser.headerLines;\n    });\n\n    let reading = false;\n    let reader = () => {\n        reading = true;\n\n        let data = parser.read();\n\n        if (data === null) {\n            reading = false;\n            return;\n        }\n\n        if (data.type === 'text') {\n            Object.keys(data).forEach(key => {\n                if (['text', 'html', 'textAsHtml'].includes(key)) {\n                    mail[key] = data[key];\n                }\n            });\n        }\n\n        if (data.type === 'attachment') {\n            mail.attachments.push(data);\n\n            let chunks = [];\n            let chunklen = 0;\n            data.content.on('readable', () => {\n                let chunk;\n                while ((chunk = data.content.read()) !== null) {\n                    chunks.push(chunk);\n                    chunklen += chunk.length;\n                }\n            });\n\n            data.content.on('end', () => {\n                data.content = Buffer.concat(chunks, chunklen);\n                data.release();\n                reader();\n            });\n        } else {\n            reader();\n        }\n    };\n\n    parser.on('readable', () => {\n        if (!reading) {\n            reader();\n        }\n    });\n\n    parser.on('end', () => {\n        ['subject', 'references', 'date', 'to', 'from', 'to', 'cc', 'bcc', 'message-id', 'in-reply-to', 'reply-to'].forEach(key => {\n            if (mail.headers && mail.headers.has(key)) {\n                mail[key.replace(/-([a-z])/g, (m, c) => c.toUpperCase())] = mail.headers.get(key);\n            }\n        });\n\n        if (keepCidLinks) {\n            return callback(null, mail);\n        }\n        parser.updateImageLinks(\n            (attachment, done) => done(false, 'data:' + attachment.contentType + ';base64,' + attachment.content.toString('base64')),\n            (err, html) => {\n                if (err) {\n                    return callback(err);\n                }\n                mail.html = html;\n\n                callback(null, mail);\n            }\n        );\n    });\n\n    if (typeof input === 'string') {\n        parser.end(Buffer.from(input));\n    } else if (Buffer.isBuffer(input)) {\n        parser.end(input);\n    } else {\n        input\n            .once('error', err => {\n                input.destroy();\n                parser.destroy();\n                callback(err);\n            })\n            .pipe(parser);\n    }\n\n    return promise;\n};\n\nfunction callbackPromise(resolve, reject) {\n    return function (...args) {\n        let err = args.shift();\n        if (err) {\n            reject(err);\n        } else {\n            resolve(...args);\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mailparser/lib/simple-parser.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mailparser/lib/stream-hash.js":
/*!****************************************************!*\
  !*** ./node_modules/mailparser/lib/stream-hash.js ***!
  \****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst crypto = __webpack_require__(/*! crypto */ \"crypto\");\nconst Transform = (__webpack_require__(/*! stream */ \"stream\").Transform);\n\nclass StreamHash extends Transform {\n    constructor(attachment, algo) {\n        super();\n        this.attachment = attachment;\n        this.algo = (algo || 'md5').toLowerCase();\n        this.hash = crypto.createHash(algo);\n        this.byteCount = 0;\n    }\n\n    _transform(chunk, encoding, done) {\n        this.hash.update(chunk);\n        this.byteCount += chunk.length;\n        done(null, chunk);\n    }\n\n    _flush(done) {\n        this.attachment.checksum = this.hash.digest('hex');\n        this.attachment.size = this.byteCount;\n        done();\n    }\n}\n\nmodule.exports = StreamHash;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWFpbHBhcnNlci9saWIvc3RyZWFtLWhhc2guanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsZUFBZSxtQkFBTyxDQUFDLHNCQUFRO0FBQy9CLGtCQUFrQix1REFBMkI7O0FBRTdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIkM6XFxDb2RlXFxvbmUtbWFpbFxcbm9kZV9tb2R1bGVzXFxtYWlscGFyc2VyXFxsaWJcXHN0cmVhbS1oYXNoLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuY29uc3QgY3J5cHRvID0gcmVxdWlyZSgnY3J5cHRvJyk7XG5jb25zdCBUcmFuc2Zvcm0gPSByZXF1aXJlKCdzdHJlYW0nKS5UcmFuc2Zvcm07XG5cbmNsYXNzIFN0cmVhbUhhc2ggZXh0ZW5kcyBUcmFuc2Zvcm0ge1xuICAgIGNvbnN0cnVjdG9yKGF0dGFjaG1lbnQsIGFsZ28pIHtcbiAgICAgICAgc3VwZXIoKTtcbiAgICAgICAgdGhpcy5hdHRhY2htZW50ID0gYXR0YWNobWVudDtcbiAgICAgICAgdGhpcy5hbGdvID0gKGFsZ28gfHwgJ21kNScpLnRvTG93ZXJDYXNlKCk7XG4gICAgICAgIHRoaXMuaGFzaCA9IGNyeXB0by5jcmVhdGVIYXNoKGFsZ28pO1xuICAgICAgICB0aGlzLmJ5dGVDb3VudCA9IDA7XG4gICAgfVxuXG4gICAgX3RyYW5zZm9ybShjaHVuaywgZW5jb2RpbmcsIGRvbmUpIHtcbiAgICAgICAgdGhpcy5oYXNoLnVwZGF0ZShjaHVuayk7XG4gICAgICAgIHRoaXMuYnl0ZUNvdW50ICs9IGNodW5rLmxlbmd0aDtcbiAgICAgICAgZG9uZShudWxsLCBjaHVuayk7XG4gICAgfVxuXG4gICAgX2ZsdXNoKGRvbmUpIHtcbiAgICAgICAgdGhpcy5hdHRhY2htZW50LmNoZWNrc3VtID0gdGhpcy5oYXNoLmRpZ2VzdCgnaGV4Jyk7XG4gICAgICAgIHRoaXMuYXR0YWNobWVudC5zaXplID0gdGhpcy5ieXRlQ291bnQ7XG4gICAgICAgIGRvbmUoKTtcbiAgICB9XG59XG5cbm1vZHVsZS5leHBvcnRzID0gU3RyZWFtSGFzaDtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mailparser/lib/stream-hash.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mailparser/node_modules/mailsplit/index.js":
/*!*****************************************************************!*\
  !*** ./node_modules/mailparser/node_modules/mailsplit/index.js ***!
  \*****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst MessageSplitter = __webpack_require__(/*! ./lib/message-splitter */ \"(rsc)/./node_modules/mailparser/node_modules/mailsplit/lib/message-splitter.js\");\nconst MessageJoiner = __webpack_require__(/*! ./lib/message-joiner */ \"(rsc)/./node_modules/mailparser/node_modules/mailsplit/lib/message-joiner.js\");\nconst NodeRewriter = __webpack_require__(/*! ./lib/node-rewriter */ \"(rsc)/./node_modules/mailparser/node_modules/mailsplit/lib/node-rewriter.js\");\nconst NodeStreamer = __webpack_require__(/*! ./lib/node-streamer */ \"(rsc)/./node_modules/mailparser/node_modules/mailsplit/lib/node-streamer.js\");\nconst Headers = __webpack_require__(/*! ./lib/headers */ \"(rsc)/./node_modules/mailparser/node_modules/mailsplit/lib/headers.js\");\n\nmodule.exports = {\n    Splitter: MessageSplitter,\n    Joiner: MessageJoiner,\n    Rewriter: NodeRewriter,\n    Streamer: NodeStreamer,\n    Headers\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWFpbHBhcnNlci9ub2RlX21vZHVsZXMvbWFpbHNwbGl0L2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLHdCQUF3QixtQkFBTyxDQUFDLDhHQUF3QjtBQUN4RCxzQkFBc0IsbUJBQU8sQ0FBQywwR0FBc0I7QUFDcEQscUJBQXFCLG1CQUFPLENBQUMsd0dBQXFCO0FBQ2xELHFCQUFxQixtQkFBTyxDQUFDLHdHQUFxQjtBQUNsRCxnQkFBZ0IsbUJBQU8sQ0FBQyw0RkFBZTs7QUFFdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxDb2RlXFxvbmUtbWFpbFxcbm9kZV9tb2R1bGVzXFxtYWlscGFyc2VyXFxub2RlX21vZHVsZXNcXG1haWxzcGxpdFxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5jb25zdCBNZXNzYWdlU3BsaXR0ZXIgPSByZXF1aXJlKCcuL2xpYi9tZXNzYWdlLXNwbGl0dGVyJyk7XG5jb25zdCBNZXNzYWdlSm9pbmVyID0gcmVxdWlyZSgnLi9saWIvbWVzc2FnZS1qb2luZXInKTtcbmNvbnN0IE5vZGVSZXdyaXRlciA9IHJlcXVpcmUoJy4vbGliL25vZGUtcmV3cml0ZXInKTtcbmNvbnN0IE5vZGVTdHJlYW1lciA9IHJlcXVpcmUoJy4vbGliL25vZGUtc3RyZWFtZXInKTtcbmNvbnN0IEhlYWRlcnMgPSByZXF1aXJlKCcuL2xpYi9oZWFkZXJzJyk7XG5cbm1vZHVsZS5leHBvcnRzID0ge1xuICAgIFNwbGl0dGVyOiBNZXNzYWdlU3BsaXR0ZXIsXG4gICAgSm9pbmVyOiBNZXNzYWdlSm9pbmVyLFxuICAgIFJld3JpdGVyOiBOb2RlUmV3cml0ZXIsXG4gICAgU3RyZWFtZXI6IE5vZGVTdHJlYW1lcixcbiAgICBIZWFkZXJzXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mailparser/node_modules/mailsplit/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mailparser/node_modules/mailsplit/lib/flowed-decoder.js":
/*!******************************************************************************!*\
  !*** ./node_modules/mailparser/node_modules/mailsplit/lib/flowed-decoder.js ***!
  \******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n// Helper class to rewrite nodes with specific mime type\n\nconst Transform = (__webpack_require__(/*! stream */ \"stream\").Transform);\nconst libmime = __webpack_require__(/*! libmime */ \"(rsc)/./node_modules/libmime/lib/libmime.js\");\n\n/**\n * Really bad \"stream\" transform to parse format=flowed content\n *\n * @constructor\n * @param {String} delSp True if delsp option was used\n */\nclass FlowedDecoder extends Transform {\n    constructor(config) {\n        super();\n        this.config = config || {};\n\n        this.chunks = [];\n        this.chunklen = 0;\n\n        this.libmime = new libmime.Libmime({ Iconv: config.Iconv });\n    }\n\n    _transform(chunk, encoding, callback) {\n        if (!chunk || !chunk.length) {\n            return callback();\n        }\n\n        if (!encoding !== 'buffer') {\n            chunk = Buffer.from(chunk, encoding);\n        }\n\n        this.chunks.push(chunk);\n        this.chunklen += chunk.length;\n\n        callback();\n    }\n\n    _flush(callback) {\n        if (this.chunklen) {\n            let currentBody = Buffer.concat(this.chunks, this.chunklen);\n\n            if (this.config.encoding === 'base64') {\n                currentBody = Buffer.from(currentBody.toString('binary'), 'base64');\n            }\n\n            let content = this.libmime.decodeFlowed(currentBody.toString('binary'), this.config.delSp);\n            this.push(Buffer.from(content, 'binary'));\n        }\n        return callback();\n    }\n}\n\nmodule.exports = FlowedDecoder;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mailparser/node_modules/mailsplit/lib/flowed-decoder.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mailparser/node_modules/mailsplit/lib/headers.js":
/*!***********************************************************************!*\
  !*** ./node_modules/mailparser/node_modules/mailsplit/lib/headers.js ***!
  \***********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst libmime = __webpack_require__(/*! libmime */ \"(rsc)/./node_modules/libmime/lib/libmime.js\");\n\n/**\n * Class Headers to parse and handle message headers. Headers instance allows to\n * check existing, delete or add new headers\n */\nclass Headers {\n    constructor(headers, config) {\n        config = config || {};\n\n        if (Array.isArray(headers)) {\n            // already using parsed headers\n            this.changed = true;\n            this.headers = false;\n            this.parsed = true;\n            this.lines = headers;\n        } else {\n            // using original string/buffer headers\n            this.changed = false;\n            this.headers = headers;\n            this.parsed = false;\n            this.lines = false;\n        }\n        this.mbox = false;\n        this.http = false;\n\n        this.libmime = new libmime.Libmime({ Iconv: config.Iconv });\n    }\n\n    hasHeader(key) {\n        if (!this.parsed) {\n            this._parseHeaders();\n        }\n        key = this._normalizeHeader(key);\n        return typeof this.lines.find(line => line.key === key) === 'object';\n    }\n\n    get(key) {\n        if (!this.parsed) {\n            this._parseHeaders();\n        }\n        key = this._normalizeHeader(key);\n        let lines = this.lines.filter(line => line.key === key).map(line => line.line);\n\n        return lines;\n    }\n\n    getDecoded(key) {\n        return this.get(key)\n            .map(line => this.libmime.decodeHeader(line))\n            .filter(line => line && line.value);\n    }\n\n    getFirst(key) {\n        if (!this.parsed) {\n            this._parseHeaders();\n        }\n        key = this._normalizeHeader(key);\n        let header = this.lines.find(line => line.key === key);\n        if (!header) {\n            return '';\n        }\n        return ((this.libmime.decodeHeader(header.line) || {}).value || '').toString().trim();\n    }\n\n    getList() {\n        if (!this.parsed) {\n            this._parseHeaders();\n        }\n        return this.lines;\n    }\n\n    add(key, value, index) {\n        if (typeof value === 'undefined') {\n            return;\n        }\n\n        if (typeof value === 'number') {\n            value = value.toString();\n        }\n\n        if (typeof value === 'string') {\n            value = Buffer.from(value);\n        }\n\n        value = value.toString('binary');\n        this.addFormatted(key, this.libmime.foldLines(key + ': ' + value.replace(/\\r?\\n/g, ''), 76, false), index);\n    }\n\n    addFormatted(key, line, index) {\n        if (!this.parsed) {\n            this._parseHeaders();\n        }\n        index = index || 0;\n        this.changed = true;\n\n        if (!line) {\n            return;\n        }\n\n        if (typeof line !== 'string') {\n            line = line.toString('binary');\n        }\n\n        let header = {\n            key: this._normalizeHeader(key),\n            line\n        };\n\n        if (index < 1) {\n            this.lines.unshift(header);\n        } else if (index >= this.lines.length) {\n            this.lines.push(header);\n        } else {\n            this.lines.splice(index, 0, header);\n        }\n    }\n\n    remove(key) {\n        if (!this.parsed) {\n            this._parseHeaders();\n        }\n        key = this._normalizeHeader(key);\n        for (let i = this.lines.length - 1; i >= 0; i--) {\n            if (this.lines[i].key === key) {\n                this.changed = true;\n                this.lines.splice(i, 1);\n            }\n        }\n    }\n\n    update(key, value, relativeIndex) {\n        if (!this.parsed) {\n            this._parseHeaders();\n        }\n        let keyName = key;\n        let index = 0;\n        key = this._normalizeHeader(key);\n        let relativeIndexCount = 0;\n        let relativeMatchFound = false;\n        for (let i = this.lines.length - 1; i >= 0; i--) {\n            if (this.lines[i].key === key) {\n                if (relativeIndex && relativeIndex !== relativeIndexCount) {\n                    relativeIndexCount++;\n                    continue;\n                }\n                index = i;\n                this.changed = true;\n                this.lines.splice(i, 1);\n                if (relativeIndex) {\n                    relativeMatchFound = true;\n                    break;\n                }\n            }\n        }\n\n        if (relativeIndex && !relativeMatchFound) {\n            return;\n        }\n\n        this.add(keyName, value, index);\n    }\n\n    build(lineEnd) {\n        if (!this.changed && !lineEnd) {\n            return typeof this.headers === 'string' ? Buffer.from(this.headers, 'binary') : this.headers;\n        }\n\n        if (!this.parsed) {\n            this._parseHeaders();\n        }\n\n        lineEnd = lineEnd || '\\r\\n';\n\n        let headers = this.lines.map(line => line.line.replace(/\\r?\\n/g, lineEnd)).join(lineEnd) + `${lineEnd}${lineEnd}`;\n\n        if (this.mbox) {\n            headers = this.mbox + lineEnd + headers;\n        }\n\n        if (this.http) {\n            headers = this.http + lineEnd + headers;\n        }\n\n        return Buffer.from(headers, 'binary');\n    }\n\n    _normalizeHeader(key) {\n        return (key || '').toLowerCase().trim();\n    }\n\n    _parseHeaders() {\n        if (!this.headers) {\n            this.lines = [];\n            this.parsed = true;\n            return;\n        }\n\n        let lines = this.headers\n            .toString('binary')\n            .replace(/[\\r\\n]+$/, '')\n            .split(/\\r?\\n/);\n\n        for (let i = lines.length - 1; i >= 0; i--) {\n            let chr = lines[i].charAt(0);\n            if (i && (chr === ' ' || chr === '\\t')) {\n                lines[i - 1] += '\\r\\n' + lines[i];\n                lines.splice(i, 1);\n            } else {\n                let line = lines[i];\n                if (!i && /^From /i.test(line)) {\n                    // mbox file\n                    this.mbox = line;\n                    lines.splice(i, 1);\n                    continue;\n                } else if (!i && /^POST /i.test(line)) {\n                    // HTTP POST request\n                    this.http = line;\n                    lines.splice(i, 1);\n                    continue;\n                }\n                let key = this._normalizeHeader(line.substr(0, line.indexOf(':')));\n                lines[i] = {\n                    key,\n                    line\n                };\n            }\n        }\n\n        this.lines = lines;\n        this.parsed = true;\n    }\n}\n\n// expose to the world\nmodule.exports = Headers;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mailparser/node_modules/mailsplit/lib/headers.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mailparser/node_modules/mailsplit/lib/message-joiner.js":
/*!******************************************************************************!*\
  !*** ./node_modules/mailparser/node_modules/mailsplit/lib/message-joiner.js ***!
  \******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst Transform = (__webpack_require__(/*! stream */ \"stream\").Transform);\n\nclass MessageJoiner extends Transform {\n    constructor() {\n        let options = {\n            readableObjectMode: false,\n            writableObjectMode: true\n        };\n        super(options);\n    }\n\n    _transform(obj, encoding, callback) {\n        if (Buffer.isBuffer(obj)) {\n            this.push(obj);\n        } else if (obj.type === 'node') {\n            this.push(obj.getHeaders());\n        } else if (obj.value) {\n            this.push(obj.value);\n        }\n        return callback();\n    }\n\n    _flush(callback) {\n        return callback();\n    }\n}\n\nmodule.exports = MessageJoiner;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWFpbHBhcnNlci9ub2RlX21vZHVsZXMvbWFpbHNwbGl0L2xpYi9tZXNzYWdlLWpvaW5lci5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYixrQkFBa0IsdURBQTJCOztBQUU3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsiQzpcXENvZGVcXG9uZS1tYWlsXFxub2RlX21vZHVsZXNcXG1haWxwYXJzZXJcXG5vZGVfbW9kdWxlc1xcbWFpbHNwbGl0XFxsaWJcXG1lc3NhZ2Utam9pbmVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuY29uc3QgVHJhbnNmb3JtID0gcmVxdWlyZSgnc3RyZWFtJykuVHJhbnNmb3JtO1xuXG5jbGFzcyBNZXNzYWdlSm9pbmVyIGV4dGVuZHMgVHJhbnNmb3JtIHtcbiAgICBjb25zdHJ1Y3RvcigpIHtcbiAgICAgICAgbGV0IG9wdGlvbnMgPSB7XG4gICAgICAgICAgICByZWFkYWJsZU9iamVjdE1vZGU6IGZhbHNlLFxuICAgICAgICAgICAgd3JpdGFibGVPYmplY3RNb2RlOiB0cnVlXG4gICAgICAgIH07XG4gICAgICAgIHN1cGVyKG9wdGlvbnMpO1xuICAgIH1cblxuICAgIF90cmFuc2Zvcm0ob2JqLCBlbmNvZGluZywgY2FsbGJhY2spIHtcbiAgICAgICAgaWYgKEJ1ZmZlci5pc0J1ZmZlcihvYmopKSB7XG4gICAgICAgICAgICB0aGlzLnB1c2gob2JqKTtcbiAgICAgICAgfSBlbHNlIGlmIChvYmoudHlwZSA9PT0gJ25vZGUnKSB7XG4gICAgICAgICAgICB0aGlzLnB1c2gob2JqLmdldEhlYWRlcnMoKSk7XG4gICAgICAgIH0gZWxzZSBpZiAob2JqLnZhbHVlKSB7XG4gICAgICAgICAgICB0aGlzLnB1c2gob2JqLnZhbHVlKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gY2FsbGJhY2soKTtcbiAgICB9XG5cbiAgICBfZmx1c2goY2FsbGJhY2spIHtcbiAgICAgICAgcmV0dXJuIGNhbGxiYWNrKCk7XG4gICAgfVxufVxuXG5tb2R1bGUuZXhwb3J0cyA9IE1lc3NhZ2VKb2luZXI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mailparser/node_modules/mailsplit/lib/message-joiner.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mailparser/node_modules/mailsplit/lib/message-splitter.js":
/*!********************************************************************************!*\
  !*** ./node_modules/mailparser/node_modules/mailsplit/lib/message-splitter.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst Transform = (__webpack_require__(/*! stream */ \"stream\").Transform);\nconst MimeNode = __webpack_require__(/*! ./mime-node */ \"(rsc)/./node_modules/mailparser/node_modules/mailsplit/lib/mime-node.js\");\n\nconst MAX_HEAD_SIZE = 1 * 1024 * 1024;\nconst MAX_CHILD_NODES = 1000;\n\nconst HEAD = 0x01;\nconst BODY = 0x02;\n\nclass MessageSplitter extends Transform {\n    constructor(config) {\n        let options = {\n            readableObjectMode: true,\n            writableObjectMode: false\n        };\n        super(options);\n\n        this.config = config || {};\n        this.maxHeadSize = this.config.maxHeadSize || MAX_HEAD_SIZE;\n        this.maxChildNodes = this.config.maxChildNodes || MAX_CHILD_NODES;\n        this.tree = [];\n        this.nodeCounter = 0;\n        this.newNode();\n        this.tree.push(this.node);\n        this.line = false;\n        this.hasFailed = false;\n    }\n\n    _transform(chunk, encoding, callback) {\n        // process line by line\n        // find next line ending\n        let pos = 0;\n        let i = 0;\n        let group = {\n            type: 'none'\n        };\n        let groupstart = this.line ? -this.line.length : 0;\n        let groupend = 0;\n\n        let checkTrailingLinebreak = data => {\n            if (data.type === 'body' && data.node.parentNode && data.value && data.value.length) {\n                if (data.value[data.value.length - 1] === 0x0a) {\n                    groupstart--;\n                    groupend--;\n                    pos--;\n                    if (data.value.length > 1 && data.value[data.value.length - 2] === 0x0d) {\n                        groupstart--;\n                        groupend--;\n                        pos--;\n                        if (groupstart < 0 && !this.line) {\n                            // store only <CR> as <LF> should be on the positive side\n                            this.line = Buffer.allocUnsafe(1);\n                            this.line[0] = 0x0d;\n                        }\n                        data.value = data.value.slice(0, data.value.length - 2);\n                    } else {\n                        data.value = data.value.slice(0, data.value.length - 1);\n                    }\n                } else if (data.value[data.value.length - 1] === 0x0d) {\n                    groupstart--;\n                    groupend--;\n                    pos--;\n                    data.value = data.value.slice(0, data.value.length - 1);\n                }\n            }\n        };\n\n        let iterateData = () => {\n            for (let len = chunk.length; i < len; i++) {\n                // find next <LF>\n                if (chunk[i] === 0x0a) {\n                    // line end\n\n                    let start = Math.max(pos, 0);\n                    pos = ++i;\n\n                    return this.processLine(chunk.slice(start, i), false, (err, data, flush) => {\n                        if (err) {\n                            this.hasFailed = true;\n                            return setImmediate(() => callback(err));\n                        }\n\n                        if (!data) {\n                            return setImmediate(iterateData);\n                        }\n\n                        if (flush) {\n                            if (group && group.type !== 'none') {\n                                if (group.type === 'body' && groupend >= groupstart && group.node.parentNode) {\n                                    // do not include the last line ending for body\n                                    if (chunk[groupend - 1] === 0x0a) {\n                                        groupend--;\n                                        if (groupend >= groupstart && chunk[groupend - 1] === 0x0d) {\n                                            groupend--;\n                                        }\n                                    }\n                                }\n                                if (groupstart !== groupend) {\n                                    group.value = chunk.slice(groupstart, groupend);\n                                    if (groupend < i) {\n                                        data.value = chunk.slice(groupend, i);\n                                    }\n                                }\n                                this.push(group);\n                                group = {\n                                    type: 'none'\n                                };\n                                groupstart = groupend = i;\n                            }\n                            this.push(data);\n                            groupend = i;\n                            return setImmediate(iterateData);\n                        }\n\n                        if (data.type === group.type) {\n                            // shift slice end position forward\n                            groupend = i;\n                        } else {\n                            if (group.type === 'body' && groupend >= groupstart && group.node.parentNode) {\n                                // do not include the last line ending for body\n                                if (chunk[groupend - 1] === 0x0a) {\n                                    groupend--;\n                                    if (groupend >= groupstart && chunk[groupend - 1] === 0x0d) {\n                                        groupend--;\n                                    }\n                                }\n                            }\n\n                            if (group.type !== 'none' && group.type !== 'node') {\n                                // we have a previous data/body chunk to output\n                                if (groupstart !== groupend) {\n                                    group.value = chunk.slice(groupstart, groupend);\n                                    if (group.value && group.value.length) {\n                                        this.push(group);\n                                        group = {\n                                            type: 'none'\n                                        };\n                                    }\n                                }\n                            }\n\n                            if (data.type === 'node') {\n                                this.push(data);\n                                groupstart = i;\n                                groupend = i;\n                            } else if (groupstart < 0) {\n                                groupstart = i;\n                                groupend = i;\n                                checkTrailingLinebreak(data);\n                                if (data.value && data.value.length) {\n                                    this.push(data);\n                                }\n                            } else {\n                                // start new body/data chunk\n                                group = data;\n                                groupstart = groupend;\n                                groupend = i;\n                            }\n                        }\n                        return setImmediate(iterateData);\n                    });\n                }\n            }\n\n            // skip last linebreak for body\n            if (pos >= groupstart + 1 && group.type === 'body' && group.node.parentNode) {\n                // do not include the last line ending for body\n                if (chunk[pos - 1] === 0x0a) {\n                    pos--;\n                    if (pos >= groupstart && chunk[pos - 1] === 0x0d) {\n                        pos--;\n                    }\n                }\n            }\n\n            if (group.type !== 'none' && group.type !== 'node' && pos > groupstart) {\n                // we have a leftover data/body chunk to push out\n                group.value = chunk.slice(groupstart, pos);\n\n                if (group.value && group.value.length) {\n                    this.push(group);\n                    group = {\n                        type: 'none'\n                    };\n                }\n            }\n\n            if (pos < chunk.length) {\n                if (this.line) {\n                    this.line = Buffer.concat([this.line, chunk.slice(pos)]);\n                } else {\n                    this.line = chunk.slice(pos);\n                }\n            }\n            callback();\n        };\n\n        setImmediate(iterateData);\n    }\n\n    _flush(callback) {\n        if (this.hasFailed) {\n            return callback();\n        }\n        this.processLine(false, true, (err, data) => {\n            if (err) {\n                return setImmediate(() => callback(err));\n            }\n            if (data && (data.type === 'node' || (data.value && data.value.length))) {\n                this.push(data);\n            }\n            callback();\n        });\n    }\n\n    compareBoundary(line, startpos, boundary) {\n        // --{boundary}\\r\\n or --{boundary}--\\r\\n\n        if (line.length < boundary.length + 3 + startpos || line.length > boundary.length + 6 + startpos) {\n            return false;\n        }\n        for (let i = 0; i < boundary.length; i++) {\n            if (line[i + 2 + startpos] !== boundary[i]) {\n                return false;\n            }\n        }\n\n        let pos = 0;\n        for (let i = boundary.length + 2 + startpos; i < line.length; i++) {\n            let c = line[i];\n            if (pos === 0 && (c === 0x0d || c === 0x0a)) {\n                // 1: next node\n                return 1;\n            }\n            if (pos === 0 && c !== 0x2d) {\n                // expecting \"-\"\n                return false;\n            }\n            if (pos === 1 && c !== 0x2d) {\n                // expecting \"-\"\n                return false;\n            }\n            if (pos === 2 && c !== 0x0d && c !== 0x0a) {\n                // expecting line terminator, either <CR> or <LF>\n                return false;\n            }\n            if (pos === 3 && c !== 0x0a) {\n                // expecting line terminator <LF>\n                return false;\n            }\n            pos++;\n        }\n\n        // 2: multipart end\n        return 2;\n    }\n\n    checkBoundary(line) {\n        let startpos = 0;\n        if (line.length >= 1 && (line[0] === 0x0d || line[0] === 0x0a)) {\n            startpos++;\n            if (line.length >= 2 && (line[0] === 0x0d || line[1] === 0x0a)) {\n                startpos++;\n            }\n        }\n        if (line.length < 4 || line[startpos] !== 0x2d || line[startpos + 1] !== 0x2d) {\n            // defnitely not a boundary\n            return false;\n        }\n\n        let boundary;\n        if (this.node._boundary && (boundary = this.compareBoundary(line, startpos, this.node._boundary))) {\n            // 1: next child\n            // 2: multipart end\n            return boundary;\n        }\n\n        if (this.node._parentBoundary && (boundary = this.compareBoundary(line, startpos, this.node._parentBoundary))) {\n            // 3: next sibling\n            // 4: parent end\n            return boundary + 2;\n        }\n\n        return false;\n    }\n\n    processLine(line, final, next) {\n        let flush = false;\n\n        if (this.line && line) {\n            line = Buffer.concat([this.line, line]);\n            this.line = false;\n        } else if (this.line && !line) {\n            line = this.line;\n            this.line = false;\n        }\n\n        if (!line) {\n            line = Buffer.alloc(0);\n        }\n\n        if (this.nodeCounter > this.maxChildNodes) {\n            let err = new Error('Max allowed child nodes exceeded');\n            err.code = 'EMAXLEN';\n            return next(err);\n        }\n\n        // we check boundary outside the HEAD/BODY scope as it may appear anywhere\n        let boundary = this.checkBoundary(line);\n        if (boundary) {\n            // reached boundary, switch context\n            switch (boundary) {\n                case 1:\n                    // next child\n                    this.newNode(this.node);\n                    flush = true;\n                    break;\n                case 2:\n                    // reached end of children, keep current node\n                    break;\n                case 3: {\n                    // next sibling\n                    let parentNode = this.node.parentNode;\n                    if (parentNode && parentNode.contentType === 'message/rfc822') {\n                        // special case where immediate parent is an inline message block\n                        // move up another step\n                        parentNode = parentNode.parentNode;\n                    }\n                    this.newNode(parentNode);\n                    flush = true;\n                    break;\n                }\n                case 4:\n                    // special case when boundary close a node with only header.\n                    if (this.node && this.node._headerlen && !this.node.headers) {\n                        this.node.parseHeaders();\n                        this.push(this.node);\n                    }\n                    // move up\n                    if (this.tree.length) {\n                        this.node = this.tree.pop();\n                    }\n                    this.state = BODY;\n                    break;\n            }\n\n            return next(\n                null,\n                {\n                    node: this.node,\n                    type: 'data',\n                    value: line\n                },\n                flush\n            );\n        }\n\n        switch (this.state) {\n            case HEAD: {\n                this.node.addHeaderChunk(line);\n                if (this.node._headerlen > this.maxHeadSize) {\n                    let err = new Error('Max header size for a MIME node exceeded');\n                    err.code = 'EMAXLEN';\n                    return next(err);\n                }\n                if (final || (line.length === 1 && line[0] === 0x0a) || (line.length === 2 && line[0] === 0x0d && line[1] === 0x0a)) {\n                    let currentNode = this.node;\n\n                    currentNode.parseHeaders();\n\n                    // if the content is attached message then just continue\n                    if (\n                        currentNode.contentType === 'message/rfc822' &&\n                        !this.config.ignoreEmbedded &&\n                        (!currentNode.encoding || ['7bit', '8bit', 'binary'].includes(currentNode.encoding)) &&\n                        (this.config.defaultInlineEmbedded ? currentNode.disposition !== 'attachment' : currentNode.disposition === 'inline')\n                    ) {\n                        currentNode.messageNode = true;\n                        this.newNode(currentNode);\n                        if (currentNode.parentNode) {\n                            this.node._parentBoundary = currentNode.parentNode._boundary;\n                        }\n                    } else {\n                        if (currentNode.contentType === 'message/rfc822') {\n                            currentNode.messageNode = false;\n                        }\n                        this.state = BODY;\n                        if (currentNode.multipart && currentNode._boundary) {\n                            this.tree.push(currentNode);\n                        }\n                    }\n\n                    return next(null, currentNode, flush);\n                }\n\n                return next();\n            }\n            case BODY: {\n                return next(\n                    null,\n                    {\n                        node: this.node,\n                        type: this.node.multipart ? 'data' : 'body',\n                        value: line\n                    },\n                    flush\n                );\n            }\n        }\n\n        next(null, false);\n    }\n\n    newNode(parent) {\n        this.node = new MimeNode(parent || false, this.config);\n        this.state = HEAD;\n        this.nodeCounter++;\n    }\n}\n\nmodule.exports = MessageSplitter;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mailparser/node_modules/mailsplit/lib/message-splitter.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mailparser/node_modules/mailsplit/lib/mime-node.js":
/*!*************************************************************************!*\
  !*** ./node_modules/mailparser/node_modules/mailsplit/lib/mime-node.js ***!
  \*************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst Headers = __webpack_require__(/*! ./headers */ \"(rsc)/./node_modules/mailparser/node_modules/mailsplit/lib/headers.js\");\nconst libmime = __webpack_require__(/*! libmime */ \"(rsc)/./node_modules/libmime/lib/libmime.js\");\nconst libqp = __webpack_require__(/*! libqp */ \"(rsc)/./node_modules/libqp/lib/libqp.js\");\nconst libbase64 = __webpack_require__(/*! libbase64 */ \"(rsc)/./node_modules/libbase64/lib/libbase64.js\");\nconst PassThrough = (__webpack_require__(/*! stream */ \"stream\").PassThrough);\nconst pathlib = __webpack_require__(/*! path */ \"path\");\n\nclass MimeNode {\n    constructor(parentNode, config) {\n        this.type = 'node';\n        this.root = !parentNode;\n        this.parentNode = parentNode;\n\n        this._parentBoundary = this.parentNode && this.parentNode._boundary;\n        this._headersLines = [];\n        this._headerlen = 0;\n\n        this._parsedContentType = false;\n        this._boundary = false;\n\n        this.multipart = false;\n        this.encoding = false;\n        this.headers = false;\n        this.contentType = false;\n        this.flowed = false;\n        this.delSp = false;\n\n        this.config = config || {};\n        this.libmime = new libmime.Libmime({ Iconv: this.config.Iconv });\n\n        this.parentPartNumber = (parentNode && this.partNr) || [];\n        this.partNr = false; // resolved later\n        this.childPartNumbers = 0;\n    }\n\n    getPartNr(provided) {\n        if (provided) {\n            return []\n                .concat(this.partNr || [])\n                .filter(nr => !isNaN(nr))\n                .concat(provided);\n        }\n        let childPartNr = ++this.childPartNumbers;\n        return []\n            .concat(this.partNr || [])\n            .filter(nr => !isNaN(nr))\n            .concat(childPartNr);\n    }\n\n    addHeaderChunk(line) {\n        if (!line) {\n            return;\n        }\n        this._headersLines.push(line);\n        this._headerlen += line.length;\n    }\n\n    parseHeaders() {\n        if (this.headers) {\n            return;\n        }\n        this.headers = new Headers(Buffer.concat(this._headersLines, this._headerlen), this.config);\n\n        this._parsedContentDisposition = this.libmime.parseHeaderValue(this.headers.getFirst('Content-Disposition'));\n\n        // if content-type is missing default to plaintext\n        let contentHeader;\n        if (this.headers.get('Content-Type').length) {\n            contentHeader = this.headers.getFirst('Content-Type');\n        } else {\n            if (this._parsedContentDisposition.params.filename) {\n                let extension = pathlib.parse(this._parsedContentDisposition.params.filename).ext.replace(/^\\./, '');\n                if (extension) {\n                    contentHeader = libmime.detectMimeType(extension);\n                }\n            }\n            if (!contentHeader) {\n                if (/^attachment$/i.test(this._parsedContentDisposition.value)) {\n                    contentHeader = 'application/octet-stream';\n                } else {\n                    contentHeader = 'text/plain';\n                }\n            }\n        }\n\n        this._parsedContentType = this.libmime.parseHeaderValue(contentHeader);\n\n        this.encoding = this.headers\n            .getFirst('Content-Transfer-Encoding')\n            .replace(/\\(.*\\)/g, '')\n            .toLowerCase()\n            .trim();\n        this.contentType = (this._parsedContentType.value || '').toLowerCase().trim() || false;\n        this.charset = this._parsedContentType.params.charset || false;\n        this.disposition = (this._parsedContentDisposition.value || '').toLowerCase().trim() || false;\n\n        // fix invalidly encoded disposition values\n        if (this.disposition) {\n            try {\n                this.disposition = this.libmime.decodeWords(this.disposition);\n            } catch (E) {\n                // failed to parse disposition, keep as is (most probably an unknown charset is used)\n            }\n        }\n\n        this.filename = this._parsedContentDisposition.params.filename || this._parsedContentType.params.name || false;\n\n        if (this._parsedContentType.params.format && this._parsedContentType.params.format.toLowerCase().trim() === 'flowed') {\n            this.flowed = true;\n            if (this._parsedContentType.params.delsp && this._parsedContentType.params.delsp.toLowerCase().trim() === 'yes') {\n                this.delSp = true;\n            }\n        }\n\n        if (this.filename) {\n            try {\n                this.filename = this.libmime.decodeWords(this.filename);\n            } catch (E) {\n                // failed to parse filename, keep as is (most probably an unknown charset is used)\n            }\n        }\n\n        this.multipart =\n            (this.contentType &&\n                this.contentType.substr(0, this.contentType.indexOf('/')) === 'multipart' &&\n                this.contentType.substr(this.contentType.indexOf('/') + 1)) ||\n            false;\n        this._boundary = (this._parsedContentType.params.boundary && Buffer.from(this._parsedContentType.params.boundary)) || false;\n\n        this.rfc822 = this.contentType === 'message/rfc822';\n\n        if (!this.parentNode || this.parentNode.rfc822) {\n            this.partNr = this.parentNode ? this.parentNode.getPartNr('TEXT') : ['TEXT'];\n        } else {\n            this.partNr = this.parentNode ? this.parentNode.getPartNr() : [];\n        }\n    }\n\n    getHeaders() {\n        if (!this.headers) {\n            this.parseHeaders();\n        }\n        return this.headers.build();\n    }\n\n    setContentType(contentType) {\n        if (!this.headers) {\n            this.parseHeaders();\n        }\n\n        contentType = (contentType || '').toLowerCase().trim();\n        if (contentType) {\n            this._parsedContentType.value = contentType;\n        }\n\n        if (!this.flowed && this._parsedContentType.params.format) {\n            delete this._parsedContentType.params.format;\n        }\n\n        if (!this.delSp && this._parsedContentType.params.delsp) {\n            delete this._parsedContentType.params.delsp;\n        }\n\n        this.headers.update('Content-Type', this.libmime.buildHeaderValue(this._parsedContentType));\n    }\n\n    setCharset(charset) {\n        if (!this.headers) {\n            this.parseHeaders();\n        }\n\n        charset = (charset || '').toLowerCase().trim();\n\n        if (charset === 'ascii') {\n            charset = '';\n        }\n\n        if (!charset) {\n            if (!this._parsedContentType.value) {\n                // nothing to set or update\n                return;\n            }\n            delete this._parsedContentType.params.charset;\n        } else {\n            this._parsedContentType.params.charset = charset;\n        }\n\n        if (!this._parsedContentType.value) {\n            this._parsedContentType.value = 'text/plain';\n        }\n\n        this.headers.update('Content-Type', this.libmime.buildHeaderValue(this._parsedContentType));\n    }\n\n    setFilename(filename) {\n        if (!this.headers) {\n            this.parseHeaders();\n        }\n\n        this.filename = (filename || '').toLowerCase().trim();\n\n        if (this._parsedContentType.params.name) {\n            delete this._parsedContentType.params.name;\n            this.headers.update('Content-Type', this.libmime.buildHeaderValue(this._parsedContentType));\n        }\n\n        if (!this.filename) {\n            if (!this._parsedContentDisposition.value) {\n                // nothing to set or update\n                return;\n            }\n            delete this._parsedContentDisposition.params.filename;\n        } else {\n            this._parsedContentDisposition.params.filename = this.filename;\n        }\n\n        if (!this._parsedContentDisposition.value) {\n            this._parsedContentDisposition.value = 'attachment';\n        }\n\n        this.headers.update('Content-Disposition', this.libmime.buildHeaderValue(this._parsedContentDisposition));\n    }\n\n    getDecoder() {\n        if (!this.headers) {\n            this.parseHeaders();\n        }\n\n        switch (this.encoding) {\n            case 'base64':\n                return new libbase64.Decoder();\n            case 'quoted-printable':\n                return new libqp.Decoder();\n            default:\n                return new PassThrough();\n        }\n    }\n\n    getEncoder(encoding) {\n        if (!this.headers) {\n            this.parseHeaders();\n        }\n\n        encoding = (encoding || '').toString().toLowerCase().trim();\n\n        if (encoding && encoding !== this.encoding) {\n            this.headers.update('Content-Transfer-Encoding', encoding);\n        } else {\n            encoding = this.encoding;\n        }\n\n        switch (encoding) {\n            case 'base64':\n                return new libbase64.Encoder();\n            case 'quoted-printable':\n                return new libqp.Encoder();\n            default:\n                return new PassThrough();\n        }\n    }\n}\n\nmodule.exports = MimeNode;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mailparser/node_modules/mailsplit/lib/mime-node.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mailparser/node_modules/mailsplit/lib/node-rewriter.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/mailparser/node_modules/mailsplit/lib/node-rewriter.js ***!
  \*****************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n// Helper class to rewrite nodes with specific mime type\n\nconst Transform = (__webpack_require__(/*! stream */ \"stream\").Transform);\nconst FlowedDecoder = __webpack_require__(/*! ./flowed-decoder */ \"(rsc)/./node_modules/mailparser/node_modules/mailsplit/lib/flowed-decoder.js\");\n\n/**\n * NodeRewriter Transform stream. Updates content for all nodes with specified mime type\n *\n * @constructor\n * @param {String} mimeType Define the Mime-Type to look for\n * @param {Function} rewriteAction Function to run with the node content\n */\nclass NodeRewriter extends Transform {\n    constructor(filterFunc, rewriteAction) {\n        let options = {\n            readableObjectMode: true,\n            writableObjectMode: true\n        };\n        super(options);\n\n        this.filterFunc = filterFunc;\n        this.rewriteAction = rewriteAction;\n\n        this.decoder = false;\n        this.encoder = false;\n        this.continue = false;\n    }\n\n    _transform(data, encoding, callback) {\n        this.processIncoming(data, callback);\n    }\n\n    _flush(callback) {\n        if (this.decoder) {\n            // emit an empty node just in case there is pending data to end\n            return this.processIncoming(\n                {\n                    type: 'none'\n                },\n                callback\n            );\n        }\n        return callback();\n    }\n\n    processIncoming(data, callback) {\n        if (this.decoder && data.type === 'body') {\n            // data to parse\n            if (!this.decoder.write(data.value)) {\n                return this.decoder.once('drain', callback);\n            } else {\n                return callback();\n            }\n        } else if (this.decoder && data.type !== 'body') {\n            // stop decoding.\n            // we can not process the current data chunk as we need to wait until\n            // the parsed data is completely processed, so we store a reference to the\n            // continue callback\n            this.continue = () => {\n                this.continue = false;\n                this.decoder = false;\n                this.encoder = false;\n                this.processIncoming(data, callback);\n            };\n            return this.decoder.end();\n        } else if (data.type === 'node' && this.filterFunc(data)) {\n            // found matching node, create new handler\n            this.emit('node', this.createDecodePair(data));\n        } else if (this.readable && data.type !== 'none') {\n            // we don't care about this data, just pass it over to the joiner\n            this.push(data);\n        }\n        callback();\n    }\n\n    createDecodePair(node) {\n        this.decoder = node.getDecoder();\n\n        if (['base64', 'quoted-printable'].includes(node.encoding)) {\n            this.encoder = node.getEncoder();\n        } else {\n            this.encoder = node.getEncoder('quoted-printable');\n        }\n\n        let lastByte = false;\n\n        let decoder = this.decoder;\n        let encoder = this.encoder;\n        let firstChunk = true;\n        decoder.$reading = false;\n\n        let readFromEncoder = () => {\n            decoder.$reading = true;\n\n            let data = encoder.read();\n            if (data === null) {\n                decoder.$reading = false;\n                return;\n            }\n\n            if (firstChunk) {\n                firstChunk = false;\n                if (this.readable) {\n                    this.push(node);\n                    if (node.type === 'body') {\n                        lastByte = node.value && node.value.length && node.value[node.value.length - 1];\n                    }\n                }\n            }\n\n            let writeMore = true;\n            if (this.readable) {\n                writeMore = this.push({\n                    node,\n                    type: 'body',\n                    value: data\n                });\n                lastByte = data && data.length && data[data.length - 1];\n            }\n\n            if (writeMore) {\n                return setImmediate(readFromEncoder);\n            } else {\n                encoder.pause();\n                // no idea how to catch drain? use timeout for now as poor man's substitute\n                // this.once('drain', () => encoder.resume());\n                setTimeout(() => {\n                    encoder.resume();\n                    setImmediate(readFromEncoder);\n                }, 100);\n            }\n        };\n\n        encoder.on('readable', () => {\n            if (!decoder.$reading) {\n                return readFromEncoder();\n            }\n        });\n\n        encoder.on('end', () => {\n            if (firstChunk) {\n                firstChunk = false;\n                if (this.readable) {\n                    this.push(node);\n                    if (node.type === 'body') {\n                        lastByte = node.value && node.value.length && node.value[node.value.length - 1];\n                    }\n                }\n            }\n\n            if (lastByte !== 0x0a) {\n                // make sure there is a terminating line break\n                this.push({\n                    node,\n                    type: 'body',\n                    value: Buffer.from([0x0a])\n                });\n            }\n\n            if (this.continue) {\n                return this.continue();\n            }\n        });\n\n        if (/^text\\//.test(node.contentType) && node.flowed) {\n            // text/plain; format=flowed is a special case\n            let flowDecoder = decoder;\n            decoder = new FlowedDecoder({\n                delSp: node.delSp,\n                encoding: node.encoding\n            });\n            flowDecoder.on('error', err => {\n                decoder.emit('error', err);\n            });\n            flowDecoder.pipe(decoder);\n\n            // we don't know what kind of data we are going to get, does it comply with the\n            // requirements of format=flowed, so we just cancel it\n            node.flowed = false;\n            node.delSp = false;\n            node.setContentType();\n        }\n\n        return {\n            node,\n            decoder,\n            encoder\n        };\n    }\n}\n\nmodule.exports = NodeRewriter;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mailparser/node_modules/mailsplit/lib/node-rewriter.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mailparser/node_modules/mailsplit/lib/node-streamer.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/mailparser/node_modules/mailsplit/lib/node-streamer.js ***!
  \*****************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n// Helper class to rewrite nodes with specific mime type\n\nconst Transform = (__webpack_require__(/*! stream */ \"stream\").Transform);\nconst FlowedDecoder = __webpack_require__(/*! ./flowed-decoder */ \"(rsc)/./node_modules/mailparser/node_modules/mailsplit/lib/flowed-decoder.js\");\n\n/**\n * NodeRewriter Transform stream. Updates content for all nodes with specified mime type\n *\n * @constructor\n * @param {String} mimeType Define the Mime-Type to look for\n * @param {Function} streamAction Function to run with the node content\n */\nclass NodeStreamer extends Transform {\n    constructor(filterFunc, streamAction) {\n        let options = {\n            readableObjectMode: true,\n            writableObjectMode: true\n        };\n        super(options);\n\n        this.filterFunc = filterFunc;\n        this.streamAction = streamAction;\n\n        this.decoder = false;\n        this.canContinue = false;\n        this.continue = false;\n    }\n\n    _transform(data, encoding, callback) {\n        this.processIncoming(data, callback);\n    }\n\n    _flush(callback) {\n        if (this.decoder) {\n            // emit an empty node just in case there is pending data to end\n            return this.processIncoming(\n                {\n                    type: 'none'\n                },\n                callback\n            );\n        }\n        return callback();\n    }\n\n    processIncoming(data, callback) {\n        if (this.decoder && data.type === 'body') {\n            // data to parse\n            this.push(data);\n            if (!this.decoder.write(data.value)) {\n                return this.decoder.once('drain', callback);\n            } else {\n                return callback();\n            }\n        } else if (this.decoder && data.type !== 'body') {\n            // stop decoding.\n            // we can not process the current data chunk as we need to wait until\n            // the parsed data is completely processed, so we store a reference to the\n            // continue callback\n\n            let doContinue = () => {\n                this.continue = false;\n                this.decoder = false;\n                this.canContinue = false;\n                this.processIncoming(data, callback);\n            };\n\n            if (this.canContinue) {\n                setImmediate(doContinue);\n            } else {\n                this.continue = () => doContinue();\n            }\n\n            return this.decoder.end();\n        } else if (data.type === 'node' && this.filterFunc(data)) {\n            this.push(data);\n            // found matching node, create new handler\n            this.emit('node', this.createDecoder(data));\n        } else if (this.readable && data.type !== 'none') {\n            // we don't care about this data, just pass it over to the joiner\n            this.push(data);\n        }\n        callback();\n    }\n\n    createDecoder(node) {\n        this.decoder = node.getDecoder();\n\n        let decoder = this.decoder;\n        decoder.$reading = false;\n\n        if (/^text\\//.test(node.contentType) && node.flowed) {\n            let flowDecoder = decoder;\n            decoder = new FlowedDecoder({\n                delSp: node.delSp\n            });\n            flowDecoder.on('error', err => {\n                decoder.emit('error', err);\n            });\n            flowDecoder.pipe(decoder);\n        }\n\n        return {\n            node,\n            decoder,\n            done: () => {\n                if (typeof this.continue === 'function') {\n                    // called once input stream is processed\n                    this.continue();\n                } else {\n                    // called before input stream is processed\n                    this.canContinue = true;\n                }\n            }\n        };\n    }\n}\n\nmodule.exports = NodeStreamer;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWFpbHBhcnNlci9ub2RlX21vZHVsZXMvbWFpbHNwbGl0L2xpYi9ub2RlLXN0cmVhbWVyLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViOztBQUVBLGtCQUFrQix1REFBMkI7QUFDN0Msc0JBQXNCLG1CQUFPLENBQUMsc0dBQWtCOztBQUVoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixXQUFXLFVBQVU7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBOztBQUVBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0I7QUFDbEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIkM6XFxDb2RlXFxvbmUtbWFpbFxcbm9kZV9tb2R1bGVzXFxtYWlscGFyc2VyXFxub2RlX21vZHVsZXNcXG1haWxzcGxpdFxcbGliXFxub2RlLXN0cmVhbWVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuLy8gSGVscGVyIGNsYXNzIHRvIHJld3JpdGUgbm9kZXMgd2l0aCBzcGVjaWZpYyBtaW1lIHR5cGVcblxuY29uc3QgVHJhbnNmb3JtID0gcmVxdWlyZSgnc3RyZWFtJykuVHJhbnNmb3JtO1xuY29uc3QgRmxvd2VkRGVjb2RlciA9IHJlcXVpcmUoJy4vZmxvd2VkLWRlY29kZXInKTtcblxuLyoqXG4gKiBOb2RlUmV3cml0ZXIgVHJhbnNmb3JtIHN0cmVhbS4gVXBkYXRlcyBjb250ZW50IGZvciBhbGwgbm9kZXMgd2l0aCBzcGVjaWZpZWQgbWltZSB0eXBlXG4gKlxuICogQGNvbnN0cnVjdG9yXG4gKiBAcGFyYW0ge1N0cmluZ30gbWltZVR5cGUgRGVmaW5lIHRoZSBNaW1lLVR5cGUgdG8gbG9vayBmb3JcbiAqIEBwYXJhbSB7RnVuY3Rpb259IHN0cmVhbUFjdGlvbiBGdW5jdGlvbiB0byBydW4gd2l0aCB0aGUgbm9kZSBjb250ZW50XG4gKi9cbmNsYXNzIE5vZGVTdHJlYW1lciBleHRlbmRzIFRyYW5zZm9ybSB7XG4gICAgY29uc3RydWN0b3IoZmlsdGVyRnVuYywgc3RyZWFtQWN0aW9uKSB7XG4gICAgICAgIGxldCBvcHRpb25zID0ge1xuICAgICAgICAgICAgcmVhZGFibGVPYmplY3RNb2RlOiB0cnVlLFxuICAgICAgICAgICAgd3JpdGFibGVPYmplY3RNb2RlOiB0cnVlXG4gICAgICAgIH07XG4gICAgICAgIHN1cGVyKG9wdGlvbnMpO1xuXG4gICAgICAgIHRoaXMuZmlsdGVyRnVuYyA9IGZpbHRlckZ1bmM7XG4gICAgICAgIHRoaXMuc3RyZWFtQWN0aW9uID0gc3RyZWFtQWN0aW9uO1xuXG4gICAgICAgIHRoaXMuZGVjb2RlciA9IGZhbHNlO1xuICAgICAgICB0aGlzLmNhbkNvbnRpbnVlID0gZmFsc2U7XG4gICAgICAgIHRoaXMuY29udGludWUgPSBmYWxzZTtcbiAgICB9XG5cbiAgICBfdHJhbnNmb3JtKGRhdGEsIGVuY29kaW5nLCBjYWxsYmFjaykge1xuICAgICAgICB0aGlzLnByb2Nlc3NJbmNvbWluZyhkYXRhLCBjYWxsYmFjayk7XG4gICAgfVxuXG4gICAgX2ZsdXNoKGNhbGxiYWNrKSB7XG4gICAgICAgIGlmICh0aGlzLmRlY29kZXIpIHtcbiAgICAgICAgICAgIC8vIGVtaXQgYW4gZW1wdHkgbm9kZSBqdXN0IGluIGNhc2UgdGhlcmUgaXMgcGVuZGluZyBkYXRhIHRvIGVuZFxuICAgICAgICAgICAgcmV0dXJuIHRoaXMucHJvY2Vzc0luY29taW5nKFxuICAgICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICAgICAgdHlwZTogJ25vbmUnXG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICBjYWxsYmFja1xuICAgICAgICAgICAgKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gY2FsbGJhY2soKTtcbiAgICB9XG5cbiAgICBwcm9jZXNzSW5jb21pbmcoZGF0YSwgY2FsbGJhY2spIHtcbiAgICAgICAgaWYgKHRoaXMuZGVjb2RlciAmJiBkYXRhLnR5cGUgPT09ICdib2R5Jykge1xuICAgICAgICAgICAgLy8gZGF0YSB0byBwYXJzZVxuICAgICAgICAgICAgdGhpcy5wdXNoKGRhdGEpO1xuICAgICAgICAgICAgaWYgKCF0aGlzLmRlY29kZXIud3JpdGUoZGF0YS52YWx1ZSkpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gdGhpcy5kZWNvZGVyLm9uY2UoJ2RyYWluJywgY2FsbGJhY2spO1xuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gY2FsbGJhY2soKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSBlbHNlIGlmICh0aGlzLmRlY29kZXIgJiYgZGF0YS50eXBlICE9PSAnYm9keScpIHtcbiAgICAgICAgICAgIC8vIHN0b3AgZGVjb2RpbmcuXG4gICAgICAgICAgICAvLyB3ZSBjYW4gbm90IHByb2Nlc3MgdGhlIGN1cnJlbnQgZGF0YSBjaHVuayBhcyB3ZSBuZWVkIHRvIHdhaXQgdW50aWxcbiAgICAgICAgICAgIC8vIHRoZSBwYXJzZWQgZGF0YSBpcyBjb21wbGV0ZWx5IHByb2Nlc3NlZCwgc28gd2Ugc3RvcmUgYSByZWZlcmVuY2UgdG8gdGhlXG4gICAgICAgICAgICAvLyBjb250aW51ZSBjYWxsYmFja1xuXG4gICAgICAgICAgICBsZXQgZG9Db250aW51ZSA9ICgpID0+IHtcbiAgICAgICAgICAgICAgICB0aGlzLmNvbnRpbnVlID0gZmFsc2U7XG4gICAgICAgICAgICAgICAgdGhpcy5kZWNvZGVyID0gZmFsc2U7XG4gICAgICAgICAgICAgICAgdGhpcy5jYW5Db250aW51ZSA9IGZhbHNlO1xuICAgICAgICAgICAgICAgIHRoaXMucHJvY2Vzc0luY29taW5nKGRhdGEsIGNhbGxiYWNrKTtcbiAgICAgICAgICAgIH07XG5cbiAgICAgICAgICAgIGlmICh0aGlzLmNhbkNvbnRpbnVlKSB7XG4gICAgICAgICAgICAgICAgc2V0SW1tZWRpYXRlKGRvQ29udGludWUpO1xuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICB0aGlzLmNvbnRpbnVlID0gKCkgPT4gZG9Db250aW51ZSgpO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICByZXR1cm4gdGhpcy5kZWNvZGVyLmVuZCgpO1xuICAgICAgICB9IGVsc2UgaWYgKGRhdGEudHlwZSA9PT0gJ25vZGUnICYmIHRoaXMuZmlsdGVyRnVuYyhkYXRhKSkge1xuICAgICAgICAgICAgdGhpcy5wdXNoKGRhdGEpO1xuICAgICAgICAgICAgLy8gZm91bmQgbWF0Y2hpbmcgbm9kZSwgY3JlYXRlIG5ldyBoYW5kbGVyXG4gICAgICAgICAgICB0aGlzLmVtaXQoJ25vZGUnLCB0aGlzLmNyZWF0ZURlY29kZXIoZGF0YSkpO1xuICAgICAgICB9IGVsc2UgaWYgKHRoaXMucmVhZGFibGUgJiYgZGF0YS50eXBlICE9PSAnbm9uZScpIHtcbiAgICAgICAgICAgIC8vIHdlIGRvbid0IGNhcmUgYWJvdXQgdGhpcyBkYXRhLCBqdXN0IHBhc3MgaXQgb3ZlciB0byB0aGUgam9pbmVyXG4gICAgICAgICAgICB0aGlzLnB1c2goZGF0YSk7XG4gICAgICAgIH1cbiAgICAgICAgY2FsbGJhY2soKTtcbiAgICB9XG5cbiAgICBjcmVhdGVEZWNvZGVyKG5vZGUpIHtcbiAgICAgICAgdGhpcy5kZWNvZGVyID0gbm9kZS5nZXREZWNvZGVyKCk7XG5cbiAgICAgICAgbGV0IGRlY29kZXIgPSB0aGlzLmRlY29kZXI7XG4gICAgICAgIGRlY29kZXIuJHJlYWRpbmcgPSBmYWxzZTtcblxuICAgICAgICBpZiAoL150ZXh0XFwvLy50ZXN0KG5vZGUuY29udGVudFR5cGUpICYmIG5vZGUuZmxvd2VkKSB7XG4gICAgICAgICAgICBsZXQgZmxvd0RlY29kZXIgPSBkZWNvZGVyO1xuICAgICAgICAgICAgZGVjb2RlciA9IG5ldyBGbG93ZWREZWNvZGVyKHtcbiAgICAgICAgICAgICAgICBkZWxTcDogbm9kZS5kZWxTcFxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBmbG93RGVjb2Rlci5vbignZXJyb3InLCBlcnIgPT4ge1xuICAgICAgICAgICAgICAgIGRlY29kZXIuZW1pdCgnZXJyb3InLCBlcnIpO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBmbG93RGVjb2Rlci5waXBlKGRlY29kZXIpO1xuICAgICAgICB9XG5cbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIG5vZGUsXG4gICAgICAgICAgICBkZWNvZGVyLFxuICAgICAgICAgICAgZG9uZTogKCkgPT4ge1xuICAgICAgICAgICAgICAgIGlmICh0eXBlb2YgdGhpcy5jb250aW51ZSA9PT0gJ2Z1bmN0aW9uJykge1xuICAgICAgICAgICAgICAgICAgICAvLyBjYWxsZWQgb25jZSBpbnB1dCBzdHJlYW0gaXMgcHJvY2Vzc2VkXG4gICAgICAgICAgICAgICAgICAgIHRoaXMuY29udGludWUoKTtcbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAvLyBjYWxsZWQgYmVmb3JlIGlucHV0IHN0cmVhbSBpcyBwcm9jZXNzZWRcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5jYW5Db250aW51ZSA9IHRydWU7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9O1xuICAgIH1cbn1cblxubW9kdWxlLmV4cG9ydHMgPSBOb2RlU3RyZWFtZXI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mailparser/node_modules/mailsplit/lib/node-streamer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mailparser/node_modules/nodemailer/lib/addressparser/index.js":
/*!************************************************************************************!*\
  !*** ./node_modules/mailparser/node_modules/nodemailer/lib/addressparser/index.js ***!
  \************************************************************************************/
/***/ ((module) => {

eval("\n\n/**\n * Converts tokens for a single address into an address object\n *\n * @param {Array} tokens Tokens object\n * @return {Object} Address object\n */\nfunction _handleAddress(tokens) {\n    let isGroup = false;\n    let state = 'text';\n    let address;\n    let addresses = [];\n    let data = {\n        address: [],\n        comment: [],\n        group: [],\n        text: []\n    };\n    let i;\n    let len;\n\n    // Filter out <addresses>, (comments) and regular text\n    for (i = 0, len = tokens.length; i < len; i++) {\n        let token = tokens[i];\n        let prevToken = i ? tokens[i - 1] : null;\n        if (token.type === 'operator') {\n            switch (token.value) {\n                case '<':\n                    state = 'address';\n                    break;\n                case '(':\n                    state = 'comment';\n                    break;\n                case ':':\n                    state = 'group';\n                    isGroup = true;\n                    break;\n                default:\n                    state = 'text';\n                    break;\n            }\n        } else if (token.value) {\n            if (state === 'address') {\n                // handle use case where unquoted name includes a \"<\"\n                // Apple Mail truncates everything between an unexpected < and an address\n                // and so will we\n                token.value = token.value.replace(/^[^<]*<\\s*/, '');\n            }\n\n            if (prevToken && prevToken.noBreak && data[state].length) {\n                // join values\n                data[state][data[state].length - 1] += token.value;\n            } else {\n                data[state].push(token.value);\n            }\n        }\n    }\n\n    // If there is no text but a comment, replace the two\n    if (!data.text.length && data.comment.length) {\n        data.text = data.comment;\n        data.comment = [];\n    }\n\n    if (isGroup) {\n        // http://tools.ietf.org/html/rfc2822#appendix-A.1.3\n        data.text = data.text.join(' ');\n        addresses.push({\n            name: data.text || (address && address.name),\n            group: data.group.length ? addressparser(data.group.join(',')) : []\n        });\n    } else {\n        // If no address was found, try to detect one from regular text\n        if (!data.address.length && data.text.length) {\n            for (i = data.text.length - 1; i >= 0; i--) {\n                if (data.text[i].match(/^[^@\\s]+@[^@\\s]+$/)) {\n                    data.address = data.text.splice(i, 1);\n                    break;\n                }\n            }\n\n            let _regexHandler = function (address) {\n                if (!data.address.length) {\n                    data.address = [address.trim()];\n                    return ' ';\n                } else {\n                    return address;\n                }\n            };\n\n            // still no address\n            if (!data.address.length) {\n                for (i = data.text.length - 1; i >= 0; i--) {\n                    // fixed the regex to parse email address correctly when email address has more than one @\n                    data.text[i] = data.text[i].replace(/\\s*\\b[^@\\s]+@[^\\s]+\\b\\s*/, _regexHandler).trim();\n                    if (data.address.length) {\n                        break;\n                    }\n                }\n            }\n        }\n\n        // If there's still is no text but a comment exixts, replace the two\n        if (!data.text.length && data.comment.length) {\n            data.text = data.comment;\n            data.comment = [];\n        }\n\n        // Keep only the first address occurence, push others to regular text\n        if (data.address.length > 1) {\n            data.text = data.text.concat(data.address.splice(1));\n        }\n\n        // Join values with spaces\n        data.text = data.text.join(' ');\n        data.address = data.address.join(' ');\n\n        if (!data.address && isGroup) {\n            return [];\n        } else {\n            address = {\n                address: data.address || data.text || '',\n                name: data.text || data.address || ''\n            };\n\n            if (address.address === address.name) {\n                if ((address.address || '').match(/@/)) {\n                    address.name = '';\n                } else {\n                    address.address = '';\n                }\n            }\n\n            addresses.push(address);\n        }\n    }\n\n    return addresses;\n}\n\n/**\n * Creates a Tokenizer object for tokenizing address field strings\n *\n * @constructor\n * @param {String} str Address field string\n */\nclass Tokenizer {\n    constructor(str) {\n        this.str = (str || '').toString();\n        this.operatorCurrent = '';\n        this.operatorExpecting = '';\n        this.node = null;\n        this.escaped = false;\n\n        this.list = [];\n        /**\n         * Operator tokens and which tokens are expected to end the sequence\n         */\n        this.operators = {\n            '\"': '\"',\n            '(': ')',\n            '<': '>',\n            ',': '',\n            ':': ';',\n            // Semicolons are not a legal delimiter per the RFC2822 grammar other\n            // than for terminating a group, but they are also not valid for any\n            // other use in this context.  Given that some mail clients have\n            // historically allowed the semicolon as a delimiter equivalent to the\n            // comma in their UI, it makes sense to treat them the same as a comma\n            // when used outside of a group.\n            ';': ''\n        };\n    }\n\n    /**\n     * Tokenizes the original input string\n     *\n     * @return {Array} An array of operator|text tokens\n     */\n    tokenize() {\n        let list = [];\n\n        for (let i = 0, len = this.str.length; i < len; i++) {\n            let chr = this.str.charAt(i);\n            let nextChr = i < len - 1 ? this.str.charAt(i + 1) : null;\n            this.checkChar(chr, nextChr);\n        }\n\n        this.list.forEach(node => {\n            node.value = (node.value || '').toString().trim();\n            if (node.value) {\n                list.push(node);\n            }\n        });\n\n        return list;\n    }\n\n    /**\n     * Checks if a character is an operator or text and acts accordingly\n     *\n     * @param {String} chr Character from the address field\n     */\n    checkChar(chr, nextChr) {\n        if (this.escaped) {\n            // ignore next condition blocks\n        } else if (chr === this.operatorExpecting) {\n            this.node = {\n                type: 'operator',\n                value: chr\n            };\n\n            if (nextChr && ![' ', '\\t', '\\r', '\\n', ',', ';'].includes(nextChr)) {\n                this.node.noBreak = true;\n            }\n\n            this.list.push(this.node);\n            this.node = null;\n            this.operatorExpecting = '';\n            this.escaped = false;\n\n            return;\n        } else if (!this.operatorExpecting && chr in this.operators) {\n            this.node = {\n                type: 'operator',\n                value: chr\n            };\n            this.list.push(this.node);\n            this.node = null;\n            this.operatorExpecting = this.operators[chr];\n            this.escaped = false;\n            return;\n        } else if (['\"', \"'\"].includes(this.operatorExpecting) && chr === '\\\\') {\n            this.escaped = true;\n            return;\n        }\n\n        if (!this.node) {\n            this.node = {\n                type: 'text',\n                value: ''\n            };\n            this.list.push(this.node);\n        }\n\n        if (chr === '\\n') {\n            // Convert newlines to spaces. Carriage return is ignored as \\r and \\n usually\n            // go together anyway and there already is a WS for \\n. Lone \\r means something is fishy.\n            chr = ' ';\n        }\n\n        if (chr.charCodeAt(0) >= 0x21 || [' ', '\\t'].includes(chr)) {\n            // skip command bytes\n            this.node.value += chr;\n        }\n\n        this.escaped = false;\n    }\n}\n\n/**\n * Parses structured e-mail addresses from an address field\n *\n * Example:\n *\n *    'Name <address@domain>'\n *\n * will be converted to\n *\n *     [{name: 'Name', address: 'address@domain'}]\n *\n * @param {String} str Address field\n * @return {Array} An array of address objects\n */\nfunction addressparser(str, options) {\n    options = options || {};\n\n    let tokenizer = new Tokenizer(str);\n    let tokens = tokenizer.tokenize();\n\n    let addresses = [];\n    let address = [];\n    let parsedAddresses = [];\n\n    tokens.forEach(token => {\n        if (token.type === 'operator' && (token.value === ',' || token.value === ';')) {\n            if (address.length) {\n                addresses.push(address);\n            }\n            address = [];\n        } else {\n            address.push(token);\n        }\n    });\n\n    if (address.length) {\n        addresses.push(address);\n    }\n\n    addresses.forEach(address => {\n        address = _handleAddress(address);\n        if (address.length) {\n            parsedAddresses = parsedAddresses.concat(address);\n        }\n    });\n\n    if (options.flatten) {\n        let addresses = [];\n        let walkAddressList = list => {\n            list.forEach(address => {\n                if (address.group) {\n                    return walkAddressList(address.group);\n                } else {\n                    addresses.push(address);\n                }\n            });\n        };\n        walkAddressList(parsedAddresses);\n        return addresses;\n    }\n\n    return parsedAddresses;\n}\n\n// expose to the world\nmodule.exports = addressparser;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mailparser/node_modules/nodemailer/lib/addressparser/index.js\n");

/***/ })

};
;