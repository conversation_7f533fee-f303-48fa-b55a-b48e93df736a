"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/safe-stable-stringify";
exports.ids = ["vendor-chunks/safe-stable-stringify"];
exports.modules = {

/***/ "(rsc)/./node_modules/safe-stable-stringify/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/safe-stable-stringify/index.js ***!
  \*****************************************************/
/***/ ((module, exports) => {

eval("\n\nconst { hasOwnProperty } = Object.prototype\n\nconst stringify = configure()\n\n// @ts-expect-error\nstringify.configure = configure\n// @ts-expect-error\nstringify.stringify = stringify\n\n// @ts-expect-error\nstringify.default = stringify\n\n// @ts-expect-error used for named export\nexports.stringify = stringify\n// @ts-expect-error used for named export\nexports.configure = configure\n\nmodule.exports = stringify\n\n// eslint-disable-next-line no-control-regex\nconst strEscapeSequencesRegExp = /[\\u0000-\\u001f\\u0022\\u005c\\ud800-\\udfff]/\n\n// Escape C0 control characters, double quotes, the backslash and every code\n// unit with a numeric value in the inclusive range 0xD800 to 0xDFFF.\nfunction strEscape (str) {\n  // Some magic numbers that worked out fine while benchmarking with v8 8.0\n  if (str.length < 5000 && !strEscapeSequencesRegExp.test(str)) {\n    return `\"${str}\"`\n  }\n  return JSON.stringify(str)\n}\n\nfunction sort (array, comparator) {\n  // Insertion sort is very efficient for small input sizes, but it has a bad\n  // worst case complexity. Thus, use native array sort for bigger values.\n  if (array.length > 2e2 || comparator) {\n    return array.sort(comparator)\n  }\n  for (let i = 1; i < array.length; i++) {\n    const currentValue = array[i]\n    let position = i\n    while (position !== 0 && array[position - 1] > currentValue) {\n      array[position] = array[position - 1]\n      position--\n    }\n    array[position] = currentValue\n  }\n  return array\n}\n\nconst typedArrayPrototypeGetSymbolToStringTag =\n  Object.getOwnPropertyDescriptor(\n    Object.getPrototypeOf(\n      Object.getPrototypeOf(\n        new Int8Array()\n      )\n    ),\n    Symbol.toStringTag\n  ).get\n\nfunction isTypedArrayWithEntries (value) {\n  return typedArrayPrototypeGetSymbolToStringTag.call(value) !== undefined && value.length !== 0\n}\n\nfunction stringifyTypedArray (array, separator, maximumBreadth) {\n  if (array.length < maximumBreadth) {\n    maximumBreadth = array.length\n  }\n  const whitespace = separator === ',' ? '' : ' '\n  let res = `\"0\":${whitespace}${array[0]}`\n  for (let i = 1; i < maximumBreadth; i++) {\n    res += `${separator}\"${i}\":${whitespace}${array[i]}`\n  }\n  return res\n}\n\nfunction getCircularValueOption (options) {\n  if (hasOwnProperty.call(options, 'circularValue')) {\n    const circularValue = options.circularValue\n    if (typeof circularValue === 'string') {\n      return `\"${circularValue}\"`\n    }\n    if (circularValue == null) {\n      return circularValue\n    }\n    if (circularValue === Error || circularValue === TypeError) {\n      return {\n        toString () {\n          throw new TypeError('Converting circular structure to JSON')\n        }\n      }\n    }\n    throw new TypeError('The \"circularValue\" argument must be of type string or the value null or undefined')\n  }\n  return '\"[Circular]\"'\n}\n\nfunction getDeterministicOption (options) {\n  let value\n  if (hasOwnProperty.call(options, 'deterministic')) {\n    value = options.deterministic\n    if (typeof value !== 'boolean' && typeof value !== 'function') {\n      throw new TypeError('The \"deterministic\" argument must be of type boolean or comparator function')\n    }\n  }\n  return value === undefined ? true : value\n}\n\nfunction getBooleanOption (options, key) {\n  let value\n  if (hasOwnProperty.call(options, key)) {\n    value = options[key]\n    if (typeof value !== 'boolean') {\n      throw new TypeError(`The \"${key}\" argument must be of type boolean`)\n    }\n  }\n  return value === undefined ? true : value\n}\n\nfunction getPositiveIntegerOption (options, key) {\n  let value\n  if (hasOwnProperty.call(options, key)) {\n    value = options[key]\n    if (typeof value !== 'number') {\n      throw new TypeError(`The \"${key}\" argument must be of type number`)\n    }\n    if (!Number.isInteger(value)) {\n      throw new TypeError(`The \"${key}\" argument must be an integer`)\n    }\n    if (value < 1) {\n      throw new RangeError(`The \"${key}\" argument must be >= 1`)\n    }\n  }\n  return value === undefined ? Infinity : value\n}\n\nfunction getItemCount (number) {\n  if (number === 1) {\n    return '1 item'\n  }\n  return `${number} items`\n}\n\nfunction getUniqueReplacerSet (replacerArray) {\n  const replacerSet = new Set()\n  for (const value of replacerArray) {\n    if (typeof value === 'string' || typeof value === 'number') {\n      replacerSet.add(String(value))\n    }\n  }\n  return replacerSet\n}\n\nfunction getStrictOption (options) {\n  if (hasOwnProperty.call(options, 'strict')) {\n    const value = options.strict\n    if (typeof value !== 'boolean') {\n      throw new TypeError('The \"strict\" argument must be of type boolean')\n    }\n    if (value) {\n      return (value) => {\n        let message = `Object can not safely be stringified. Received type ${typeof value}`\n        if (typeof value !== 'function') message += ` (${value.toString()})`\n        throw new Error(message)\n      }\n    }\n  }\n}\n\nfunction configure (options) {\n  options = { ...options }\n  const fail = getStrictOption(options)\n  if (fail) {\n    if (options.bigint === undefined) {\n      options.bigint = false\n    }\n    if (!('circularValue' in options)) {\n      options.circularValue = Error\n    }\n  }\n  const circularValue = getCircularValueOption(options)\n  const bigint = getBooleanOption(options, 'bigint')\n  const deterministic = getDeterministicOption(options)\n  const comparator = typeof deterministic === 'function' ? deterministic : undefined\n  const maximumDepth = getPositiveIntegerOption(options, 'maximumDepth')\n  const maximumBreadth = getPositiveIntegerOption(options, 'maximumBreadth')\n\n  function stringifyFnReplacer (key, parent, stack, replacer, spacer, indentation) {\n    let value = parent[key]\n\n    if (typeof value === 'object' && value !== null && typeof value.toJSON === 'function') {\n      value = value.toJSON(key)\n    }\n    value = replacer.call(parent, key, value)\n\n    switch (typeof value) {\n      case 'string':\n        return strEscape(value)\n      case 'object': {\n        if (value === null) {\n          return 'null'\n        }\n        if (stack.indexOf(value) !== -1) {\n          return circularValue\n        }\n\n        let res = ''\n        let join = ','\n        const originalIndentation = indentation\n\n        if (Array.isArray(value)) {\n          if (value.length === 0) {\n            return '[]'\n          }\n          if (maximumDepth < stack.length + 1) {\n            return '\"[Array]\"'\n          }\n          stack.push(value)\n          if (spacer !== '') {\n            indentation += spacer\n            res += `\\n${indentation}`\n            join = `,\\n${indentation}`\n          }\n          const maximumValuesToStringify = Math.min(value.length, maximumBreadth)\n          let i = 0\n          for (; i < maximumValuesToStringify - 1; i++) {\n            const tmp = stringifyFnReplacer(String(i), value, stack, replacer, spacer, indentation)\n            res += tmp !== undefined ? tmp : 'null'\n            res += join\n          }\n          const tmp = stringifyFnReplacer(String(i), value, stack, replacer, spacer, indentation)\n          res += tmp !== undefined ? tmp : 'null'\n          if (value.length - 1 > maximumBreadth) {\n            const removedKeys = value.length - maximumBreadth - 1\n            res += `${join}\"... ${getItemCount(removedKeys)} not stringified\"`\n          }\n          if (spacer !== '') {\n            res += `\\n${originalIndentation}`\n          }\n          stack.pop()\n          return `[${res}]`\n        }\n\n        let keys = Object.keys(value)\n        const keyLength = keys.length\n        if (keyLength === 0) {\n          return '{}'\n        }\n        if (maximumDepth < stack.length + 1) {\n          return '\"[Object]\"'\n        }\n        let whitespace = ''\n        let separator = ''\n        if (spacer !== '') {\n          indentation += spacer\n          join = `,\\n${indentation}`\n          whitespace = ' '\n        }\n        const maximumPropertiesToStringify = Math.min(keyLength, maximumBreadth)\n        if (deterministic && !isTypedArrayWithEntries(value)) {\n          keys = sort(keys, comparator)\n        }\n        stack.push(value)\n        for (let i = 0; i < maximumPropertiesToStringify; i++) {\n          const key = keys[i]\n          const tmp = stringifyFnReplacer(key, value, stack, replacer, spacer, indentation)\n          if (tmp !== undefined) {\n            res += `${separator}${strEscape(key)}:${whitespace}${tmp}`\n            separator = join\n          }\n        }\n        if (keyLength > maximumBreadth) {\n          const removedKeys = keyLength - maximumBreadth\n          res += `${separator}\"...\":${whitespace}\"${getItemCount(removedKeys)} not stringified\"`\n          separator = join\n        }\n        if (spacer !== '' && separator.length > 1) {\n          res = `\\n${indentation}${res}\\n${originalIndentation}`\n        }\n        stack.pop()\n        return `{${res}}`\n      }\n      case 'number':\n        return isFinite(value) ? String(value) : fail ? fail(value) : 'null'\n      case 'boolean':\n        return value === true ? 'true' : 'false'\n      case 'undefined':\n        return undefined\n      case 'bigint':\n        if (bigint) {\n          return String(value)\n        }\n        // fallthrough\n      default:\n        return fail ? fail(value) : undefined\n    }\n  }\n\n  function stringifyArrayReplacer (key, value, stack, replacer, spacer, indentation) {\n    if (typeof value === 'object' && value !== null && typeof value.toJSON === 'function') {\n      value = value.toJSON(key)\n    }\n\n    switch (typeof value) {\n      case 'string':\n        return strEscape(value)\n      case 'object': {\n        if (value === null) {\n          return 'null'\n        }\n        if (stack.indexOf(value) !== -1) {\n          return circularValue\n        }\n\n        const originalIndentation = indentation\n        let res = ''\n        let join = ','\n\n        if (Array.isArray(value)) {\n          if (value.length === 0) {\n            return '[]'\n          }\n          if (maximumDepth < stack.length + 1) {\n            return '\"[Array]\"'\n          }\n          stack.push(value)\n          if (spacer !== '') {\n            indentation += spacer\n            res += `\\n${indentation}`\n            join = `,\\n${indentation}`\n          }\n          const maximumValuesToStringify = Math.min(value.length, maximumBreadth)\n          let i = 0\n          for (; i < maximumValuesToStringify - 1; i++) {\n            const tmp = stringifyArrayReplacer(String(i), value[i], stack, replacer, spacer, indentation)\n            res += tmp !== undefined ? tmp : 'null'\n            res += join\n          }\n          const tmp = stringifyArrayReplacer(String(i), value[i], stack, replacer, spacer, indentation)\n          res += tmp !== undefined ? tmp : 'null'\n          if (value.length - 1 > maximumBreadth) {\n            const removedKeys = value.length - maximumBreadth - 1\n            res += `${join}\"... ${getItemCount(removedKeys)} not stringified\"`\n          }\n          if (spacer !== '') {\n            res += `\\n${originalIndentation}`\n          }\n          stack.pop()\n          return `[${res}]`\n        }\n        stack.push(value)\n        let whitespace = ''\n        if (spacer !== '') {\n          indentation += spacer\n          join = `,\\n${indentation}`\n          whitespace = ' '\n        }\n        let separator = ''\n        for (const key of replacer) {\n          const tmp = stringifyArrayReplacer(key, value[key], stack, replacer, spacer, indentation)\n          if (tmp !== undefined) {\n            res += `${separator}${strEscape(key)}:${whitespace}${tmp}`\n            separator = join\n          }\n        }\n        if (spacer !== '' && separator.length > 1) {\n          res = `\\n${indentation}${res}\\n${originalIndentation}`\n        }\n        stack.pop()\n        return `{${res}}`\n      }\n      case 'number':\n        return isFinite(value) ? String(value) : fail ? fail(value) : 'null'\n      case 'boolean':\n        return value === true ? 'true' : 'false'\n      case 'undefined':\n        return undefined\n      case 'bigint':\n        if (bigint) {\n          return String(value)\n        }\n        // fallthrough\n      default:\n        return fail ? fail(value) : undefined\n    }\n  }\n\n  function stringifyIndent (key, value, stack, spacer, indentation) {\n    switch (typeof value) {\n      case 'string':\n        return strEscape(value)\n      case 'object': {\n        if (value === null) {\n          return 'null'\n        }\n        if (typeof value.toJSON === 'function') {\n          value = value.toJSON(key)\n          // Prevent calling `toJSON` again.\n          if (typeof value !== 'object') {\n            return stringifyIndent(key, value, stack, spacer, indentation)\n          }\n          if (value === null) {\n            return 'null'\n          }\n        }\n        if (stack.indexOf(value) !== -1) {\n          return circularValue\n        }\n        const originalIndentation = indentation\n\n        if (Array.isArray(value)) {\n          if (value.length === 0) {\n            return '[]'\n          }\n          if (maximumDepth < stack.length + 1) {\n            return '\"[Array]\"'\n          }\n          stack.push(value)\n          indentation += spacer\n          let res = `\\n${indentation}`\n          const join = `,\\n${indentation}`\n          const maximumValuesToStringify = Math.min(value.length, maximumBreadth)\n          let i = 0\n          for (; i < maximumValuesToStringify - 1; i++) {\n            const tmp = stringifyIndent(String(i), value[i], stack, spacer, indentation)\n            res += tmp !== undefined ? tmp : 'null'\n            res += join\n          }\n          const tmp = stringifyIndent(String(i), value[i], stack, spacer, indentation)\n          res += tmp !== undefined ? tmp : 'null'\n          if (value.length - 1 > maximumBreadth) {\n            const removedKeys = value.length - maximumBreadth - 1\n            res += `${join}\"... ${getItemCount(removedKeys)} not stringified\"`\n          }\n          res += `\\n${originalIndentation}`\n          stack.pop()\n          return `[${res}]`\n        }\n\n        let keys = Object.keys(value)\n        const keyLength = keys.length\n        if (keyLength === 0) {\n          return '{}'\n        }\n        if (maximumDepth < stack.length + 1) {\n          return '\"[Object]\"'\n        }\n        indentation += spacer\n        const join = `,\\n${indentation}`\n        let res = ''\n        let separator = ''\n        let maximumPropertiesToStringify = Math.min(keyLength, maximumBreadth)\n        if (isTypedArrayWithEntries(value)) {\n          res += stringifyTypedArray(value, join, maximumBreadth)\n          keys = keys.slice(value.length)\n          maximumPropertiesToStringify -= value.length\n          separator = join\n        }\n        if (deterministic) {\n          keys = sort(keys, comparator)\n        }\n        stack.push(value)\n        for (let i = 0; i < maximumPropertiesToStringify; i++) {\n          const key = keys[i]\n          const tmp = stringifyIndent(key, value[key], stack, spacer, indentation)\n          if (tmp !== undefined) {\n            res += `${separator}${strEscape(key)}: ${tmp}`\n            separator = join\n          }\n        }\n        if (keyLength > maximumBreadth) {\n          const removedKeys = keyLength - maximumBreadth\n          res += `${separator}\"...\": \"${getItemCount(removedKeys)} not stringified\"`\n          separator = join\n        }\n        if (separator !== '') {\n          res = `\\n${indentation}${res}\\n${originalIndentation}`\n        }\n        stack.pop()\n        return `{${res}}`\n      }\n      case 'number':\n        return isFinite(value) ? String(value) : fail ? fail(value) : 'null'\n      case 'boolean':\n        return value === true ? 'true' : 'false'\n      case 'undefined':\n        return undefined\n      case 'bigint':\n        if (bigint) {\n          return String(value)\n        }\n        // fallthrough\n      default:\n        return fail ? fail(value) : undefined\n    }\n  }\n\n  function stringifySimple (key, value, stack) {\n    switch (typeof value) {\n      case 'string':\n        return strEscape(value)\n      case 'object': {\n        if (value === null) {\n          return 'null'\n        }\n        if (typeof value.toJSON === 'function') {\n          value = value.toJSON(key)\n          // Prevent calling `toJSON` again\n          if (typeof value !== 'object') {\n            return stringifySimple(key, value, stack)\n          }\n          if (value === null) {\n            return 'null'\n          }\n        }\n        if (stack.indexOf(value) !== -1) {\n          return circularValue\n        }\n\n        let res = ''\n\n        const hasLength = value.length !== undefined\n        if (hasLength && Array.isArray(value)) {\n          if (value.length === 0) {\n            return '[]'\n          }\n          if (maximumDepth < stack.length + 1) {\n            return '\"[Array]\"'\n          }\n          stack.push(value)\n          const maximumValuesToStringify = Math.min(value.length, maximumBreadth)\n          let i = 0\n          for (; i < maximumValuesToStringify - 1; i++) {\n            const tmp = stringifySimple(String(i), value[i], stack)\n            res += tmp !== undefined ? tmp : 'null'\n            res += ','\n          }\n          const tmp = stringifySimple(String(i), value[i], stack)\n          res += tmp !== undefined ? tmp : 'null'\n          if (value.length - 1 > maximumBreadth) {\n            const removedKeys = value.length - maximumBreadth - 1\n            res += `,\"... ${getItemCount(removedKeys)} not stringified\"`\n          }\n          stack.pop()\n          return `[${res}]`\n        }\n\n        let keys = Object.keys(value)\n        const keyLength = keys.length\n        if (keyLength === 0) {\n          return '{}'\n        }\n        if (maximumDepth < stack.length + 1) {\n          return '\"[Object]\"'\n        }\n        let separator = ''\n        let maximumPropertiesToStringify = Math.min(keyLength, maximumBreadth)\n        if (hasLength && isTypedArrayWithEntries(value)) {\n          res += stringifyTypedArray(value, ',', maximumBreadth)\n          keys = keys.slice(value.length)\n          maximumPropertiesToStringify -= value.length\n          separator = ','\n        }\n        if (deterministic) {\n          keys = sort(keys, comparator)\n        }\n        stack.push(value)\n        for (let i = 0; i < maximumPropertiesToStringify; i++) {\n          const key = keys[i]\n          const tmp = stringifySimple(key, value[key], stack)\n          if (tmp !== undefined) {\n            res += `${separator}${strEscape(key)}:${tmp}`\n            separator = ','\n          }\n        }\n        if (keyLength > maximumBreadth) {\n          const removedKeys = keyLength - maximumBreadth\n          res += `${separator}\"...\":\"${getItemCount(removedKeys)} not stringified\"`\n        }\n        stack.pop()\n        return `{${res}}`\n      }\n      case 'number':\n        return isFinite(value) ? String(value) : fail ? fail(value) : 'null'\n      case 'boolean':\n        return value === true ? 'true' : 'false'\n      case 'undefined':\n        return undefined\n      case 'bigint':\n        if (bigint) {\n          return String(value)\n        }\n        // fallthrough\n      default:\n        return fail ? fail(value) : undefined\n    }\n  }\n\n  function stringify (value, replacer, space) {\n    if (arguments.length > 1) {\n      let spacer = ''\n      if (typeof space === 'number') {\n        spacer = ' '.repeat(Math.min(space, 10))\n      } else if (typeof space === 'string') {\n        spacer = space.slice(0, 10)\n      }\n      if (replacer != null) {\n        if (typeof replacer === 'function') {\n          return stringifyFnReplacer('', { '': value }, [], replacer, spacer, '')\n        }\n        if (Array.isArray(replacer)) {\n          return stringifyArrayReplacer('', value, [], getUniqueReplacerSet(replacer), spacer, '')\n        }\n      }\n      if (spacer.length !== 0) {\n        return stringifyIndent('', value, [], spacer, '')\n      }\n    }\n    return stringifySimple('', value, [])\n  }\n\n  return stringify\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/safe-stable-stringify/index.js\n");

/***/ })

};
;