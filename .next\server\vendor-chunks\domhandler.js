"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/domhandler";
exports.ids = ["vendor-chunks/domhandler"];
exports.modules = {

/***/ "(rsc)/./node_modules/domhandler/lib/index.js":
/*!**********************************************!*\
  !*** ./node_modules/domhandler/lib/index.js ***!
  \**********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DomHandler = void 0;\nvar domelementtype_1 = __webpack_require__(/*! domelementtype */ \"(rsc)/./node_modules/domelementtype/lib/index.js\");\nvar node_js_1 = __webpack_require__(/*! ./node.js */ \"(rsc)/./node_modules/domhandler/lib/node.js\");\n__exportStar(__webpack_require__(/*! ./node.js */ \"(rsc)/./node_modules/domhandler/lib/node.js\"), exports);\n// Default options\nvar defaultOpts = {\n    withStartIndices: false,\n    withEndIndices: false,\n    xmlMode: false,\n};\nvar DomHandler = /** @class */ (function () {\n    /**\n     * @param callback Called once parsing has completed.\n     * @param options Settings for the handler.\n     * @param elementCB Callback whenever a tag is closed.\n     */\n    function DomHandler(callback, options, elementCB) {\n        /** The elements of the DOM */\n        this.dom = [];\n        /** The root element for the DOM */\n        this.root = new node_js_1.Document(this.dom);\n        /** Indicated whether parsing has been completed. */\n        this.done = false;\n        /** Stack of open tags. */\n        this.tagStack = [this.root];\n        /** A data node that is still being written to. */\n        this.lastNode = null;\n        /** Reference to the parser instance. Used for location information. */\n        this.parser = null;\n        // Make it possible to skip arguments, for backwards-compatibility\n        if (typeof options === \"function\") {\n            elementCB = options;\n            options = defaultOpts;\n        }\n        if (typeof callback === \"object\") {\n            options = callback;\n            callback = undefined;\n        }\n        this.callback = callback !== null && callback !== void 0 ? callback : null;\n        this.options = options !== null && options !== void 0 ? options : defaultOpts;\n        this.elementCB = elementCB !== null && elementCB !== void 0 ? elementCB : null;\n    }\n    DomHandler.prototype.onparserinit = function (parser) {\n        this.parser = parser;\n    };\n    // Resets the handler back to starting state\n    DomHandler.prototype.onreset = function () {\n        this.dom = [];\n        this.root = new node_js_1.Document(this.dom);\n        this.done = false;\n        this.tagStack = [this.root];\n        this.lastNode = null;\n        this.parser = null;\n    };\n    // Signals the handler that parsing is done\n    DomHandler.prototype.onend = function () {\n        if (this.done)\n            return;\n        this.done = true;\n        this.parser = null;\n        this.handleCallback(null);\n    };\n    DomHandler.prototype.onerror = function (error) {\n        this.handleCallback(error);\n    };\n    DomHandler.prototype.onclosetag = function () {\n        this.lastNode = null;\n        var elem = this.tagStack.pop();\n        if (this.options.withEndIndices) {\n            elem.endIndex = this.parser.endIndex;\n        }\n        if (this.elementCB)\n            this.elementCB(elem);\n    };\n    DomHandler.prototype.onopentag = function (name, attribs) {\n        var type = this.options.xmlMode ? domelementtype_1.ElementType.Tag : undefined;\n        var element = new node_js_1.Element(name, attribs, undefined, type);\n        this.addNode(element);\n        this.tagStack.push(element);\n    };\n    DomHandler.prototype.ontext = function (data) {\n        var lastNode = this.lastNode;\n        if (lastNode && lastNode.type === domelementtype_1.ElementType.Text) {\n            lastNode.data += data;\n            if (this.options.withEndIndices) {\n                lastNode.endIndex = this.parser.endIndex;\n            }\n        }\n        else {\n            var node = new node_js_1.Text(data);\n            this.addNode(node);\n            this.lastNode = node;\n        }\n    };\n    DomHandler.prototype.oncomment = function (data) {\n        if (this.lastNode && this.lastNode.type === domelementtype_1.ElementType.Comment) {\n            this.lastNode.data += data;\n            return;\n        }\n        var node = new node_js_1.Comment(data);\n        this.addNode(node);\n        this.lastNode = node;\n    };\n    DomHandler.prototype.oncommentend = function () {\n        this.lastNode = null;\n    };\n    DomHandler.prototype.oncdatastart = function () {\n        var text = new node_js_1.Text(\"\");\n        var node = new node_js_1.CDATA([text]);\n        this.addNode(node);\n        text.parent = node;\n        this.lastNode = text;\n    };\n    DomHandler.prototype.oncdataend = function () {\n        this.lastNode = null;\n    };\n    DomHandler.prototype.onprocessinginstruction = function (name, data) {\n        var node = new node_js_1.ProcessingInstruction(name, data);\n        this.addNode(node);\n    };\n    DomHandler.prototype.handleCallback = function (error) {\n        if (typeof this.callback === \"function\") {\n            this.callback(error, this.dom);\n        }\n        else if (error) {\n            throw error;\n        }\n    };\n    DomHandler.prototype.addNode = function (node) {\n        var parent = this.tagStack[this.tagStack.length - 1];\n        var previousSibling = parent.children[parent.children.length - 1];\n        if (this.options.withStartIndices) {\n            node.startIndex = this.parser.startIndex;\n        }\n        if (this.options.withEndIndices) {\n            node.endIndex = this.parser.endIndex;\n        }\n        parent.children.push(node);\n        if (previousSibling) {\n            node.prev = previousSibling;\n            previousSibling.next = node;\n        }\n        node.parent = parent;\n        this.lastNode = null;\n    };\n    return DomHandler;\n}());\nexports.DomHandler = DomHandler;\nexports[\"default\"] = DomHandler;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZG9taGFuZGxlci9saWIvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsb0NBQW9DO0FBQ25EO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsa0JBQWtCO0FBQ2xCLHVCQUF1QixtQkFBTyxDQUFDLHdFQUFnQjtBQUMvQyxnQkFBZ0IsbUJBQU8sQ0FBQyw4REFBVztBQUNuQyxhQUFhLG1CQUFPLENBQUMsOERBQVc7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Qsa0JBQWtCO0FBQ2xCLGtCQUFlIiwic291cmNlcyI6WyJDOlxcQ29kZVxcb25lLW1haWxcXG5vZGVfbW9kdWxlc1xcZG9taGFuZGxlclxcbGliXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbnZhciBfX2NyZWF0ZUJpbmRpbmcgPSAodGhpcyAmJiB0aGlzLl9fY3JlYXRlQmluZGluZykgfHwgKE9iamVjdC5jcmVhdGUgPyAoZnVuY3Rpb24obywgbSwgaywgazIpIHtcbiAgICBpZiAoazIgPT09IHVuZGVmaW5lZCkgazIgPSBrO1xuICAgIHZhciBkZXNjID0gT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcihtLCBrKTtcbiAgICBpZiAoIWRlc2MgfHwgKFwiZ2V0XCIgaW4gZGVzYyA/ICFtLl9fZXNNb2R1bGUgOiBkZXNjLndyaXRhYmxlIHx8IGRlc2MuY29uZmlndXJhYmxlKSkge1xuICAgICAgZGVzYyA9IHsgZW51bWVyYWJsZTogdHJ1ZSwgZ2V0OiBmdW5jdGlvbigpIHsgcmV0dXJuIG1ba107IH0gfTtcbiAgICB9XG4gICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KG8sIGsyLCBkZXNjKTtcbn0pIDogKGZ1bmN0aW9uKG8sIG0sIGssIGsyKSB7XG4gICAgaWYgKGsyID09PSB1bmRlZmluZWQpIGsyID0gaztcbiAgICBvW2syXSA9IG1ba107XG59KSk7XG52YXIgX19leHBvcnRTdGFyID0gKHRoaXMgJiYgdGhpcy5fX2V4cG9ydFN0YXIpIHx8IGZ1bmN0aW9uKG0sIGV4cG9ydHMpIHtcbiAgICBmb3IgKHZhciBwIGluIG0pIGlmIChwICE9PSBcImRlZmF1bHRcIiAmJiAhT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKGV4cG9ydHMsIHApKSBfX2NyZWF0ZUJpbmRpbmcoZXhwb3J0cywgbSwgcCk7XG59O1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5Eb21IYW5kbGVyID0gdm9pZCAwO1xudmFyIGRvbWVsZW1lbnR0eXBlXzEgPSByZXF1aXJlKFwiZG9tZWxlbWVudHR5cGVcIik7XG52YXIgbm9kZV9qc18xID0gcmVxdWlyZShcIi4vbm9kZS5qc1wiKTtcbl9fZXhwb3J0U3RhcihyZXF1aXJlKFwiLi9ub2RlLmpzXCIpLCBleHBvcnRzKTtcbi8vIERlZmF1bHQgb3B0aW9uc1xudmFyIGRlZmF1bHRPcHRzID0ge1xuICAgIHdpdGhTdGFydEluZGljZXM6IGZhbHNlLFxuICAgIHdpdGhFbmRJbmRpY2VzOiBmYWxzZSxcbiAgICB4bWxNb2RlOiBmYWxzZSxcbn07XG52YXIgRG9tSGFuZGxlciA9IC8qKiBAY2xhc3MgKi8gKGZ1bmN0aW9uICgpIHtcbiAgICAvKipcbiAgICAgKiBAcGFyYW0gY2FsbGJhY2sgQ2FsbGVkIG9uY2UgcGFyc2luZyBoYXMgY29tcGxldGVkLlxuICAgICAqIEBwYXJhbSBvcHRpb25zIFNldHRpbmdzIGZvciB0aGUgaGFuZGxlci5cbiAgICAgKiBAcGFyYW0gZWxlbWVudENCIENhbGxiYWNrIHdoZW5ldmVyIGEgdGFnIGlzIGNsb3NlZC5cbiAgICAgKi9cbiAgICBmdW5jdGlvbiBEb21IYW5kbGVyKGNhbGxiYWNrLCBvcHRpb25zLCBlbGVtZW50Q0IpIHtcbiAgICAgICAgLyoqIFRoZSBlbGVtZW50cyBvZiB0aGUgRE9NICovXG4gICAgICAgIHRoaXMuZG9tID0gW107XG4gICAgICAgIC8qKiBUaGUgcm9vdCBlbGVtZW50IGZvciB0aGUgRE9NICovXG4gICAgICAgIHRoaXMucm9vdCA9IG5ldyBub2RlX2pzXzEuRG9jdW1lbnQodGhpcy5kb20pO1xuICAgICAgICAvKiogSW5kaWNhdGVkIHdoZXRoZXIgcGFyc2luZyBoYXMgYmVlbiBjb21wbGV0ZWQuICovXG4gICAgICAgIHRoaXMuZG9uZSA9IGZhbHNlO1xuICAgICAgICAvKiogU3RhY2sgb2Ygb3BlbiB0YWdzLiAqL1xuICAgICAgICB0aGlzLnRhZ1N0YWNrID0gW3RoaXMucm9vdF07XG4gICAgICAgIC8qKiBBIGRhdGEgbm9kZSB0aGF0IGlzIHN0aWxsIGJlaW5nIHdyaXR0ZW4gdG8uICovXG4gICAgICAgIHRoaXMubGFzdE5vZGUgPSBudWxsO1xuICAgICAgICAvKiogUmVmZXJlbmNlIHRvIHRoZSBwYXJzZXIgaW5zdGFuY2UuIFVzZWQgZm9yIGxvY2F0aW9uIGluZm9ybWF0aW9uLiAqL1xuICAgICAgICB0aGlzLnBhcnNlciA9IG51bGw7XG4gICAgICAgIC8vIE1ha2UgaXQgcG9zc2libGUgdG8gc2tpcCBhcmd1bWVudHMsIGZvciBiYWNrd2FyZHMtY29tcGF0aWJpbGl0eVxuICAgICAgICBpZiAodHlwZW9mIG9wdGlvbnMgPT09IFwiZnVuY3Rpb25cIikge1xuICAgICAgICAgICAgZWxlbWVudENCID0gb3B0aW9ucztcbiAgICAgICAgICAgIG9wdGlvbnMgPSBkZWZhdWx0T3B0cztcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGNhbGxiYWNrID09PSBcIm9iamVjdFwiKSB7XG4gICAgICAgICAgICBvcHRpb25zID0gY2FsbGJhY2s7XG4gICAgICAgICAgICBjYWxsYmFjayA9IHVuZGVmaW5lZDtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLmNhbGxiYWNrID0gY2FsbGJhY2sgIT09IG51bGwgJiYgY2FsbGJhY2sgIT09IHZvaWQgMCA/IGNhbGxiYWNrIDogbnVsbDtcbiAgICAgICAgdGhpcy5vcHRpb25zID0gb3B0aW9ucyAhPT0gbnVsbCAmJiBvcHRpb25zICE9PSB2b2lkIDAgPyBvcHRpb25zIDogZGVmYXVsdE9wdHM7XG4gICAgICAgIHRoaXMuZWxlbWVudENCID0gZWxlbWVudENCICE9PSBudWxsICYmIGVsZW1lbnRDQiAhPT0gdm9pZCAwID8gZWxlbWVudENCIDogbnVsbDtcbiAgICB9XG4gICAgRG9tSGFuZGxlci5wcm90b3R5cGUub25wYXJzZXJpbml0ID0gZnVuY3Rpb24gKHBhcnNlcikge1xuICAgICAgICB0aGlzLnBhcnNlciA9IHBhcnNlcjtcbiAgICB9O1xuICAgIC8vIFJlc2V0cyB0aGUgaGFuZGxlciBiYWNrIHRvIHN0YXJ0aW5nIHN0YXRlXG4gICAgRG9tSGFuZGxlci5wcm90b3R5cGUub25yZXNldCA9IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgdGhpcy5kb20gPSBbXTtcbiAgICAgICAgdGhpcy5yb290ID0gbmV3IG5vZGVfanNfMS5Eb2N1bWVudCh0aGlzLmRvbSk7XG4gICAgICAgIHRoaXMuZG9uZSA9IGZhbHNlO1xuICAgICAgICB0aGlzLnRhZ1N0YWNrID0gW3RoaXMucm9vdF07XG4gICAgICAgIHRoaXMubGFzdE5vZGUgPSBudWxsO1xuICAgICAgICB0aGlzLnBhcnNlciA9IG51bGw7XG4gICAgfTtcbiAgICAvLyBTaWduYWxzIHRoZSBoYW5kbGVyIHRoYXQgcGFyc2luZyBpcyBkb25lXG4gICAgRG9tSGFuZGxlci5wcm90b3R5cGUub25lbmQgPSBmdW5jdGlvbiAoKSB7XG4gICAgICAgIGlmICh0aGlzLmRvbmUpXG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIHRoaXMuZG9uZSA9IHRydWU7XG4gICAgICAgIHRoaXMucGFyc2VyID0gbnVsbDtcbiAgICAgICAgdGhpcy5oYW5kbGVDYWxsYmFjayhudWxsKTtcbiAgICB9O1xuICAgIERvbUhhbmRsZXIucHJvdG90eXBlLm9uZXJyb3IgPSBmdW5jdGlvbiAoZXJyb3IpIHtcbiAgICAgICAgdGhpcy5oYW5kbGVDYWxsYmFjayhlcnJvcik7XG4gICAgfTtcbiAgICBEb21IYW5kbGVyLnByb3RvdHlwZS5vbmNsb3NldGFnID0gZnVuY3Rpb24gKCkge1xuICAgICAgICB0aGlzLmxhc3ROb2RlID0gbnVsbDtcbiAgICAgICAgdmFyIGVsZW0gPSB0aGlzLnRhZ1N0YWNrLnBvcCgpO1xuICAgICAgICBpZiAodGhpcy5vcHRpb25zLndpdGhFbmRJbmRpY2VzKSB7XG4gICAgICAgICAgICBlbGVtLmVuZEluZGV4ID0gdGhpcy5wYXJzZXIuZW5kSW5kZXg7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHRoaXMuZWxlbWVudENCKVxuICAgICAgICAgICAgdGhpcy5lbGVtZW50Q0IoZWxlbSk7XG4gICAgfTtcbiAgICBEb21IYW5kbGVyLnByb3RvdHlwZS5vbm9wZW50YWcgPSBmdW5jdGlvbiAobmFtZSwgYXR0cmlicykge1xuICAgICAgICB2YXIgdHlwZSA9IHRoaXMub3B0aW9ucy54bWxNb2RlID8gZG9tZWxlbWVudHR5cGVfMS5FbGVtZW50VHlwZS5UYWcgOiB1bmRlZmluZWQ7XG4gICAgICAgIHZhciBlbGVtZW50ID0gbmV3IG5vZGVfanNfMS5FbGVtZW50KG5hbWUsIGF0dHJpYnMsIHVuZGVmaW5lZCwgdHlwZSk7XG4gICAgICAgIHRoaXMuYWRkTm9kZShlbGVtZW50KTtcbiAgICAgICAgdGhpcy50YWdTdGFjay5wdXNoKGVsZW1lbnQpO1xuICAgIH07XG4gICAgRG9tSGFuZGxlci5wcm90b3R5cGUub250ZXh0ID0gZnVuY3Rpb24gKGRhdGEpIHtcbiAgICAgICAgdmFyIGxhc3ROb2RlID0gdGhpcy5sYXN0Tm9kZTtcbiAgICAgICAgaWYgKGxhc3ROb2RlICYmIGxhc3ROb2RlLnR5cGUgPT09IGRvbWVsZW1lbnR0eXBlXzEuRWxlbWVudFR5cGUuVGV4dCkge1xuICAgICAgICAgICAgbGFzdE5vZGUuZGF0YSArPSBkYXRhO1xuICAgICAgICAgICAgaWYgKHRoaXMub3B0aW9ucy53aXRoRW5kSW5kaWNlcykge1xuICAgICAgICAgICAgICAgIGxhc3ROb2RlLmVuZEluZGV4ID0gdGhpcy5wYXJzZXIuZW5kSW5kZXg7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICB2YXIgbm9kZSA9IG5ldyBub2RlX2pzXzEuVGV4dChkYXRhKTtcbiAgICAgICAgICAgIHRoaXMuYWRkTm9kZShub2RlKTtcbiAgICAgICAgICAgIHRoaXMubGFzdE5vZGUgPSBub2RlO1xuICAgICAgICB9XG4gICAgfTtcbiAgICBEb21IYW5kbGVyLnByb3RvdHlwZS5vbmNvbW1lbnQgPSBmdW5jdGlvbiAoZGF0YSkge1xuICAgICAgICBpZiAodGhpcy5sYXN0Tm9kZSAmJiB0aGlzLmxhc3ROb2RlLnR5cGUgPT09IGRvbWVsZW1lbnR0eXBlXzEuRWxlbWVudFR5cGUuQ29tbWVudCkge1xuICAgICAgICAgICAgdGhpcy5sYXN0Tm9kZS5kYXRhICs9IGRhdGE7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgdmFyIG5vZGUgPSBuZXcgbm9kZV9qc18xLkNvbW1lbnQoZGF0YSk7XG4gICAgICAgIHRoaXMuYWRkTm9kZShub2RlKTtcbiAgICAgICAgdGhpcy5sYXN0Tm9kZSA9IG5vZGU7XG4gICAgfTtcbiAgICBEb21IYW5kbGVyLnByb3RvdHlwZS5vbmNvbW1lbnRlbmQgPSBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHRoaXMubGFzdE5vZGUgPSBudWxsO1xuICAgIH07XG4gICAgRG9tSGFuZGxlci5wcm90b3R5cGUub25jZGF0YXN0YXJ0ID0gZnVuY3Rpb24gKCkge1xuICAgICAgICB2YXIgdGV4dCA9IG5ldyBub2RlX2pzXzEuVGV4dChcIlwiKTtcbiAgICAgICAgdmFyIG5vZGUgPSBuZXcgbm9kZV9qc18xLkNEQVRBKFt0ZXh0XSk7XG4gICAgICAgIHRoaXMuYWRkTm9kZShub2RlKTtcbiAgICAgICAgdGV4dC5wYXJlbnQgPSBub2RlO1xuICAgICAgICB0aGlzLmxhc3ROb2RlID0gdGV4dDtcbiAgICB9O1xuICAgIERvbUhhbmRsZXIucHJvdG90eXBlLm9uY2RhdGFlbmQgPSBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHRoaXMubGFzdE5vZGUgPSBudWxsO1xuICAgIH07XG4gICAgRG9tSGFuZGxlci5wcm90b3R5cGUub25wcm9jZXNzaW5naW5zdHJ1Y3Rpb24gPSBmdW5jdGlvbiAobmFtZSwgZGF0YSkge1xuICAgICAgICB2YXIgbm9kZSA9IG5ldyBub2RlX2pzXzEuUHJvY2Vzc2luZ0luc3RydWN0aW9uKG5hbWUsIGRhdGEpO1xuICAgICAgICB0aGlzLmFkZE5vZGUobm9kZSk7XG4gICAgfTtcbiAgICBEb21IYW5kbGVyLnByb3RvdHlwZS5oYW5kbGVDYWxsYmFjayA9IGZ1bmN0aW9uIChlcnJvcikge1xuICAgICAgICBpZiAodHlwZW9mIHRoaXMuY2FsbGJhY2sgPT09IFwiZnVuY3Rpb25cIikge1xuICAgICAgICAgICAgdGhpcy5jYWxsYmFjayhlcnJvciwgdGhpcy5kb20pO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKGVycm9yKSB7XG4gICAgICAgICAgICB0aHJvdyBlcnJvcjtcbiAgICAgICAgfVxuICAgIH07XG4gICAgRG9tSGFuZGxlci5wcm90b3R5cGUuYWRkTm9kZSA9IGZ1bmN0aW9uIChub2RlKSB7XG4gICAgICAgIHZhciBwYXJlbnQgPSB0aGlzLnRhZ1N0YWNrW3RoaXMudGFnU3RhY2subGVuZ3RoIC0gMV07XG4gICAgICAgIHZhciBwcmV2aW91c1NpYmxpbmcgPSBwYXJlbnQuY2hpbGRyZW5bcGFyZW50LmNoaWxkcmVuLmxlbmd0aCAtIDFdO1xuICAgICAgICBpZiAodGhpcy5vcHRpb25zLndpdGhTdGFydEluZGljZXMpIHtcbiAgICAgICAgICAgIG5vZGUuc3RhcnRJbmRleCA9IHRoaXMucGFyc2VyLnN0YXJ0SW5kZXg7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHRoaXMub3B0aW9ucy53aXRoRW5kSW5kaWNlcykge1xuICAgICAgICAgICAgbm9kZS5lbmRJbmRleCA9IHRoaXMucGFyc2VyLmVuZEluZGV4O1xuICAgICAgICB9XG4gICAgICAgIHBhcmVudC5jaGlsZHJlbi5wdXNoKG5vZGUpO1xuICAgICAgICBpZiAocHJldmlvdXNTaWJsaW5nKSB7XG4gICAgICAgICAgICBub2RlLnByZXYgPSBwcmV2aW91c1NpYmxpbmc7XG4gICAgICAgICAgICBwcmV2aW91c1NpYmxpbmcubmV4dCA9IG5vZGU7XG4gICAgICAgIH1cbiAgICAgICAgbm9kZS5wYXJlbnQgPSBwYXJlbnQ7XG4gICAgICAgIHRoaXMubGFzdE5vZGUgPSBudWxsO1xuICAgIH07XG4gICAgcmV0dXJuIERvbUhhbmRsZXI7XG59KCkpO1xuZXhwb3J0cy5Eb21IYW5kbGVyID0gRG9tSGFuZGxlcjtcbmV4cG9ydHMuZGVmYXVsdCA9IERvbUhhbmRsZXI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domhandler/lib/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/domhandler/lib/node.js":
/*!*********************************************!*\
  !*** ./node_modules/domhandler/lib/node.js ***!
  \*********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.cloneNode = exports.hasChildren = exports.isDocument = exports.isDirective = exports.isComment = exports.isText = exports.isCDATA = exports.isTag = exports.Element = exports.Document = exports.CDATA = exports.NodeWithChildren = exports.ProcessingInstruction = exports.Comment = exports.Text = exports.DataNode = exports.Node = void 0;\nvar domelementtype_1 = __webpack_require__(/*! domelementtype */ \"(rsc)/./node_modules/domelementtype/lib/index.js\");\n/**\n * This object will be used as the prototype for Nodes when creating a\n * DOM-Level-1-compliant structure.\n */\nvar Node = /** @class */ (function () {\n    function Node() {\n        /** Parent of the node */\n        this.parent = null;\n        /** Previous sibling */\n        this.prev = null;\n        /** Next sibling */\n        this.next = null;\n        /** The start index of the node. Requires `withStartIndices` on the handler to be `true. */\n        this.startIndex = null;\n        /** The end index of the node. Requires `withEndIndices` on the handler to be `true. */\n        this.endIndex = null;\n    }\n    Object.defineProperty(Node.prototype, \"parentNode\", {\n        // Read-write aliases for properties\n        /**\n         * Same as {@link parent}.\n         * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n         */\n        get: function () {\n            return this.parent;\n        },\n        set: function (parent) {\n            this.parent = parent;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Node.prototype, \"previousSibling\", {\n        /**\n         * Same as {@link prev}.\n         * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n         */\n        get: function () {\n            return this.prev;\n        },\n        set: function (prev) {\n            this.prev = prev;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Node.prototype, \"nextSibling\", {\n        /**\n         * Same as {@link next}.\n         * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n         */\n        get: function () {\n            return this.next;\n        },\n        set: function (next) {\n            this.next = next;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * Clone this node, and optionally its children.\n     *\n     * @param recursive Clone child nodes as well.\n     * @returns A clone of the node.\n     */\n    Node.prototype.cloneNode = function (recursive) {\n        if (recursive === void 0) { recursive = false; }\n        return cloneNode(this, recursive);\n    };\n    return Node;\n}());\nexports.Node = Node;\n/**\n * A node that contains some data.\n */\nvar DataNode = /** @class */ (function (_super) {\n    __extends(DataNode, _super);\n    /**\n     * @param data The content of the data node\n     */\n    function DataNode(data) {\n        var _this = _super.call(this) || this;\n        _this.data = data;\n        return _this;\n    }\n    Object.defineProperty(DataNode.prototype, \"nodeValue\", {\n        /**\n         * Same as {@link data}.\n         * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n         */\n        get: function () {\n            return this.data;\n        },\n        set: function (data) {\n            this.data = data;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return DataNode;\n}(Node));\nexports.DataNode = DataNode;\n/**\n * Text within the document.\n */\nvar Text = /** @class */ (function (_super) {\n    __extends(Text, _super);\n    function Text() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.type = domelementtype_1.ElementType.Text;\n        return _this;\n    }\n    Object.defineProperty(Text.prototype, \"nodeType\", {\n        get: function () {\n            return 3;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return Text;\n}(DataNode));\nexports.Text = Text;\n/**\n * Comments within the document.\n */\nvar Comment = /** @class */ (function (_super) {\n    __extends(Comment, _super);\n    function Comment() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.type = domelementtype_1.ElementType.Comment;\n        return _this;\n    }\n    Object.defineProperty(Comment.prototype, \"nodeType\", {\n        get: function () {\n            return 8;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return Comment;\n}(DataNode));\nexports.Comment = Comment;\n/**\n * Processing instructions, including doc types.\n */\nvar ProcessingInstruction = /** @class */ (function (_super) {\n    __extends(ProcessingInstruction, _super);\n    function ProcessingInstruction(name, data) {\n        var _this = _super.call(this, data) || this;\n        _this.name = name;\n        _this.type = domelementtype_1.ElementType.Directive;\n        return _this;\n    }\n    Object.defineProperty(ProcessingInstruction.prototype, \"nodeType\", {\n        get: function () {\n            return 1;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return ProcessingInstruction;\n}(DataNode));\nexports.ProcessingInstruction = ProcessingInstruction;\n/**\n * A `Node` that can have children.\n */\nvar NodeWithChildren = /** @class */ (function (_super) {\n    __extends(NodeWithChildren, _super);\n    /**\n     * @param children Children of the node. Only certain node types can have children.\n     */\n    function NodeWithChildren(children) {\n        var _this = _super.call(this) || this;\n        _this.children = children;\n        return _this;\n    }\n    Object.defineProperty(NodeWithChildren.prototype, \"firstChild\", {\n        // Aliases\n        /** First child of the node. */\n        get: function () {\n            var _a;\n            return (_a = this.children[0]) !== null && _a !== void 0 ? _a : null;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(NodeWithChildren.prototype, \"lastChild\", {\n        /** Last child of the node. */\n        get: function () {\n            return this.children.length > 0\n                ? this.children[this.children.length - 1]\n                : null;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(NodeWithChildren.prototype, \"childNodes\", {\n        /**\n         * Same as {@link children}.\n         * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n         */\n        get: function () {\n            return this.children;\n        },\n        set: function (children) {\n            this.children = children;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return NodeWithChildren;\n}(Node));\nexports.NodeWithChildren = NodeWithChildren;\nvar CDATA = /** @class */ (function (_super) {\n    __extends(CDATA, _super);\n    function CDATA() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.type = domelementtype_1.ElementType.CDATA;\n        return _this;\n    }\n    Object.defineProperty(CDATA.prototype, \"nodeType\", {\n        get: function () {\n            return 4;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return CDATA;\n}(NodeWithChildren));\nexports.CDATA = CDATA;\n/**\n * The root node of the document.\n */\nvar Document = /** @class */ (function (_super) {\n    __extends(Document, _super);\n    function Document() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.type = domelementtype_1.ElementType.Root;\n        return _this;\n    }\n    Object.defineProperty(Document.prototype, \"nodeType\", {\n        get: function () {\n            return 9;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return Document;\n}(NodeWithChildren));\nexports.Document = Document;\n/**\n * An element within the DOM.\n */\nvar Element = /** @class */ (function (_super) {\n    __extends(Element, _super);\n    /**\n     * @param name Name of the tag, eg. `div`, `span`.\n     * @param attribs Object mapping attribute names to attribute values.\n     * @param children Children of the node.\n     */\n    function Element(name, attribs, children, type) {\n        if (children === void 0) { children = []; }\n        if (type === void 0) { type = name === \"script\"\n            ? domelementtype_1.ElementType.Script\n            : name === \"style\"\n                ? domelementtype_1.ElementType.Style\n                : domelementtype_1.ElementType.Tag; }\n        var _this = _super.call(this, children) || this;\n        _this.name = name;\n        _this.attribs = attribs;\n        _this.type = type;\n        return _this;\n    }\n    Object.defineProperty(Element.prototype, \"nodeType\", {\n        get: function () {\n            return 1;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Element.prototype, \"tagName\", {\n        // DOM Level 1 aliases\n        /**\n         * Same as {@link name}.\n         * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n         */\n        get: function () {\n            return this.name;\n        },\n        set: function (name) {\n            this.name = name;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Element.prototype, \"attributes\", {\n        get: function () {\n            var _this = this;\n            return Object.keys(this.attribs).map(function (name) {\n                var _a, _b;\n                return ({\n                    name: name,\n                    value: _this.attribs[name],\n                    namespace: (_a = _this[\"x-attribsNamespace\"]) === null || _a === void 0 ? void 0 : _a[name],\n                    prefix: (_b = _this[\"x-attribsPrefix\"]) === null || _b === void 0 ? void 0 : _b[name],\n                });\n            });\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return Element;\n}(NodeWithChildren));\nexports.Element = Element;\n/**\n * @param node Node to check.\n * @returns `true` if the node is a `Element`, `false` otherwise.\n */\nfunction isTag(node) {\n    return (0, domelementtype_1.isTag)(node);\n}\nexports.isTag = isTag;\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `CDATA`, `false` otherwise.\n */\nfunction isCDATA(node) {\n    return node.type === domelementtype_1.ElementType.CDATA;\n}\nexports.isCDATA = isCDATA;\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `Text`, `false` otherwise.\n */\nfunction isText(node) {\n    return node.type === domelementtype_1.ElementType.Text;\n}\nexports.isText = isText;\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `Comment`, `false` otherwise.\n */\nfunction isComment(node) {\n    return node.type === domelementtype_1.ElementType.Comment;\n}\nexports.isComment = isComment;\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `ProcessingInstruction`, `false` otherwise.\n */\nfunction isDirective(node) {\n    return node.type === domelementtype_1.ElementType.Directive;\n}\nexports.isDirective = isDirective;\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `ProcessingInstruction`, `false` otherwise.\n */\nfunction isDocument(node) {\n    return node.type === domelementtype_1.ElementType.Root;\n}\nexports.isDocument = isDocument;\n/**\n * @param node Node to check.\n * @returns `true` if the node has children, `false` otherwise.\n */\nfunction hasChildren(node) {\n    return Object.prototype.hasOwnProperty.call(node, \"children\");\n}\nexports.hasChildren = hasChildren;\n/**\n * Clone a node, and optionally its children.\n *\n * @param recursive Clone child nodes as well.\n * @returns A clone of the node.\n */\nfunction cloneNode(node, recursive) {\n    if (recursive === void 0) { recursive = false; }\n    var result;\n    if (isText(node)) {\n        result = new Text(node.data);\n    }\n    else if (isComment(node)) {\n        result = new Comment(node.data);\n    }\n    else if (isTag(node)) {\n        var children = recursive ? cloneChildren(node.children) : [];\n        var clone_1 = new Element(node.name, __assign({}, node.attribs), children);\n        children.forEach(function (child) { return (child.parent = clone_1); });\n        if (node.namespace != null) {\n            clone_1.namespace = node.namespace;\n        }\n        if (node[\"x-attribsNamespace\"]) {\n            clone_1[\"x-attribsNamespace\"] = __assign({}, node[\"x-attribsNamespace\"]);\n        }\n        if (node[\"x-attribsPrefix\"]) {\n            clone_1[\"x-attribsPrefix\"] = __assign({}, node[\"x-attribsPrefix\"]);\n        }\n        result = clone_1;\n    }\n    else if (isCDATA(node)) {\n        var children = recursive ? cloneChildren(node.children) : [];\n        var clone_2 = new CDATA(children);\n        children.forEach(function (child) { return (child.parent = clone_2); });\n        result = clone_2;\n    }\n    else if (isDocument(node)) {\n        var children = recursive ? cloneChildren(node.children) : [];\n        var clone_3 = new Document(children);\n        children.forEach(function (child) { return (child.parent = clone_3); });\n        if (node[\"x-mode\"]) {\n            clone_3[\"x-mode\"] = node[\"x-mode\"];\n        }\n        result = clone_3;\n    }\n    else if (isDirective(node)) {\n        var instruction = new ProcessingInstruction(node.name, node.data);\n        if (node[\"x-name\"] != null) {\n            instruction[\"x-name\"] = node[\"x-name\"];\n            instruction[\"x-publicId\"] = node[\"x-publicId\"];\n            instruction[\"x-systemId\"] = node[\"x-systemId\"];\n        }\n        result = instruction;\n    }\n    else {\n        throw new Error(\"Not implemented yet: \".concat(node.type));\n    }\n    result.startIndex = node.startIndex;\n    result.endIndex = node.endIndex;\n    if (node.sourceCodeLocation != null) {\n        result.sourceCodeLocation = node.sourceCodeLocation;\n    }\n    return result;\n}\nexports.cloneNode = cloneNode;\nfunction cloneChildren(childs) {\n    var children = childs.map(function (child) { return cloneNode(child, true); });\n    for (var i = 1; i < children.length; i++) {\n        children[i].prev = children[i - 1];\n        children[i - 1].next = children[i];\n    }\n    return children;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domhandler/lib/node.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/domhandler/lib/index.js":
/*!**********************************************!*\
  !*** ./node_modules/domhandler/lib/index.js ***!
  \**********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DomHandler = void 0;\nvar domelementtype_1 = __webpack_require__(/*! domelementtype */ \"(ssr)/./node_modules/domelementtype/lib/index.js\");\nvar node_js_1 = __webpack_require__(/*! ./node.js */ \"(ssr)/./node_modules/domhandler/lib/node.js\");\n__exportStar(__webpack_require__(/*! ./node.js */ \"(ssr)/./node_modules/domhandler/lib/node.js\"), exports);\n// Default options\nvar defaultOpts = {\n    withStartIndices: false,\n    withEndIndices: false,\n    xmlMode: false,\n};\nvar DomHandler = /** @class */ (function () {\n    /**\n     * @param callback Called once parsing has completed.\n     * @param options Settings for the handler.\n     * @param elementCB Callback whenever a tag is closed.\n     */\n    function DomHandler(callback, options, elementCB) {\n        /** The elements of the DOM */\n        this.dom = [];\n        /** The root element for the DOM */\n        this.root = new node_js_1.Document(this.dom);\n        /** Indicated whether parsing has been completed. */\n        this.done = false;\n        /** Stack of open tags. */\n        this.tagStack = [this.root];\n        /** A data node that is still being written to. */\n        this.lastNode = null;\n        /** Reference to the parser instance. Used for location information. */\n        this.parser = null;\n        // Make it possible to skip arguments, for backwards-compatibility\n        if (typeof options === \"function\") {\n            elementCB = options;\n            options = defaultOpts;\n        }\n        if (typeof callback === \"object\") {\n            options = callback;\n            callback = undefined;\n        }\n        this.callback = callback !== null && callback !== void 0 ? callback : null;\n        this.options = options !== null && options !== void 0 ? options : defaultOpts;\n        this.elementCB = elementCB !== null && elementCB !== void 0 ? elementCB : null;\n    }\n    DomHandler.prototype.onparserinit = function (parser) {\n        this.parser = parser;\n    };\n    // Resets the handler back to starting state\n    DomHandler.prototype.onreset = function () {\n        this.dom = [];\n        this.root = new node_js_1.Document(this.dom);\n        this.done = false;\n        this.tagStack = [this.root];\n        this.lastNode = null;\n        this.parser = null;\n    };\n    // Signals the handler that parsing is done\n    DomHandler.prototype.onend = function () {\n        if (this.done)\n            return;\n        this.done = true;\n        this.parser = null;\n        this.handleCallback(null);\n    };\n    DomHandler.prototype.onerror = function (error) {\n        this.handleCallback(error);\n    };\n    DomHandler.prototype.onclosetag = function () {\n        this.lastNode = null;\n        var elem = this.tagStack.pop();\n        if (this.options.withEndIndices) {\n            elem.endIndex = this.parser.endIndex;\n        }\n        if (this.elementCB)\n            this.elementCB(elem);\n    };\n    DomHandler.prototype.onopentag = function (name, attribs) {\n        var type = this.options.xmlMode ? domelementtype_1.ElementType.Tag : undefined;\n        var element = new node_js_1.Element(name, attribs, undefined, type);\n        this.addNode(element);\n        this.tagStack.push(element);\n    };\n    DomHandler.prototype.ontext = function (data) {\n        var lastNode = this.lastNode;\n        if (lastNode && lastNode.type === domelementtype_1.ElementType.Text) {\n            lastNode.data += data;\n            if (this.options.withEndIndices) {\n                lastNode.endIndex = this.parser.endIndex;\n            }\n        }\n        else {\n            var node = new node_js_1.Text(data);\n            this.addNode(node);\n            this.lastNode = node;\n        }\n    };\n    DomHandler.prototype.oncomment = function (data) {\n        if (this.lastNode && this.lastNode.type === domelementtype_1.ElementType.Comment) {\n            this.lastNode.data += data;\n            return;\n        }\n        var node = new node_js_1.Comment(data);\n        this.addNode(node);\n        this.lastNode = node;\n    };\n    DomHandler.prototype.oncommentend = function () {\n        this.lastNode = null;\n    };\n    DomHandler.prototype.oncdatastart = function () {\n        var text = new node_js_1.Text(\"\");\n        var node = new node_js_1.CDATA([text]);\n        this.addNode(node);\n        text.parent = node;\n        this.lastNode = text;\n    };\n    DomHandler.prototype.oncdataend = function () {\n        this.lastNode = null;\n    };\n    DomHandler.prototype.onprocessinginstruction = function (name, data) {\n        var node = new node_js_1.ProcessingInstruction(name, data);\n        this.addNode(node);\n    };\n    DomHandler.prototype.handleCallback = function (error) {\n        if (typeof this.callback === \"function\") {\n            this.callback(error, this.dom);\n        }\n        else if (error) {\n            throw error;\n        }\n    };\n    DomHandler.prototype.addNode = function (node) {\n        var parent = this.tagStack[this.tagStack.length - 1];\n        var previousSibling = parent.children[parent.children.length - 1];\n        if (this.options.withStartIndices) {\n            node.startIndex = this.parser.startIndex;\n        }\n        if (this.options.withEndIndices) {\n            node.endIndex = this.parser.endIndex;\n        }\n        parent.children.push(node);\n        if (previousSibling) {\n            node.prev = previousSibling;\n            previousSibling.next = node;\n        }\n        node.parent = parent;\n        this.lastNode = null;\n    };\n    return DomHandler;\n}());\nexports.DomHandler = DomHandler;\nexports[\"default\"] = DomHandler;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/domhandler/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/domhandler/lib/node.js":
/*!*********************************************!*\
  !*** ./node_modules/domhandler/lib/node.js ***!
  \*********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.cloneNode = exports.hasChildren = exports.isDocument = exports.isDirective = exports.isComment = exports.isText = exports.isCDATA = exports.isTag = exports.Element = exports.Document = exports.CDATA = exports.NodeWithChildren = exports.ProcessingInstruction = exports.Comment = exports.Text = exports.DataNode = exports.Node = void 0;\nvar domelementtype_1 = __webpack_require__(/*! domelementtype */ \"(ssr)/./node_modules/domelementtype/lib/index.js\");\n/**\n * This object will be used as the prototype for Nodes when creating a\n * DOM-Level-1-compliant structure.\n */\nvar Node = /** @class */ (function () {\n    function Node() {\n        /** Parent of the node */\n        this.parent = null;\n        /** Previous sibling */\n        this.prev = null;\n        /** Next sibling */\n        this.next = null;\n        /** The start index of the node. Requires `withStartIndices` on the handler to be `true. */\n        this.startIndex = null;\n        /** The end index of the node. Requires `withEndIndices` on the handler to be `true. */\n        this.endIndex = null;\n    }\n    Object.defineProperty(Node.prototype, \"parentNode\", {\n        // Read-write aliases for properties\n        /**\n         * Same as {@link parent}.\n         * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n         */\n        get: function () {\n            return this.parent;\n        },\n        set: function (parent) {\n            this.parent = parent;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Node.prototype, \"previousSibling\", {\n        /**\n         * Same as {@link prev}.\n         * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n         */\n        get: function () {\n            return this.prev;\n        },\n        set: function (prev) {\n            this.prev = prev;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Node.prototype, \"nextSibling\", {\n        /**\n         * Same as {@link next}.\n         * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n         */\n        get: function () {\n            return this.next;\n        },\n        set: function (next) {\n            this.next = next;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * Clone this node, and optionally its children.\n     *\n     * @param recursive Clone child nodes as well.\n     * @returns A clone of the node.\n     */\n    Node.prototype.cloneNode = function (recursive) {\n        if (recursive === void 0) { recursive = false; }\n        return cloneNode(this, recursive);\n    };\n    return Node;\n}());\nexports.Node = Node;\n/**\n * A node that contains some data.\n */\nvar DataNode = /** @class */ (function (_super) {\n    __extends(DataNode, _super);\n    /**\n     * @param data The content of the data node\n     */\n    function DataNode(data) {\n        var _this = _super.call(this) || this;\n        _this.data = data;\n        return _this;\n    }\n    Object.defineProperty(DataNode.prototype, \"nodeValue\", {\n        /**\n         * Same as {@link data}.\n         * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n         */\n        get: function () {\n            return this.data;\n        },\n        set: function (data) {\n            this.data = data;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return DataNode;\n}(Node));\nexports.DataNode = DataNode;\n/**\n * Text within the document.\n */\nvar Text = /** @class */ (function (_super) {\n    __extends(Text, _super);\n    function Text() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.type = domelementtype_1.ElementType.Text;\n        return _this;\n    }\n    Object.defineProperty(Text.prototype, \"nodeType\", {\n        get: function () {\n            return 3;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return Text;\n}(DataNode));\nexports.Text = Text;\n/**\n * Comments within the document.\n */\nvar Comment = /** @class */ (function (_super) {\n    __extends(Comment, _super);\n    function Comment() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.type = domelementtype_1.ElementType.Comment;\n        return _this;\n    }\n    Object.defineProperty(Comment.prototype, \"nodeType\", {\n        get: function () {\n            return 8;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return Comment;\n}(DataNode));\nexports.Comment = Comment;\n/**\n * Processing instructions, including doc types.\n */\nvar ProcessingInstruction = /** @class */ (function (_super) {\n    __extends(ProcessingInstruction, _super);\n    function ProcessingInstruction(name, data) {\n        var _this = _super.call(this, data) || this;\n        _this.name = name;\n        _this.type = domelementtype_1.ElementType.Directive;\n        return _this;\n    }\n    Object.defineProperty(ProcessingInstruction.prototype, \"nodeType\", {\n        get: function () {\n            return 1;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return ProcessingInstruction;\n}(DataNode));\nexports.ProcessingInstruction = ProcessingInstruction;\n/**\n * A `Node` that can have children.\n */\nvar NodeWithChildren = /** @class */ (function (_super) {\n    __extends(NodeWithChildren, _super);\n    /**\n     * @param children Children of the node. Only certain node types can have children.\n     */\n    function NodeWithChildren(children) {\n        var _this = _super.call(this) || this;\n        _this.children = children;\n        return _this;\n    }\n    Object.defineProperty(NodeWithChildren.prototype, \"firstChild\", {\n        // Aliases\n        /** First child of the node. */\n        get: function () {\n            var _a;\n            return (_a = this.children[0]) !== null && _a !== void 0 ? _a : null;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(NodeWithChildren.prototype, \"lastChild\", {\n        /** Last child of the node. */\n        get: function () {\n            return this.children.length > 0\n                ? this.children[this.children.length - 1]\n                : null;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(NodeWithChildren.prototype, \"childNodes\", {\n        /**\n         * Same as {@link children}.\n         * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n         */\n        get: function () {\n            return this.children;\n        },\n        set: function (children) {\n            this.children = children;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return NodeWithChildren;\n}(Node));\nexports.NodeWithChildren = NodeWithChildren;\nvar CDATA = /** @class */ (function (_super) {\n    __extends(CDATA, _super);\n    function CDATA() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.type = domelementtype_1.ElementType.CDATA;\n        return _this;\n    }\n    Object.defineProperty(CDATA.prototype, \"nodeType\", {\n        get: function () {\n            return 4;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return CDATA;\n}(NodeWithChildren));\nexports.CDATA = CDATA;\n/**\n * The root node of the document.\n */\nvar Document = /** @class */ (function (_super) {\n    __extends(Document, _super);\n    function Document() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.type = domelementtype_1.ElementType.Root;\n        return _this;\n    }\n    Object.defineProperty(Document.prototype, \"nodeType\", {\n        get: function () {\n            return 9;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return Document;\n}(NodeWithChildren));\nexports.Document = Document;\n/**\n * An element within the DOM.\n */\nvar Element = /** @class */ (function (_super) {\n    __extends(Element, _super);\n    /**\n     * @param name Name of the tag, eg. `div`, `span`.\n     * @param attribs Object mapping attribute names to attribute values.\n     * @param children Children of the node.\n     */\n    function Element(name, attribs, children, type) {\n        if (children === void 0) { children = []; }\n        if (type === void 0) { type = name === \"script\"\n            ? domelementtype_1.ElementType.Script\n            : name === \"style\"\n                ? domelementtype_1.ElementType.Style\n                : domelementtype_1.ElementType.Tag; }\n        var _this = _super.call(this, children) || this;\n        _this.name = name;\n        _this.attribs = attribs;\n        _this.type = type;\n        return _this;\n    }\n    Object.defineProperty(Element.prototype, \"nodeType\", {\n        get: function () {\n            return 1;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Element.prototype, \"tagName\", {\n        // DOM Level 1 aliases\n        /**\n         * Same as {@link name}.\n         * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n         */\n        get: function () {\n            return this.name;\n        },\n        set: function (name) {\n            this.name = name;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Element.prototype, \"attributes\", {\n        get: function () {\n            var _this = this;\n            return Object.keys(this.attribs).map(function (name) {\n                var _a, _b;\n                return ({\n                    name: name,\n                    value: _this.attribs[name],\n                    namespace: (_a = _this[\"x-attribsNamespace\"]) === null || _a === void 0 ? void 0 : _a[name],\n                    prefix: (_b = _this[\"x-attribsPrefix\"]) === null || _b === void 0 ? void 0 : _b[name],\n                });\n            });\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return Element;\n}(NodeWithChildren));\nexports.Element = Element;\n/**\n * @param node Node to check.\n * @returns `true` if the node is a `Element`, `false` otherwise.\n */\nfunction isTag(node) {\n    return (0, domelementtype_1.isTag)(node);\n}\nexports.isTag = isTag;\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `CDATA`, `false` otherwise.\n */\nfunction isCDATA(node) {\n    return node.type === domelementtype_1.ElementType.CDATA;\n}\nexports.isCDATA = isCDATA;\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `Text`, `false` otherwise.\n */\nfunction isText(node) {\n    return node.type === domelementtype_1.ElementType.Text;\n}\nexports.isText = isText;\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `Comment`, `false` otherwise.\n */\nfunction isComment(node) {\n    return node.type === domelementtype_1.ElementType.Comment;\n}\nexports.isComment = isComment;\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `ProcessingInstruction`, `false` otherwise.\n */\nfunction isDirective(node) {\n    return node.type === domelementtype_1.ElementType.Directive;\n}\nexports.isDirective = isDirective;\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `ProcessingInstruction`, `false` otherwise.\n */\nfunction isDocument(node) {\n    return node.type === domelementtype_1.ElementType.Root;\n}\nexports.isDocument = isDocument;\n/**\n * @param node Node to check.\n * @returns `true` if the node has children, `false` otherwise.\n */\nfunction hasChildren(node) {\n    return Object.prototype.hasOwnProperty.call(node, \"children\");\n}\nexports.hasChildren = hasChildren;\n/**\n * Clone a node, and optionally its children.\n *\n * @param recursive Clone child nodes as well.\n * @returns A clone of the node.\n */\nfunction cloneNode(node, recursive) {\n    if (recursive === void 0) { recursive = false; }\n    var result;\n    if (isText(node)) {\n        result = new Text(node.data);\n    }\n    else if (isComment(node)) {\n        result = new Comment(node.data);\n    }\n    else if (isTag(node)) {\n        var children = recursive ? cloneChildren(node.children) : [];\n        var clone_1 = new Element(node.name, __assign({}, node.attribs), children);\n        children.forEach(function (child) { return (child.parent = clone_1); });\n        if (node.namespace != null) {\n            clone_1.namespace = node.namespace;\n        }\n        if (node[\"x-attribsNamespace\"]) {\n            clone_1[\"x-attribsNamespace\"] = __assign({}, node[\"x-attribsNamespace\"]);\n        }\n        if (node[\"x-attribsPrefix\"]) {\n            clone_1[\"x-attribsPrefix\"] = __assign({}, node[\"x-attribsPrefix\"]);\n        }\n        result = clone_1;\n    }\n    else if (isCDATA(node)) {\n        var children = recursive ? cloneChildren(node.children) : [];\n        var clone_2 = new CDATA(children);\n        children.forEach(function (child) { return (child.parent = clone_2); });\n        result = clone_2;\n    }\n    else if (isDocument(node)) {\n        var children = recursive ? cloneChildren(node.children) : [];\n        var clone_3 = new Document(children);\n        children.forEach(function (child) { return (child.parent = clone_3); });\n        if (node[\"x-mode\"]) {\n            clone_3[\"x-mode\"] = node[\"x-mode\"];\n        }\n        result = clone_3;\n    }\n    else if (isDirective(node)) {\n        var instruction = new ProcessingInstruction(node.name, node.data);\n        if (node[\"x-name\"] != null) {\n            instruction[\"x-name\"] = node[\"x-name\"];\n            instruction[\"x-publicId\"] = node[\"x-publicId\"];\n            instruction[\"x-systemId\"] = node[\"x-systemId\"];\n        }\n        result = instruction;\n    }\n    else {\n        throw new Error(\"Not implemented yet: \".concat(node.type));\n    }\n    result.startIndex = node.startIndex;\n    result.endIndex = node.endIndex;\n    if (node.sourceCodeLocation != null) {\n        result.sourceCodeLocation = node.sourceCodeLocation;\n    }\n    return result;\n}\nexports.cloneNode = cloneNode;\nfunction cloneChildren(childs) {\n    var children = childs.map(function (child) { return cloneNode(child, true); });\n    for (var i = 1; i < children.length; i++) {\n        children[i].prev = children[i - 1];\n        children[i - 1].next = children[i];\n    }\n    return children;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/domhandler/lib/node.js\n");

/***/ })

};
;