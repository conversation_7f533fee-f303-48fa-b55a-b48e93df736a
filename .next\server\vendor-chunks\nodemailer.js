"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/nodemailer";
exports.ids = ["vendor-chunks/nodemailer"];
exports.modules = {

/***/ "(rsc)/./node_modules/nodemailer/lib/smtp-connection/http-proxy-client.js":
/*!**************************************************************************!*\
  !*** ./node_modules/nodemailer/lib/smtp-connection/http-proxy-client.js ***!
  \**************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n/**\n * Minimal HTTP/S proxy client\n */\n\nconst net = __webpack_require__(/*! net */ \"net\");\nconst tls = __webpack_require__(/*! tls */ \"tls\");\nconst urllib = __webpack_require__(/*! url */ \"url\");\n\n/**\n * Establishes proxied connection to destinationPort\n *\n * httpProxyClient(\"http://localhost:3128/\", 80, \"google.com\", function(err, socket){\n *     socket.write(\"GET / HTTP/1.0\\r\\n\\r\\n\");\n * });\n *\n * @param {String} proxyUrl proxy configuration, etg \"http://proxy.host:3128/\"\n * @param {Number} destinationPort Port to open in destination host\n * @param {String} destinationHost Destination hostname\n * @param {Function} callback Callback to run with the rocket object once connection is established\n */\nfunction httpProxyClient(proxyUrl, destinationPort, destinationHost, callback) {\n    let proxy = urllib.parse(proxyUrl);\n\n    // create a socket connection to the proxy server\n    let options;\n    let connect;\n    let socket;\n\n    options = {\n        host: proxy.hostname,\n        port: Number(proxy.port) ? Number(proxy.port) : proxy.protocol === 'https:' ? 443 : 80\n    };\n\n    if (proxy.protocol === 'https:') {\n        // we can use untrusted proxies as long as we verify actual SMTP certificates\n        options.rejectUnauthorized = false;\n        connect = tls.connect.bind(tls);\n    } else {\n        connect = net.connect.bind(net);\n    }\n\n    // Error harness for initial connection. Once connection is established, the responsibility\n    // to handle errors is passed to whoever uses this socket\n    let finished = false;\n    let tempSocketErr = err => {\n        if (finished) {\n            return;\n        }\n        finished = true;\n        try {\n            socket.destroy();\n        } catch (E) {\n            // ignore\n        }\n        callback(err);\n    };\n\n    let timeoutErr = () => {\n        let err = new Error('Proxy socket timed out');\n        err.code = 'ETIMEDOUT';\n        tempSocketErr(err);\n    };\n\n    socket = connect(options, () => {\n        if (finished) {\n            return;\n        }\n\n        let reqHeaders = {\n            Host: destinationHost + ':' + destinationPort,\n            Connection: 'close'\n        };\n        if (proxy.auth) {\n            reqHeaders['Proxy-Authorization'] = 'Basic ' + Buffer.from(proxy.auth).toString('base64');\n        }\n\n        socket.write(\n            // HTTP method\n            'CONNECT ' +\n                destinationHost +\n                ':' +\n                destinationPort +\n                ' HTTP/1.1\\r\\n' +\n                // HTTP request headers\n                Object.keys(reqHeaders)\n                    .map(key => key + ': ' + reqHeaders[key])\n                    .join('\\r\\n') +\n                // End request\n                '\\r\\n\\r\\n'\n        );\n\n        let headers = '';\n        let onSocketData = chunk => {\n            let match;\n            let remainder;\n\n            if (finished) {\n                return;\n            }\n\n            headers += chunk.toString('binary');\n            if ((match = headers.match(/\\r\\n\\r\\n/))) {\n                socket.removeListener('data', onSocketData);\n\n                remainder = headers.substr(match.index + match[0].length);\n                headers = headers.substr(0, match.index);\n                if (remainder) {\n                    socket.unshift(Buffer.from(remainder, 'binary'));\n                }\n\n                // proxy connection is now established\n                finished = true;\n\n                // check response code\n                match = headers.match(/^HTTP\\/\\d+\\.\\d+ (\\d+)/i);\n                if (!match || (match[1] || '').charAt(0) !== '2') {\n                    try {\n                        socket.destroy();\n                    } catch (E) {\n                        // ignore\n                    }\n                    return callback(new Error('Invalid response from proxy' + ((match && ': ' + match[1]) || '')));\n                }\n\n                socket.removeListener('error', tempSocketErr);\n                socket.removeListener('timeout', timeoutErr);\n                socket.setTimeout(0);\n\n                return callback(null, socket);\n            }\n        };\n        socket.on('data', onSocketData);\n    });\n\n    socket.setTimeout(httpProxyClient.timeout || 30 * 1000);\n    socket.on('timeout', timeoutErr);\n\n    socket.once('error', tempSocketErr);\n}\n\nmodule.exports = httpProxyClient;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/nodemailer/lib/smtp-connection/http-proxy-client.js\n");

/***/ })

};
;