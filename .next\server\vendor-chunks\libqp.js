"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/libqp";
exports.ids = ["vendor-chunks/libqp"];
exports.modules = {

/***/ "(rsc)/./node_modules/libqp/lib/libqp.js":
/*!*****************************************!*\
  !*** ./node_modules/libqp/lib/libqp.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/* eslint no-useless-escape: 0 */\n\n\n\nconst { Buffer } = __webpack_require__(/*! node:buffer */ \"node:buffer\");\nconst stream = __webpack_require__(/*! node:stream */ \"node:stream\");\nconst Transform = stream.Transform;\n\n/**\n * Encodes a Buffer into a Quoted-Printable encoded string\n *\n * @param {Buffer} buffer Buffer to convert\n * @returns {String} Quoted-Printable encoded string\n */\nfunction encode(buffer) {\n    if (typeof buffer === 'string') {\n        buffer = Buffer.from(buffer, 'utf-8');\n    }\n\n    // usable characters that do not need encoding\n    let ranges = [\n        // https://tools.ietf.org/html/rfc2045#section-6.7\n        [0x09], // <TAB>\n        [0x0a], // <LF>\n        [0x0d], // <CR>\n        [0x20, 0x3c], // <SP>!\"#$%&'()*+,-./0123456789:;\n        [0x3e, 0x7e] // >?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\\]^_`abcdefghijklmnopqrstuvwxyz{|}\n    ];\n    let result = '';\n    let ord;\n\n    for (let i = 0, len = buffer.length; i < len; i++) {\n        ord = buffer[i];\n        // if the char is in allowed range, then keep as is, unless it is a ws in the end of a line\n        if (checkRanges(ord, ranges) && !((ord === 0x20 || ord === 0x09) && (i === len - 1 || buffer[i + 1] === 0x0a || buffer[i + 1] === 0x0d))) {\n            result += String.fromCharCode(ord);\n            continue;\n        }\n        result += '=' + (ord < 0x10 ? '0' : '') + ord.toString(16).toUpperCase();\n    }\n\n    return result;\n}\n\n/**\n * Decodes a Quoted-Printable encoded string to a Buffer object\n *\n * @param {String} str Quoted-Printable encoded string\n * @returns {Buffer} Decoded value\n */\nfunction decode(str) {\n    str = (str || '')\n        .toString()\n        // remove invalid whitespace from the end of lines\n        .replace(/[\\t ]+$/gm, '')\n        // remove soft line breaks\n        .replace(/\\=(?:\\r?\\n|$)/g, '');\n\n    let encodedBytesCount = (str.match(/\\=[\\da-fA-F]{2}/g) || []).length,\n        bufferLength = str.length - encodedBytesCount * 2,\n        chr,\n        hex,\n        buffer = Buffer.alloc(bufferLength),\n        bufferPos = 0;\n\n    for (let i = 0, len = str.length; i < len; i++) {\n        chr = str.charAt(i);\n        if (chr === '=' && (hex = str.substr(i + 1, 2)) && /[\\da-fA-F]{2}/.test(hex)) {\n            buffer[bufferPos++] = parseInt(hex, 16);\n            i += 2;\n            continue;\n        }\n        buffer[bufferPos++] = chr.charCodeAt(0);\n    }\n\n    return buffer;\n}\n\n/**\n * Adds soft line breaks to a Quoted-Printable string\n *\n * @param {String} str Quoted-Printable encoded string that might need line wrapping\n * @param {Number} [lineLength=76] Maximum allowed length for a line\n * @returns {String} Soft-wrapped Quoted-Printable encoded string\n */\nfunction wrap(str, lineLength) {\n    str = (str || '').toString();\n    lineLength = lineLength || 76;\n\n    if (str.length <= lineLength) {\n        return str;\n    }\n\n    let pos = 0,\n        len = str.length,\n        match,\n        code,\n        line,\n        lineMargin = Math.floor(lineLength / 3),\n        result = '';\n\n    // insert soft linebreaks where needed\n    while (pos < len) {\n        line = str.substr(pos, lineLength);\n        if ((match = line.match(/\\r\\n/))) {\n            line = line.substr(0, match.index + match[0].length);\n            result += line;\n            pos += line.length;\n            continue;\n        }\n\n        if (line.substr(-1) === '\\n') {\n            // nothing to change here\n            result += line;\n            pos += line.length;\n            continue;\n        } else if ((match = line.substr(-lineMargin).match(/\\n.*?$/))) {\n            // truncate to nearest line break\n            line = line.substr(0, line.length - (match[0].length - 1));\n            result += line;\n            pos += line.length;\n            continue;\n        } else if (line.length > lineLength - lineMargin && (match = line.substr(-lineMargin).match(/[ \\t\\.,!\\?][^ \\t\\.,!\\?]*$/))) {\n            // truncate to nearest space\n            line = line.substr(0, line.length - (match[0].length - 1));\n        } else if (line.match(/\\=[\\da-f]{0,2}$/i)) {\n            // push incomplete encoding sequences to the next line\n            if ((match = line.match(/\\=[\\da-f]{0,1}$/i))) {\n                line = line.substr(0, line.length - match[0].length);\n            }\n\n            // ensure that utf-8 sequences are not split\n            while (line.length > 3 && line.length < len - pos && !line.match(/^(?:=[\\da-f]{2}){1,4}$/i) && (match = line.match(/\\=[\\da-f]{2}$/gi))) {\n                code = parseInt(match[0].substr(1, 2), 16);\n                if (code < 128) {\n                    break;\n                }\n\n                line = line.substr(0, line.length - 3);\n\n                if (code >= 0xc0) {\n                    break;\n                }\n            }\n        }\n\n        if (pos + line.length < len && line.substr(-1) !== '\\n') {\n            if (line.length === lineLength && line.match(/\\=[\\da-f]{2}$/i)) {\n                line = line.substr(0, line.length - 3);\n            } else if (line.length === lineLength) {\n                line = line.substr(0, line.length - 1);\n            }\n            pos += line.length;\n            line += '=\\r\\n';\n        } else {\n            pos += line.length;\n        }\n\n        result += line;\n    }\n\n    return result;\n}\n\n/**\n * Helper function to check if a number is inside provided ranges\n *\n * @param {Number} nr Number to check for\n * @param {Array} ranges An Array of allowed values\n * @returns {Boolean} True if the value was found inside allowed ranges, false otherwise\n */\nfunction checkRanges(nr, ranges) {\n    for (let i = ranges.length - 1; i >= 0; i--) {\n        if (!ranges[i].length) {\n            continue;\n        }\n        if (ranges[i].length === 1 && nr === ranges[i][0]) {\n            return true;\n        }\n        if (ranges[i].length === 2 && nr >= ranges[i][0] && nr <= ranges[i][1]) {\n            return true;\n        }\n    }\n    return false;\n}\n\n/**\n * Creates a transform stream for encoding data to Quoted-Printable encoding\n *\n * @constructor\n * @param {Object} options Stream options\n * @param {Number} [options.lineLength=76] Maximum lenght for lines, set to false to disable wrapping\n */\nclass Encoder extends Transform {\n    constructor(options) {\n        super();\n\n        // init Transform\n        this.options = options || {};\n\n        if (this.options.lineLength !== false) {\n            this.options.lineLength = this.options.lineLength || 76;\n        }\n\n        this._curLine = '';\n\n        this.inputBytes = 0;\n        this.outputBytes = 0;\n\n        Transform.call(this, this.options);\n    }\n\n    _transform(chunk, encoding, done) {\n        let qp;\n\n        if (encoding !== 'buffer') {\n            chunk = Buffer.from(chunk, encoding);\n        }\n\n        if (!chunk || !chunk.length) {\n            return done();\n        }\n\n        this.inputBytes += chunk.length;\n\n        if (this.options.lineLength) {\n            qp = this._curLine + encode(chunk);\n            qp = wrap(qp, this.options.lineLength);\n            qp = qp.replace(/(^|\\n)([^\\n]*)$/, (match, lineBreak, lastLine) => {\n                this._curLine = lastLine;\n                return lineBreak;\n            });\n\n            if (qp) {\n                this.outputBytes += qp.length;\n                this.push(qp);\n            }\n        } else {\n            qp = encode(chunk);\n            this.outputBytes += qp.length;\n            this.push(qp, 'ascii');\n        }\n\n        done();\n    }\n\n    _flush(done) {\n        if (this._curLine) {\n            this.outputBytes += this._curLine.length;\n            this.push(this._curLine, 'ascii');\n        }\n        done();\n    }\n}\n\n/**\n * Creates a transform stream for decoding Quoted-Printable encoded strings\n * The input is not actually processed as a stream but concatted and processed as a single input\n *\n * @constructor\n * @param {Object} options Stream options\n */\nclass Decoder extends Transform {\n    constructor(options) {\n        options = options || {};\n        super(options);\n\n        // init Transform\n        this.options = options;\n        this._curLine = '';\n\n        this.inputBytes = 0;\n        this.outputBytes = 0;\n\n        this.qpChunks = [];\n    }\n\n    _transform(chunk, encoding, done) {\n        if (!chunk || !chunk.length) {\n            return done();\n        }\n\n        if (typeof chunk === 'string') {\n            chunk = Buffer.from(chunk, encoding);\n        }\n\n        this.qpChunks.push(chunk);\n        this.inputBytes += chunk.length;\n\n        done();\n    }\n\n    _flush(done) {\n        if (this.inputBytes) {\n            let buf = decode(Buffer.concat(this.qpChunks, this.inputBytes).toString());\n            this.outputBytes += buf.length;\n            this.push(buf);\n        }\n\n        done();\n    }\n}\n\n// expose to the world\nmodule.exports = {\n    encode,\n    decode,\n    wrap,\n    Encoder,\n    Decoder\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/libqp/lib/libqp.js\n");

/***/ })

};
;