"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/socks";
exports.ids = ["vendor-chunks/socks"];
exports.modules = {

/***/ "(rsc)/./node_modules/socks/build/client/socksclient.js":
/*!********************************************************!*\
  !*** ./node_modules/socks/build/client/socksclient.js ***!
  \********************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SocksClientError = exports.SocksClient = void 0;\nconst events_1 = __webpack_require__(/*! events */ \"events\");\nconst net = __webpack_require__(/*! net */ \"net\");\nconst smart_buffer_1 = __webpack_require__(/*! smart-buffer */ \"(rsc)/./node_modules/smart-buffer/build/smartbuffer.js\");\nconst constants_1 = __webpack_require__(/*! ../common/constants */ \"(rsc)/./node_modules/socks/build/common/constants.js\");\nconst helpers_1 = __webpack_require__(/*! ../common/helpers */ \"(rsc)/./node_modules/socks/build/common/helpers.js\");\nconst receivebuffer_1 = __webpack_require__(/*! ../common/receivebuffer */ \"(rsc)/./node_modules/socks/build/common/receivebuffer.js\");\nconst util_1 = __webpack_require__(/*! ../common/util */ \"(rsc)/./node_modules/socks/build/common/util.js\");\nObject.defineProperty(exports, \"SocksClientError\", ({ enumerable: true, get: function () { return util_1.SocksClientError; } }));\nconst ip_address_1 = __webpack_require__(/*! ip-address */ \"(rsc)/./node_modules/ip-address/dist/ip-address.js\");\nclass SocksClient extends events_1.EventEmitter {\n    constructor(options) {\n        super();\n        this.options = Object.assign({}, options);\n        // Validate SocksClientOptions\n        (0, helpers_1.validateSocksClientOptions)(options);\n        // Default state\n        this.setState(constants_1.SocksClientState.Created);\n    }\n    /**\n     * Creates a new SOCKS connection.\n     *\n     * Note: Supports callbacks and promises. Only supports the connect command.\n     * @param options { SocksClientOptions } Options.\n     * @param callback { Function } An optional callback function.\n     * @returns { Promise }\n     */\n    static createConnection(options, callback) {\n        return new Promise((resolve, reject) => {\n            // Validate SocksClientOptions\n            try {\n                (0, helpers_1.validateSocksClientOptions)(options, ['connect']);\n            }\n            catch (err) {\n                if (typeof callback === 'function') {\n                    callback(err);\n                    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                    return resolve(err); // Resolves pending promise (prevents memory leaks).\n                }\n                else {\n                    return reject(err);\n                }\n            }\n            const client = new SocksClient(options);\n            client.connect(options.existing_socket);\n            client.once('established', (info) => {\n                client.removeAllListeners();\n                if (typeof callback === 'function') {\n                    callback(null, info);\n                    resolve(info); // Resolves pending promise (prevents memory leaks).\n                }\n                else {\n                    resolve(info);\n                }\n            });\n            // Error occurred, failed to establish connection.\n            client.once('error', (err) => {\n                client.removeAllListeners();\n                if (typeof callback === 'function') {\n                    callback(err);\n                    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                    resolve(err); // Resolves pending promise (prevents memory leaks).\n                }\n                else {\n                    reject(err);\n                }\n            });\n        });\n    }\n    /**\n     * Creates a new SOCKS connection chain to a destination host through 2 or more SOCKS proxies.\n     *\n     * Note: Supports callbacks and promises. Only supports the connect method.\n     * Note: Implemented via createConnection() factory function.\n     * @param options { SocksClientChainOptions } Options\n     * @param callback { Function } An optional callback function.\n     * @returns { Promise }\n     */\n    static createConnectionChain(options, callback) {\n        // eslint-disable-next-line no-async-promise-executor\n        return new Promise((resolve, reject) => __awaiter(this, void 0, void 0, function* () {\n            // Validate SocksClientChainOptions\n            try {\n                (0, helpers_1.validateSocksClientChainOptions)(options);\n            }\n            catch (err) {\n                if (typeof callback === 'function') {\n                    callback(err);\n                    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                    return resolve(err); // Resolves pending promise (prevents memory leaks).\n                }\n                else {\n                    return reject(err);\n                }\n            }\n            // Shuffle proxies\n            if (options.randomizeChain) {\n                (0, util_1.shuffleArray)(options.proxies);\n            }\n            try {\n                let sock;\n                for (let i = 0; i < options.proxies.length; i++) {\n                    const nextProxy = options.proxies[i];\n                    // If we've reached the last proxy in the chain, the destination is the actual destination, otherwise it's the next proxy.\n                    const nextDestination = i === options.proxies.length - 1\n                        ? options.destination\n                        : {\n                            host: options.proxies[i + 1].host ||\n                                options.proxies[i + 1].ipaddress,\n                            port: options.proxies[i + 1].port,\n                        };\n                    // Creates the next connection in the chain.\n                    const result = yield SocksClient.createConnection({\n                        command: 'connect',\n                        proxy: nextProxy,\n                        destination: nextDestination,\n                        existing_socket: sock,\n                    });\n                    // If sock is undefined, assign it here.\n                    sock = sock || result.socket;\n                }\n                if (typeof callback === 'function') {\n                    callback(null, { socket: sock });\n                    resolve({ socket: sock }); // Resolves pending promise (prevents memory leaks).\n                }\n                else {\n                    resolve({ socket: sock });\n                }\n            }\n            catch (err) {\n                if (typeof callback === 'function') {\n                    callback(err);\n                    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                    resolve(err); // Resolves pending promise (prevents memory leaks).\n                }\n                else {\n                    reject(err);\n                }\n            }\n        }));\n    }\n    /**\n     * Creates a SOCKS UDP Frame.\n     * @param options\n     */\n    static createUDPFrame(options) {\n        const buff = new smart_buffer_1.SmartBuffer();\n        buff.writeUInt16BE(0);\n        buff.writeUInt8(options.frameNumber || 0);\n        // IPv4/IPv6/Hostname\n        if (net.isIPv4(options.remoteHost.host)) {\n            buff.writeUInt8(constants_1.Socks5HostType.IPv4);\n            buff.writeUInt32BE((0, helpers_1.ipv4ToInt32)(options.remoteHost.host));\n        }\n        else if (net.isIPv6(options.remoteHost.host)) {\n            buff.writeUInt8(constants_1.Socks5HostType.IPv6);\n            buff.writeBuffer((0, helpers_1.ipToBuffer)(options.remoteHost.host));\n        }\n        else {\n            buff.writeUInt8(constants_1.Socks5HostType.Hostname);\n            buff.writeUInt8(Buffer.byteLength(options.remoteHost.host));\n            buff.writeString(options.remoteHost.host);\n        }\n        // Port\n        buff.writeUInt16BE(options.remoteHost.port);\n        // Data\n        buff.writeBuffer(options.data);\n        return buff.toBuffer();\n    }\n    /**\n     * Parses a SOCKS UDP frame.\n     * @param data\n     */\n    static parseUDPFrame(data) {\n        const buff = smart_buffer_1.SmartBuffer.fromBuffer(data);\n        buff.readOffset = 2;\n        const frameNumber = buff.readUInt8();\n        const hostType = buff.readUInt8();\n        let remoteHost;\n        if (hostType === constants_1.Socks5HostType.IPv4) {\n            remoteHost = (0, helpers_1.int32ToIpv4)(buff.readUInt32BE());\n        }\n        else if (hostType === constants_1.Socks5HostType.IPv6) {\n            remoteHost = ip_address_1.Address6.fromByteArray(Array.from(buff.readBuffer(16))).canonicalForm();\n        }\n        else {\n            remoteHost = buff.readString(buff.readUInt8());\n        }\n        const remotePort = buff.readUInt16BE();\n        return {\n            frameNumber,\n            remoteHost: {\n                host: remoteHost,\n                port: remotePort,\n            },\n            data: buff.readBuffer(),\n        };\n    }\n    /**\n     * Internal state setter. If the SocksClient is in an error state, it cannot be changed to a non error state.\n     */\n    setState(newState) {\n        if (this.state !== constants_1.SocksClientState.Error) {\n            this.state = newState;\n        }\n    }\n    /**\n     * Starts the connection establishment to the proxy and destination.\n     * @param existingSocket Connected socket to use instead of creating a new one (internal use).\n     */\n    connect(existingSocket) {\n        this.onDataReceived = (data) => this.onDataReceivedHandler(data);\n        this.onClose = () => this.onCloseHandler();\n        this.onError = (err) => this.onErrorHandler(err);\n        this.onConnect = () => this.onConnectHandler();\n        // Start timeout timer (defaults to 30 seconds)\n        const timer = setTimeout(() => this.onEstablishedTimeout(), this.options.timeout || constants_1.DEFAULT_TIMEOUT);\n        // check whether unref is available as it differs from browser to NodeJS (#33)\n        if (timer.unref && typeof timer.unref === 'function') {\n            timer.unref();\n        }\n        // If an existing socket is provided, use it to negotiate SOCKS handshake. Otherwise create a new Socket.\n        if (existingSocket) {\n            this.socket = existingSocket;\n        }\n        else {\n            this.socket = new net.Socket();\n        }\n        // Attach Socket error handlers.\n        this.socket.once('close', this.onClose);\n        this.socket.once('error', this.onError);\n        this.socket.once('connect', this.onConnect);\n        this.socket.on('data', this.onDataReceived);\n        this.setState(constants_1.SocksClientState.Connecting);\n        this.receiveBuffer = new receivebuffer_1.ReceiveBuffer();\n        if (existingSocket) {\n            this.socket.emit('connect');\n        }\n        else {\n            this.socket.connect(this.getSocketOptions());\n            if (this.options.set_tcp_nodelay !== undefined &&\n                this.options.set_tcp_nodelay !== null) {\n                this.socket.setNoDelay(!!this.options.set_tcp_nodelay);\n            }\n        }\n        // Listen for established event so we can re-emit any excess data received during handshakes.\n        this.prependOnceListener('established', (info) => {\n            setImmediate(() => {\n                if (this.receiveBuffer.length > 0) {\n                    const excessData = this.receiveBuffer.get(this.receiveBuffer.length);\n                    info.socket.emit('data', excessData);\n                }\n                info.socket.resume();\n            });\n        });\n    }\n    // Socket options (defaults host/port to options.proxy.host/options.proxy.port)\n    getSocketOptions() {\n        return Object.assign(Object.assign({}, this.options.socket_options), { host: this.options.proxy.host || this.options.proxy.ipaddress, port: this.options.proxy.port });\n    }\n    /**\n     * Handles internal Socks timeout callback.\n     * Note: If the Socks client is not BoundWaitingForConnection or Established, the connection will be closed.\n     */\n    onEstablishedTimeout() {\n        if (this.state !== constants_1.SocksClientState.Established &&\n            this.state !== constants_1.SocksClientState.BoundWaitingForConnection) {\n            this.closeSocket(constants_1.ERRORS.ProxyConnectionTimedOut);\n        }\n    }\n    /**\n     * Handles Socket connect event.\n     */\n    onConnectHandler() {\n        this.setState(constants_1.SocksClientState.Connected);\n        // Send initial handshake.\n        if (this.options.proxy.type === 4) {\n            this.sendSocks4InitialHandshake();\n        }\n        else {\n            this.sendSocks5InitialHandshake();\n        }\n        this.setState(constants_1.SocksClientState.SentInitialHandshake);\n    }\n    /**\n     * Handles Socket data event.\n     * @param data\n     */\n    onDataReceivedHandler(data) {\n        /*\n          All received data is appended to a ReceiveBuffer.\n          This makes sure that all the data we need is received before we attempt to process it.\n        */\n        this.receiveBuffer.append(data);\n        // Process data that we have.\n        this.processData();\n    }\n    /**\n     * Handles processing of the data we have received.\n     */\n    processData() {\n        // If we have enough data to process the next step in the SOCKS handshake, proceed.\n        while (this.state !== constants_1.SocksClientState.Established &&\n            this.state !== constants_1.SocksClientState.Error &&\n            this.receiveBuffer.length >= this.nextRequiredPacketBufferSize) {\n            // Sent initial handshake, waiting for response.\n            if (this.state === constants_1.SocksClientState.SentInitialHandshake) {\n                if (this.options.proxy.type === 4) {\n                    // Socks v4 only has one handshake response.\n                    this.handleSocks4FinalHandshakeResponse();\n                }\n                else {\n                    // Socks v5 has two handshakes, handle initial one here.\n                    this.handleInitialSocks5HandshakeResponse();\n                }\n                // Sent auth request for Socks v5, waiting for response.\n            }\n            else if (this.state === constants_1.SocksClientState.SentAuthentication) {\n                this.handleInitialSocks5AuthenticationHandshakeResponse();\n                // Sent final Socks v5 handshake, waiting for final response.\n            }\n            else if (this.state === constants_1.SocksClientState.SentFinalHandshake) {\n                this.handleSocks5FinalHandshakeResponse();\n                // Socks BIND established. Waiting for remote connection via proxy.\n            }\n            else if (this.state === constants_1.SocksClientState.BoundWaitingForConnection) {\n                if (this.options.proxy.type === 4) {\n                    this.handleSocks4IncomingConnectionResponse();\n                }\n                else {\n                    this.handleSocks5IncomingConnectionResponse();\n                }\n            }\n            else {\n                this.closeSocket(constants_1.ERRORS.InternalError);\n                break;\n            }\n        }\n    }\n    /**\n     * Handles Socket close event.\n     * @param had_error\n     */\n    onCloseHandler() {\n        this.closeSocket(constants_1.ERRORS.SocketClosed);\n    }\n    /**\n     * Handles Socket error event.\n     * @param err\n     */\n    onErrorHandler(err) {\n        this.closeSocket(err.message);\n    }\n    /**\n     * Removes internal event listeners on the underlying Socket.\n     */\n    removeInternalSocketHandlers() {\n        // Pauses data flow of the socket (this is internally resumed after 'established' is emitted)\n        this.socket.pause();\n        this.socket.removeListener('data', this.onDataReceived);\n        this.socket.removeListener('close', this.onClose);\n        this.socket.removeListener('error', this.onError);\n        this.socket.removeListener('connect', this.onConnect);\n    }\n    /**\n     * Closes and destroys the underlying Socket. Emits an error event.\n     * @param err { String } An error string to include in error event.\n     */\n    closeSocket(err) {\n        // Make sure only one 'error' event is fired for the lifetime of this SocksClient instance.\n        if (this.state !== constants_1.SocksClientState.Error) {\n            // Set internal state to Error.\n            this.setState(constants_1.SocksClientState.Error);\n            // Destroy Socket\n            this.socket.destroy();\n            // Remove internal listeners\n            this.removeInternalSocketHandlers();\n            // Fire 'error' event.\n            this.emit('error', new util_1.SocksClientError(err, this.options));\n        }\n    }\n    /**\n     * Sends initial Socks v4 handshake request.\n     */\n    sendSocks4InitialHandshake() {\n        const userId = this.options.proxy.userId || '';\n        const buff = new smart_buffer_1.SmartBuffer();\n        buff.writeUInt8(0x04);\n        buff.writeUInt8(constants_1.SocksCommand[this.options.command]);\n        buff.writeUInt16BE(this.options.destination.port);\n        // Socks 4 (IPv4)\n        if (net.isIPv4(this.options.destination.host)) {\n            buff.writeBuffer((0, helpers_1.ipToBuffer)(this.options.destination.host));\n            buff.writeStringNT(userId);\n            // Socks 4a (hostname)\n        }\n        else {\n            buff.writeUInt8(0x00);\n            buff.writeUInt8(0x00);\n            buff.writeUInt8(0x00);\n            buff.writeUInt8(0x01);\n            buff.writeStringNT(userId);\n            buff.writeStringNT(this.options.destination.host);\n        }\n        this.nextRequiredPacketBufferSize =\n            constants_1.SOCKS_INCOMING_PACKET_SIZES.Socks4Response;\n        this.socket.write(buff.toBuffer());\n    }\n    /**\n     * Handles Socks v4 handshake response.\n     * @param data\n     */\n    handleSocks4FinalHandshakeResponse() {\n        const data = this.receiveBuffer.get(8);\n        if (data[1] !== constants_1.Socks4Response.Granted) {\n            this.closeSocket(`${constants_1.ERRORS.Socks4ProxyRejectedConnection} - (${constants_1.Socks4Response[data[1]]})`);\n        }\n        else {\n            // Bind response\n            if (constants_1.SocksCommand[this.options.command] === constants_1.SocksCommand.bind) {\n                const buff = smart_buffer_1.SmartBuffer.fromBuffer(data);\n                buff.readOffset = 2;\n                const remoteHost = {\n                    port: buff.readUInt16BE(),\n                    host: (0, helpers_1.int32ToIpv4)(buff.readUInt32BE()),\n                };\n                // If host is 0.0.0.0, set to proxy host.\n                if (remoteHost.host === '0.0.0.0') {\n                    remoteHost.host = this.options.proxy.ipaddress;\n                }\n                this.setState(constants_1.SocksClientState.BoundWaitingForConnection);\n                this.emit('bound', { remoteHost, socket: this.socket });\n                // Connect response\n            }\n            else {\n                this.setState(constants_1.SocksClientState.Established);\n                this.removeInternalSocketHandlers();\n                this.emit('established', { socket: this.socket });\n            }\n        }\n    }\n    /**\n     * Handles Socks v4 incoming connection request (BIND)\n     * @param data\n     */\n    handleSocks4IncomingConnectionResponse() {\n        const data = this.receiveBuffer.get(8);\n        if (data[1] !== constants_1.Socks4Response.Granted) {\n            this.closeSocket(`${constants_1.ERRORS.Socks4ProxyRejectedIncomingBoundConnection} - (${constants_1.Socks4Response[data[1]]})`);\n        }\n        else {\n            const buff = smart_buffer_1.SmartBuffer.fromBuffer(data);\n            buff.readOffset = 2;\n            const remoteHost = {\n                port: buff.readUInt16BE(),\n                host: (0, helpers_1.int32ToIpv4)(buff.readUInt32BE()),\n            };\n            this.setState(constants_1.SocksClientState.Established);\n            this.removeInternalSocketHandlers();\n            this.emit('established', { remoteHost, socket: this.socket });\n        }\n    }\n    /**\n     * Sends initial Socks v5 handshake request.\n     */\n    sendSocks5InitialHandshake() {\n        const buff = new smart_buffer_1.SmartBuffer();\n        // By default we always support no auth.\n        const supportedAuthMethods = [constants_1.Socks5Auth.NoAuth];\n        // We should only tell the proxy we support user/pass auth if auth info is actually provided.\n        // Note: As of Tor v0.3.5.7+, if user/pass auth is an option from the client, by default it will always take priority.\n        if (this.options.proxy.userId || this.options.proxy.password) {\n            supportedAuthMethods.push(constants_1.Socks5Auth.UserPass);\n        }\n        // Custom auth method?\n        if (this.options.proxy.custom_auth_method !== undefined) {\n            supportedAuthMethods.push(this.options.proxy.custom_auth_method);\n        }\n        // Build handshake packet\n        buff.writeUInt8(0x05);\n        buff.writeUInt8(supportedAuthMethods.length);\n        for (const authMethod of supportedAuthMethods) {\n            buff.writeUInt8(authMethod);\n        }\n        this.nextRequiredPacketBufferSize =\n            constants_1.SOCKS_INCOMING_PACKET_SIZES.Socks5InitialHandshakeResponse;\n        this.socket.write(buff.toBuffer());\n        this.setState(constants_1.SocksClientState.SentInitialHandshake);\n    }\n    /**\n     * Handles initial Socks v5 handshake response.\n     * @param data\n     */\n    handleInitialSocks5HandshakeResponse() {\n        const data = this.receiveBuffer.get(2);\n        if (data[0] !== 0x05) {\n            this.closeSocket(constants_1.ERRORS.InvalidSocks5IntiailHandshakeSocksVersion);\n        }\n        else if (data[1] === constants_1.SOCKS5_NO_ACCEPTABLE_AUTH) {\n            this.closeSocket(constants_1.ERRORS.InvalidSocks5InitialHandshakeNoAcceptedAuthType);\n        }\n        else {\n            // If selected Socks v5 auth method is no auth, send final handshake request.\n            if (data[1] === constants_1.Socks5Auth.NoAuth) {\n                this.socks5ChosenAuthType = constants_1.Socks5Auth.NoAuth;\n                this.sendSocks5CommandRequest();\n                // If selected Socks v5 auth method is user/password, send auth handshake.\n            }\n            else if (data[1] === constants_1.Socks5Auth.UserPass) {\n                this.socks5ChosenAuthType = constants_1.Socks5Auth.UserPass;\n                this.sendSocks5UserPassAuthentication();\n                // If selected Socks v5 auth method is the custom_auth_method, send custom handshake.\n            }\n            else if (data[1] === this.options.proxy.custom_auth_method) {\n                this.socks5ChosenAuthType = this.options.proxy.custom_auth_method;\n                this.sendSocks5CustomAuthentication();\n            }\n            else {\n                this.closeSocket(constants_1.ERRORS.InvalidSocks5InitialHandshakeUnknownAuthType);\n            }\n        }\n    }\n    /**\n     * Sends Socks v5 user & password auth handshake.\n     *\n     * Note: No auth and user/pass are currently supported.\n     */\n    sendSocks5UserPassAuthentication() {\n        const userId = this.options.proxy.userId || '';\n        const password = this.options.proxy.password || '';\n        const buff = new smart_buffer_1.SmartBuffer();\n        buff.writeUInt8(0x01);\n        buff.writeUInt8(Buffer.byteLength(userId));\n        buff.writeString(userId);\n        buff.writeUInt8(Buffer.byteLength(password));\n        buff.writeString(password);\n        this.nextRequiredPacketBufferSize =\n            constants_1.SOCKS_INCOMING_PACKET_SIZES.Socks5UserPassAuthenticationResponse;\n        this.socket.write(buff.toBuffer());\n        this.setState(constants_1.SocksClientState.SentAuthentication);\n    }\n    sendSocks5CustomAuthentication() {\n        return __awaiter(this, void 0, void 0, function* () {\n            this.nextRequiredPacketBufferSize =\n                this.options.proxy.custom_auth_response_size;\n            this.socket.write(yield this.options.proxy.custom_auth_request_handler());\n            this.setState(constants_1.SocksClientState.SentAuthentication);\n        });\n    }\n    handleSocks5CustomAuthHandshakeResponse(data) {\n        return __awaiter(this, void 0, void 0, function* () {\n            return yield this.options.proxy.custom_auth_response_handler(data);\n        });\n    }\n    handleSocks5AuthenticationNoAuthHandshakeResponse(data) {\n        return __awaiter(this, void 0, void 0, function* () {\n            return data[1] === 0x00;\n        });\n    }\n    handleSocks5AuthenticationUserPassHandshakeResponse(data) {\n        return __awaiter(this, void 0, void 0, function* () {\n            return data[1] === 0x00;\n        });\n    }\n    /**\n     * Handles Socks v5 auth handshake response.\n     * @param data\n     */\n    handleInitialSocks5AuthenticationHandshakeResponse() {\n        return __awaiter(this, void 0, void 0, function* () {\n            this.setState(constants_1.SocksClientState.ReceivedAuthenticationResponse);\n            let authResult = false;\n            if (this.socks5ChosenAuthType === constants_1.Socks5Auth.NoAuth) {\n                authResult = yield this.handleSocks5AuthenticationNoAuthHandshakeResponse(this.receiveBuffer.get(2));\n            }\n            else if (this.socks5ChosenAuthType === constants_1.Socks5Auth.UserPass) {\n                authResult =\n                    yield this.handleSocks5AuthenticationUserPassHandshakeResponse(this.receiveBuffer.get(2));\n            }\n            else if (this.socks5ChosenAuthType === this.options.proxy.custom_auth_method) {\n                authResult = yield this.handleSocks5CustomAuthHandshakeResponse(this.receiveBuffer.get(this.options.proxy.custom_auth_response_size));\n            }\n            if (!authResult) {\n                this.closeSocket(constants_1.ERRORS.Socks5AuthenticationFailed);\n            }\n            else {\n                this.sendSocks5CommandRequest();\n            }\n        });\n    }\n    /**\n     * Sends Socks v5 final handshake request.\n     */\n    sendSocks5CommandRequest() {\n        const buff = new smart_buffer_1.SmartBuffer();\n        buff.writeUInt8(0x05);\n        buff.writeUInt8(constants_1.SocksCommand[this.options.command]);\n        buff.writeUInt8(0x00);\n        // ipv4, ipv6, domain?\n        if (net.isIPv4(this.options.destination.host)) {\n            buff.writeUInt8(constants_1.Socks5HostType.IPv4);\n            buff.writeBuffer((0, helpers_1.ipToBuffer)(this.options.destination.host));\n        }\n        else if (net.isIPv6(this.options.destination.host)) {\n            buff.writeUInt8(constants_1.Socks5HostType.IPv6);\n            buff.writeBuffer((0, helpers_1.ipToBuffer)(this.options.destination.host));\n        }\n        else {\n            buff.writeUInt8(constants_1.Socks5HostType.Hostname);\n            buff.writeUInt8(this.options.destination.host.length);\n            buff.writeString(this.options.destination.host);\n        }\n        buff.writeUInt16BE(this.options.destination.port);\n        this.nextRequiredPacketBufferSize =\n            constants_1.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseHeader;\n        this.socket.write(buff.toBuffer());\n        this.setState(constants_1.SocksClientState.SentFinalHandshake);\n    }\n    /**\n     * Handles Socks v5 final handshake response.\n     * @param data\n     */\n    handleSocks5FinalHandshakeResponse() {\n        // Peek at available data (we need at least 5 bytes to get the hostname length)\n        const header = this.receiveBuffer.peek(5);\n        if (header[0] !== 0x05 || header[1] !== constants_1.Socks5Response.Granted) {\n            this.closeSocket(`${constants_1.ERRORS.InvalidSocks5FinalHandshakeRejected} - ${constants_1.Socks5Response[header[1]]}`);\n        }\n        else {\n            // Read address type\n            const addressType = header[3];\n            let remoteHost;\n            let buff;\n            // IPv4\n            if (addressType === constants_1.Socks5HostType.IPv4) {\n                // Check if data is available.\n                const dataNeeded = constants_1.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseIPv4;\n                if (this.receiveBuffer.length < dataNeeded) {\n                    this.nextRequiredPacketBufferSize = dataNeeded;\n                    return;\n                }\n                buff = smart_buffer_1.SmartBuffer.fromBuffer(this.receiveBuffer.get(dataNeeded).slice(4));\n                remoteHost = {\n                    host: (0, helpers_1.int32ToIpv4)(buff.readUInt32BE()),\n                    port: buff.readUInt16BE(),\n                };\n                // If given host is 0.0.0.0, assume remote proxy ip instead.\n                if (remoteHost.host === '0.0.0.0') {\n                    remoteHost.host = this.options.proxy.ipaddress;\n                }\n                // Hostname\n            }\n            else if (addressType === constants_1.Socks5HostType.Hostname) {\n                const hostLength = header[4];\n                const dataNeeded = constants_1.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseHostname(hostLength); // header + host length + host + port\n                // Check if data is available.\n                if (this.receiveBuffer.length < dataNeeded) {\n                    this.nextRequiredPacketBufferSize = dataNeeded;\n                    return;\n                }\n                buff = smart_buffer_1.SmartBuffer.fromBuffer(this.receiveBuffer.get(dataNeeded).slice(5));\n                remoteHost = {\n                    host: buff.readString(hostLength),\n                    port: buff.readUInt16BE(),\n                };\n                // IPv6\n            }\n            else if (addressType === constants_1.Socks5HostType.IPv6) {\n                // Check if data is available.\n                const dataNeeded = constants_1.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseIPv6;\n                if (this.receiveBuffer.length < dataNeeded) {\n                    this.nextRequiredPacketBufferSize = dataNeeded;\n                    return;\n                }\n                buff = smart_buffer_1.SmartBuffer.fromBuffer(this.receiveBuffer.get(dataNeeded).slice(4));\n                remoteHost = {\n                    host: ip_address_1.Address6.fromByteArray(Array.from(buff.readBuffer(16))).canonicalForm(),\n                    port: buff.readUInt16BE(),\n                };\n            }\n            // We have everything we need\n            this.setState(constants_1.SocksClientState.ReceivedFinalResponse);\n            // If using CONNECT, the client is now in the established state.\n            if (constants_1.SocksCommand[this.options.command] === constants_1.SocksCommand.connect) {\n                this.setState(constants_1.SocksClientState.Established);\n                this.removeInternalSocketHandlers();\n                this.emit('established', { remoteHost, socket: this.socket });\n            }\n            else if (constants_1.SocksCommand[this.options.command] === constants_1.SocksCommand.bind) {\n                /* If using BIND, the Socks client is now in BoundWaitingForConnection state.\n                   This means that the remote proxy server is waiting for a remote connection to the bound port. */\n                this.setState(constants_1.SocksClientState.BoundWaitingForConnection);\n                this.nextRequiredPacketBufferSize =\n                    constants_1.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseHeader;\n                this.emit('bound', { remoteHost, socket: this.socket });\n                /*\n                  If using Associate, the Socks client is now Established. And the proxy server is now accepting UDP packets at the\n                  given bound port. This initial Socks TCP connection must remain open for the UDP relay to continue to work.\n                */\n            }\n            else if (constants_1.SocksCommand[this.options.command] === constants_1.SocksCommand.associate) {\n                this.setState(constants_1.SocksClientState.Established);\n                this.removeInternalSocketHandlers();\n                this.emit('established', {\n                    remoteHost,\n                    socket: this.socket,\n                });\n            }\n        }\n    }\n    /**\n     * Handles Socks v5 incoming connection request (BIND).\n     */\n    handleSocks5IncomingConnectionResponse() {\n        // Peek at available data (we need at least 5 bytes to get the hostname length)\n        const header = this.receiveBuffer.peek(5);\n        if (header[0] !== 0x05 || header[1] !== constants_1.Socks5Response.Granted) {\n            this.closeSocket(`${constants_1.ERRORS.Socks5ProxyRejectedIncomingBoundConnection} - ${constants_1.Socks5Response[header[1]]}`);\n        }\n        else {\n            // Read address type\n            const addressType = header[3];\n            let remoteHost;\n            let buff;\n            // IPv4\n            if (addressType === constants_1.Socks5HostType.IPv4) {\n                // Check if data is available.\n                const dataNeeded = constants_1.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseIPv4;\n                if (this.receiveBuffer.length < dataNeeded) {\n                    this.nextRequiredPacketBufferSize = dataNeeded;\n                    return;\n                }\n                buff = smart_buffer_1.SmartBuffer.fromBuffer(this.receiveBuffer.get(dataNeeded).slice(4));\n                remoteHost = {\n                    host: (0, helpers_1.int32ToIpv4)(buff.readUInt32BE()),\n                    port: buff.readUInt16BE(),\n                };\n                // If given host is 0.0.0.0, assume remote proxy ip instead.\n                if (remoteHost.host === '0.0.0.0') {\n                    remoteHost.host = this.options.proxy.ipaddress;\n                }\n                // Hostname\n            }\n            else if (addressType === constants_1.Socks5HostType.Hostname) {\n                const hostLength = header[4];\n                const dataNeeded = constants_1.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseHostname(hostLength); // header + host length + port\n                // Check if data is available.\n                if (this.receiveBuffer.length < dataNeeded) {\n                    this.nextRequiredPacketBufferSize = dataNeeded;\n                    return;\n                }\n                buff = smart_buffer_1.SmartBuffer.fromBuffer(this.receiveBuffer.get(dataNeeded).slice(5));\n                remoteHost = {\n                    host: buff.readString(hostLength),\n                    port: buff.readUInt16BE(),\n                };\n                // IPv6\n            }\n            else if (addressType === constants_1.Socks5HostType.IPv6) {\n                // Check if data is available.\n                const dataNeeded = constants_1.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseIPv6;\n                if (this.receiveBuffer.length < dataNeeded) {\n                    this.nextRequiredPacketBufferSize = dataNeeded;\n                    return;\n                }\n                buff = smart_buffer_1.SmartBuffer.fromBuffer(this.receiveBuffer.get(dataNeeded).slice(4));\n                remoteHost = {\n                    host: ip_address_1.Address6.fromByteArray(Array.from(buff.readBuffer(16))).canonicalForm(),\n                    port: buff.readUInt16BE(),\n                };\n            }\n            this.setState(constants_1.SocksClientState.Established);\n            this.removeInternalSocketHandlers();\n            this.emit('established', { remoteHost, socket: this.socket });\n        }\n    }\n    get socksClientOptions() {\n        return Object.assign({}, this.options);\n    }\n}\nexports.SocksClient = SocksClient;\n//# sourceMappingURL=socksclient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/socks/build/client/socksclient.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/socks/build/common/constants.js":
/*!******************************************************!*\
  !*** ./node_modules/socks/build/common/constants.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SOCKS5_NO_ACCEPTABLE_AUTH = exports.SOCKS5_CUSTOM_AUTH_END = exports.SOCKS5_CUSTOM_AUTH_START = exports.SOCKS_INCOMING_PACKET_SIZES = exports.SocksClientState = exports.Socks5Response = exports.Socks5HostType = exports.Socks5Auth = exports.Socks4Response = exports.SocksCommand = exports.ERRORS = exports.DEFAULT_TIMEOUT = void 0;\nconst DEFAULT_TIMEOUT = 30000;\nexports.DEFAULT_TIMEOUT = DEFAULT_TIMEOUT;\n// prettier-ignore\nconst ERRORS = {\n    InvalidSocksCommand: 'An invalid SOCKS command was provided. Valid options are connect, bind, and associate.',\n    InvalidSocksCommandForOperation: 'An invalid SOCKS command was provided. Only a subset of commands are supported for this operation.',\n    InvalidSocksCommandChain: 'An invalid SOCKS command was provided. Chaining currently only supports the connect command.',\n    InvalidSocksClientOptionsDestination: 'An invalid destination host was provided.',\n    InvalidSocksClientOptionsExistingSocket: 'An invalid existing socket was provided. This should be an instance of stream.Duplex.',\n    InvalidSocksClientOptionsProxy: 'Invalid SOCKS proxy details were provided.',\n    InvalidSocksClientOptionsTimeout: 'An invalid timeout value was provided. Please enter a value above 0 (in ms).',\n    InvalidSocksClientOptionsProxiesLength: 'At least two socks proxies must be provided for chaining.',\n    InvalidSocksClientOptionsCustomAuthRange: 'Custom auth must be a value between 0x80 and 0xFE.',\n    InvalidSocksClientOptionsCustomAuthOptions: 'When a custom_auth_method is provided, custom_auth_request_handler, custom_auth_response_size, and custom_auth_response_handler must also be provided and valid.',\n    NegotiationError: 'Negotiation error',\n    SocketClosed: 'Socket closed',\n    ProxyConnectionTimedOut: 'Proxy connection timed out',\n    InternalError: 'SocksClient internal error (this should not happen)',\n    InvalidSocks4HandshakeResponse: 'Received invalid Socks4 handshake response',\n    Socks4ProxyRejectedConnection: 'Socks4 Proxy rejected connection',\n    InvalidSocks4IncomingConnectionResponse: 'Socks4 invalid incoming connection response',\n    Socks4ProxyRejectedIncomingBoundConnection: 'Socks4 Proxy rejected incoming bound connection',\n    InvalidSocks5InitialHandshakeResponse: 'Received invalid Socks5 initial handshake response',\n    InvalidSocks5IntiailHandshakeSocksVersion: 'Received invalid Socks5 initial handshake (invalid socks version)',\n    InvalidSocks5InitialHandshakeNoAcceptedAuthType: 'Received invalid Socks5 initial handshake (no accepted authentication type)',\n    InvalidSocks5InitialHandshakeUnknownAuthType: 'Received invalid Socks5 initial handshake (unknown authentication type)',\n    Socks5AuthenticationFailed: 'Socks5 Authentication failed',\n    InvalidSocks5FinalHandshake: 'Received invalid Socks5 final handshake response',\n    InvalidSocks5FinalHandshakeRejected: 'Socks5 proxy rejected connection',\n    InvalidSocks5IncomingConnectionResponse: 'Received invalid Socks5 incoming connection response',\n    Socks5ProxyRejectedIncomingBoundConnection: 'Socks5 Proxy rejected incoming bound connection',\n};\nexports.ERRORS = ERRORS;\nconst SOCKS_INCOMING_PACKET_SIZES = {\n    Socks5InitialHandshakeResponse: 2,\n    Socks5UserPassAuthenticationResponse: 2,\n    // Command response + incoming connection (bind)\n    Socks5ResponseHeader: 5, // We need at least 5 to read the hostname length, then we wait for the address+port information.\n    Socks5ResponseIPv4: 10, // 4 header + 4 ip + 2 port\n    Socks5ResponseIPv6: 22, // 4 header + 16 ip + 2 port\n    Socks5ResponseHostname: (hostNameLength) => hostNameLength + 7, // 4 header + 1 host length + host + 2 port\n    // Command response + incoming connection (bind)\n    Socks4Response: 8, // 2 header + 2 port + 4 ip\n};\nexports.SOCKS_INCOMING_PACKET_SIZES = SOCKS_INCOMING_PACKET_SIZES;\nvar SocksCommand;\n(function (SocksCommand) {\n    SocksCommand[SocksCommand[\"connect\"] = 1] = \"connect\";\n    SocksCommand[SocksCommand[\"bind\"] = 2] = \"bind\";\n    SocksCommand[SocksCommand[\"associate\"] = 3] = \"associate\";\n})(SocksCommand || (exports.SocksCommand = SocksCommand = {}));\nvar Socks4Response;\n(function (Socks4Response) {\n    Socks4Response[Socks4Response[\"Granted\"] = 90] = \"Granted\";\n    Socks4Response[Socks4Response[\"Failed\"] = 91] = \"Failed\";\n    Socks4Response[Socks4Response[\"Rejected\"] = 92] = \"Rejected\";\n    Socks4Response[Socks4Response[\"RejectedIdent\"] = 93] = \"RejectedIdent\";\n})(Socks4Response || (exports.Socks4Response = Socks4Response = {}));\nvar Socks5Auth;\n(function (Socks5Auth) {\n    Socks5Auth[Socks5Auth[\"NoAuth\"] = 0] = \"NoAuth\";\n    Socks5Auth[Socks5Auth[\"GSSApi\"] = 1] = \"GSSApi\";\n    Socks5Auth[Socks5Auth[\"UserPass\"] = 2] = \"UserPass\";\n})(Socks5Auth || (exports.Socks5Auth = Socks5Auth = {}));\nconst SOCKS5_CUSTOM_AUTH_START = 0x80;\nexports.SOCKS5_CUSTOM_AUTH_START = SOCKS5_CUSTOM_AUTH_START;\nconst SOCKS5_CUSTOM_AUTH_END = 0xfe;\nexports.SOCKS5_CUSTOM_AUTH_END = SOCKS5_CUSTOM_AUTH_END;\nconst SOCKS5_NO_ACCEPTABLE_AUTH = 0xff;\nexports.SOCKS5_NO_ACCEPTABLE_AUTH = SOCKS5_NO_ACCEPTABLE_AUTH;\nvar Socks5Response;\n(function (Socks5Response) {\n    Socks5Response[Socks5Response[\"Granted\"] = 0] = \"Granted\";\n    Socks5Response[Socks5Response[\"Failure\"] = 1] = \"Failure\";\n    Socks5Response[Socks5Response[\"NotAllowed\"] = 2] = \"NotAllowed\";\n    Socks5Response[Socks5Response[\"NetworkUnreachable\"] = 3] = \"NetworkUnreachable\";\n    Socks5Response[Socks5Response[\"HostUnreachable\"] = 4] = \"HostUnreachable\";\n    Socks5Response[Socks5Response[\"ConnectionRefused\"] = 5] = \"ConnectionRefused\";\n    Socks5Response[Socks5Response[\"TTLExpired\"] = 6] = \"TTLExpired\";\n    Socks5Response[Socks5Response[\"CommandNotSupported\"] = 7] = \"CommandNotSupported\";\n    Socks5Response[Socks5Response[\"AddressNotSupported\"] = 8] = \"AddressNotSupported\";\n})(Socks5Response || (exports.Socks5Response = Socks5Response = {}));\nvar Socks5HostType;\n(function (Socks5HostType) {\n    Socks5HostType[Socks5HostType[\"IPv4\"] = 1] = \"IPv4\";\n    Socks5HostType[Socks5HostType[\"Hostname\"] = 3] = \"Hostname\";\n    Socks5HostType[Socks5HostType[\"IPv6\"] = 4] = \"IPv6\";\n})(Socks5HostType || (exports.Socks5HostType = Socks5HostType = {}));\nvar SocksClientState;\n(function (SocksClientState) {\n    SocksClientState[SocksClientState[\"Created\"] = 0] = \"Created\";\n    SocksClientState[SocksClientState[\"Connecting\"] = 1] = \"Connecting\";\n    SocksClientState[SocksClientState[\"Connected\"] = 2] = \"Connected\";\n    SocksClientState[SocksClientState[\"SentInitialHandshake\"] = 3] = \"SentInitialHandshake\";\n    SocksClientState[SocksClientState[\"ReceivedInitialHandshakeResponse\"] = 4] = \"ReceivedInitialHandshakeResponse\";\n    SocksClientState[SocksClientState[\"SentAuthentication\"] = 5] = \"SentAuthentication\";\n    SocksClientState[SocksClientState[\"ReceivedAuthenticationResponse\"] = 6] = \"ReceivedAuthenticationResponse\";\n    SocksClientState[SocksClientState[\"SentFinalHandshake\"] = 7] = \"SentFinalHandshake\";\n    SocksClientState[SocksClientState[\"ReceivedFinalResponse\"] = 8] = \"ReceivedFinalResponse\";\n    SocksClientState[SocksClientState[\"BoundWaitingForConnection\"] = 9] = \"BoundWaitingForConnection\";\n    SocksClientState[SocksClientState[\"Established\"] = 10] = \"Established\";\n    SocksClientState[SocksClientState[\"Disconnected\"] = 11] = \"Disconnected\";\n    SocksClientState[SocksClientState[\"Error\"] = 99] = \"Error\";\n})(SocksClientState || (exports.SocksClientState = SocksClientState = {}));\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/socks/build/common/constants.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/socks/build/common/helpers.js":
/*!****************************************************!*\
  !*** ./node_modules/socks/build/common/helpers.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ipToBuffer = exports.int32ToIpv4 = exports.ipv4ToInt32 = exports.validateSocksClientChainOptions = exports.validateSocksClientOptions = void 0;\nconst util_1 = __webpack_require__(/*! ./util */ \"(rsc)/./node_modules/socks/build/common/util.js\");\nconst constants_1 = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/socks/build/common/constants.js\");\nconst stream = __webpack_require__(/*! stream */ \"stream\");\nconst ip_address_1 = __webpack_require__(/*! ip-address */ \"(rsc)/./node_modules/ip-address/dist/ip-address.js\");\nconst net = __webpack_require__(/*! net */ \"net\");\n/**\n * Validates the provided SocksClientOptions\n * @param options { SocksClientOptions }\n * @param acceptedCommands { string[] } A list of accepted SocksProxy commands.\n */\nfunction validateSocksClientOptions(options, acceptedCommands = ['connect', 'bind', 'associate']) {\n    // Check SOCKs command option.\n    if (!constants_1.SocksCommand[options.command]) {\n        throw new util_1.SocksClientError(constants_1.ERRORS.InvalidSocksCommand, options);\n    }\n    // Check SocksCommand for acceptable command.\n    if (acceptedCommands.indexOf(options.command) === -1) {\n        throw new util_1.SocksClientError(constants_1.ERRORS.InvalidSocksCommandForOperation, options);\n    }\n    // Check destination\n    if (!isValidSocksRemoteHost(options.destination)) {\n        throw new util_1.SocksClientError(constants_1.ERRORS.InvalidSocksClientOptionsDestination, options);\n    }\n    // Check SOCKS proxy to use\n    if (!isValidSocksProxy(options.proxy)) {\n        throw new util_1.SocksClientError(constants_1.ERRORS.InvalidSocksClientOptionsProxy, options);\n    }\n    // Validate custom auth (if set)\n    validateCustomProxyAuth(options.proxy, options);\n    // Check timeout\n    if (options.timeout && !isValidTimeoutValue(options.timeout)) {\n        throw new util_1.SocksClientError(constants_1.ERRORS.InvalidSocksClientOptionsTimeout, options);\n    }\n    // Check existing_socket (if provided)\n    if (options.existing_socket &&\n        !(options.existing_socket instanceof stream.Duplex)) {\n        throw new util_1.SocksClientError(constants_1.ERRORS.InvalidSocksClientOptionsExistingSocket, options);\n    }\n}\nexports.validateSocksClientOptions = validateSocksClientOptions;\n/**\n * Validates the SocksClientChainOptions\n * @param options { SocksClientChainOptions }\n */\nfunction validateSocksClientChainOptions(options) {\n    // Only connect is supported when chaining.\n    if (options.command !== 'connect') {\n        throw new util_1.SocksClientError(constants_1.ERRORS.InvalidSocksCommandChain, options);\n    }\n    // Check destination\n    if (!isValidSocksRemoteHost(options.destination)) {\n        throw new util_1.SocksClientError(constants_1.ERRORS.InvalidSocksClientOptionsDestination, options);\n    }\n    // Validate proxies (length)\n    if (!(options.proxies &&\n        Array.isArray(options.proxies) &&\n        options.proxies.length >= 2)) {\n        throw new util_1.SocksClientError(constants_1.ERRORS.InvalidSocksClientOptionsProxiesLength, options);\n    }\n    // Validate proxies\n    options.proxies.forEach((proxy) => {\n        if (!isValidSocksProxy(proxy)) {\n            throw new util_1.SocksClientError(constants_1.ERRORS.InvalidSocksClientOptionsProxy, options);\n        }\n        // Validate custom auth (if set)\n        validateCustomProxyAuth(proxy, options);\n    });\n    // Check timeout\n    if (options.timeout && !isValidTimeoutValue(options.timeout)) {\n        throw new util_1.SocksClientError(constants_1.ERRORS.InvalidSocksClientOptionsTimeout, options);\n    }\n}\nexports.validateSocksClientChainOptions = validateSocksClientChainOptions;\nfunction validateCustomProxyAuth(proxy, options) {\n    if (proxy.custom_auth_method !== undefined) {\n        // Invalid auth method range\n        if (proxy.custom_auth_method < constants_1.SOCKS5_CUSTOM_AUTH_START ||\n            proxy.custom_auth_method > constants_1.SOCKS5_CUSTOM_AUTH_END) {\n            throw new util_1.SocksClientError(constants_1.ERRORS.InvalidSocksClientOptionsCustomAuthRange, options);\n        }\n        // Missing custom_auth_request_handler\n        if (proxy.custom_auth_request_handler === undefined ||\n            typeof proxy.custom_auth_request_handler !== 'function') {\n            throw new util_1.SocksClientError(constants_1.ERRORS.InvalidSocksClientOptionsCustomAuthOptions, options);\n        }\n        // Missing custom_auth_response_size\n        if (proxy.custom_auth_response_size === undefined) {\n            throw new util_1.SocksClientError(constants_1.ERRORS.InvalidSocksClientOptionsCustomAuthOptions, options);\n        }\n        // Missing/invalid custom_auth_response_handler\n        if (proxy.custom_auth_response_handler === undefined ||\n            typeof proxy.custom_auth_response_handler !== 'function') {\n            throw new util_1.SocksClientError(constants_1.ERRORS.InvalidSocksClientOptionsCustomAuthOptions, options);\n        }\n    }\n}\n/**\n * Validates a SocksRemoteHost\n * @param remoteHost { SocksRemoteHost }\n */\nfunction isValidSocksRemoteHost(remoteHost) {\n    return (remoteHost &&\n        typeof remoteHost.host === 'string' &&\n        Buffer.byteLength(remoteHost.host) < 256 &&\n        typeof remoteHost.port === 'number' &&\n        remoteHost.port >= 0 &&\n        remoteHost.port <= 65535);\n}\n/**\n * Validates a SocksProxy\n * @param proxy { SocksProxy }\n */\nfunction isValidSocksProxy(proxy) {\n    return (proxy &&\n        (typeof proxy.host === 'string' || typeof proxy.ipaddress === 'string') &&\n        typeof proxy.port === 'number' &&\n        proxy.port >= 0 &&\n        proxy.port <= 65535 &&\n        (proxy.type === 4 || proxy.type === 5));\n}\n/**\n * Validates a timeout value.\n * @param value { Number }\n */\nfunction isValidTimeoutValue(value) {\n    return typeof value === 'number' && value > 0;\n}\nfunction ipv4ToInt32(ip) {\n    const address = new ip_address_1.Address4(ip);\n    // Convert the IPv4 address parts to an integer\n    return address.toArray().reduce((acc, part) => (acc << 8) + part, 0) >>> 0;\n}\nexports.ipv4ToInt32 = ipv4ToInt32;\nfunction int32ToIpv4(int32) {\n    // Extract each byte (octet) from the 32-bit integer\n    const octet1 = (int32 >>> 24) & 0xff;\n    const octet2 = (int32 >>> 16) & 0xff;\n    const octet3 = (int32 >>> 8) & 0xff;\n    const octet4 = int32 & 0xff;\n    // Combine the octets into a string in IPv4 format\n    return [octet1, octet2, octet3, octet4].join('.');\n}\nexports.int32ToIpv4 = int32ToIpv4;\nfunction ipToBuffer(ip) {\n    if (net.isIPv4(ip)) {\n        // Handle IPv4 addresses\n        const address = new ip_address_1.Address4(ip);\n        return Buffer.from(address.toArray());\n    }\n    else if (net.isIPv6(ip)) {\n        // Handle IPv6 addresses\n        const address = new ip_address_1.Address6(ip);\n        return Buffer.from(address\n            .canonicalForm()\n            .split(':')\n            .map((segment) => segment.padStart(4, '0'))\n            .join(''), 'hex');\n    }\n    else {\n        throw new Error('Invalid IP address format');\n    }\n}\nexports.ipToBuffer = ipToBuffer;\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/socks/build/common/helpers.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/socks/build/common/receivebuffer.js":
/*!**********************************************************!*\
  !*** ./node_modules/socks/build/common/receivebuffer.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ReceiveBuffer = void 0;\nclass ReceiveBuffer {\n    constructor(size = 4096) {\n        this.buffer = Buffer.allocUnsafe(size);\n        this.offset = 0;\n        this.originalSize = size;\n    }\n    get length() {\n        return this.offset;\n    }\n    append(data) {\n        if (!Buffer.isBuffer(data)) {\n            throw new Error('Attempted to append a non-buffer instance to ReceiveBuffer.');\n        }\n        if (this.offset + data.length >= this.buffer.length) {\n            const tmp = this.buffer;\n            this.buffer = Buffer.allocUnsafe(Math.max(this.buffer.length + this.originalSize, this.buffer.length + data.length));\n            tmp.copy(this.buffer);\n        }\n        data.copy(this.buffer, this.offset);\n        return (this.offset += data.length);\n    }\n    peek(length) {\n        if (length > this.offset) {\n            throw new Error('Attempted to read beyond the bounds of the managed internal data.');\n        }\n        return this.buffer.slice(0, length);\n    }\n    get(length) {\n        if (length > this.offset) {\n            throw new Error('Attempted to read beyond the bounds of the managed internal data.');\n        }\n        const value = Buffer.allocUnsafe(length);\n        this.buffer.slice(0, length).copy(value);\n        this.buffer.copyWithin(0, length, length + this.offset - length);\n        this.offset -= length;\n        return value;\n    }\n}\nexports.ReceiveBuffer = ReceiveBuffer;\n//# sourceMappingURL=receivebuffer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/socks/build/common/receivebuffer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/socks/build/common/util.js":
/*!*************************************************!*\
  !*** ./node_modules/socks/build/common/util.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.shuffleArray = exports.SocksClientError = void 0;\n/**\n * Error wrapper for SocksClient\n */\nclass SocksClientError extends Error {\n    constructor(message, options) {\n        super(message);\n        this.options = options;\n    }\n}\nexports.SocksClientError = SocksClientError;\n/**\n * Shuffles a given array.\n * @param array The array to shuffle.\n */\nfunction shuffleArray(array) {\n    for (let i = array.length - 1; i > 0; i--) {\n        const j = Math.floor(Math.random() * (i + 1));\n        [array[i], array[j]] = [array[j], array[i]];\n    }\n}\nexports.shuffleArray = shuffleArray;\n//# sourceMappingURL=util.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvc29ja3MvYnVpbGQvY29tbW9uL3V0aWwuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsb0JBQW9CLEdBQUcsd0JBQXdCO0FBQy9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QjtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUNBQW1DLE9BQU87QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0I7QUFDcEIiLCJzb3VyY2VzIjpbIkM6XFxDb2RlXFxvbmUtbWFpbFxcbm9kZV9tb2R1bGVzXFxzb2Nrc1xcYnVpbGRcXGNvbW1vblxcdXRpbC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuc2h1ZmZsZUFycmF5ID0gZXhwb3J0cy5Tb2Nrc0NsaWVudEVycm9yID0gdm9pZCAwO1xuLyoqXG4gKiBFcnJvciB3cmFwcGVyIGZvciBTb2Nrc0NsaWVudFxuICovXG5jbGFzcyBTb2Nrc0NsaWVudEVycm9yIGV4dGVuZHMgRXJyb3Ige1xuICAgIGNvbnN0cnVjdG9yKG1lc3NhZ2UsIG9wdGlvbnMpIHtcbiAgICAgICAgc3VwZXIobWVzc2FnZSk7XG4gICAgICAgIHRoaXMub3B0aW9ucyA9IG9wdGlvbnM7XG4gICAgfVxufVxuZXhwb3J0cy5Tb2Nrc0NsaWVudEVycm9yID0gU29ja3NDbGllbnRFcnJvcjtcbi8qKlxuICogU2h1ZmZsZXMgYSBnaXZlbiBhcnJheS5cbiAqIEBwYXJhbSBhcnJheSBUaGUgYXJyYXkgdG8gc2h1ZmZsZS5cbiAqL1xuZnVuY3Rpb24gc2h1ZmZsZUFycmF5KGFycmF5KSB7XG4gICAgZm9yIChsZXQgaSA9IGFycmF5Lmxlbmd0aCAtIDE7IGkgPiAwOyBpLS0pIHtcbiAgICAgICAgY29uc3QgaiA9IE1hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIChpICsgMSkpO1xuICAgICAgICBbYXJyYXlbaV0sIGFycmF5W2pdXSA9IFthcnJheVtqXSwgYXJyYXlbaV1dO1xuICAgIH1cbn1cbmV4cG9ydHMuc2h1ZmZsZUFycmF5ID0gc2h1ZmZsZUFycmF5O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dXRpbC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/socks/build/common/util.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/socks/build/index.js":
/*!*******************************************!*\
  !*** ./node_modules/socks/build/index.js ***!
  \*******************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./client/socksclient */ \"(rsc)/./node_modules/socks/build/client/socksclient.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvc29ja3MvYnVpbGQvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsb0NBQW9DO0FBQ25EO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsYUFBYSxtQkFBTyxDQUFDLG9GQUFzQjtBQUMzQyIsInNvdXJjZXMiOlsiQzpcXENvZGVcXG9uZS1tYWlsXFxub2RlX21vZHVsZXNcXHNvY2tzXFxidWlsZFxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG52YXIgX19jcmVhdGVCaW5kaW5nID0gKHRoaXMgJiYgdGhpcy5fX2NyZWF0ZUJpbmRpbmcpIHx8IChPYmplY3QuY3JlYXRlID8gKGZ1bmN0aW9uKG8sIG0sIGssIGsyKSB7XG4gICAgaWYgKGsyID09PSB1bmRlZmluZWQpIGsyID0gaztcbiAgICB2YXIgZGVzYyA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3IobSwgayk7XG4gICAgaWYgKCFkZXNjIHx8IChcImdldFwiIGluIGRlc2MgPyAhbS5fX2VzTW9kdWxlIDogZGVzYy53cml0YWJsZSB8fCBkZXNjLmNvbmZpZ3VyYWJsZSkpIHtcbiAgICAgIGRlc2MgPSB7IGVudW1lcmFibGU6IHRydWUsIGdldDogZnVuY3Rpb24oKSB7IHJldHVybiBtW2tdOyB9IH07XG4gICAgfVxuICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShvLCBrMiwgZGVzYyk7XG59KSA6IChmdW5jdGlvbihvLCBtLCBrLCBrMikge1xuICAgIGlmIChrMiA9PT0gdW5kZWZpbmVkKSBrMiA9IGs7XG4gICAgb1trMl0gPSBtW2tdO1xufSkpO1xudmFyIF9fZXhwb3J0U3RhciA9ICh0aGlzICYmIHRoaXMuX19leHBvcnRTdGFyKSB8fCBmdW5jdGlvbihtLCBleHBvcnRzKSB7XG4gICAgZm9yICh2YXIgcCBpbiBtKSBpZiAocCAhPT0gXCJkZWZhdWx0XCIgJiYgIU9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChleHBvcnRzLCBwKSkgX19jcmVhdGVCaW5kaW5nKGV4cG9ydHMsIG0sIHApO1xufTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbl9fZXhwb3J0U3RhcihyZXF1aXJlKFwiLi9jbGllbnQvc29ja3NjbGllbnRcIiksIGV4cG9ydHMpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/socks/build/index.js\n");

/***/ })

};
;