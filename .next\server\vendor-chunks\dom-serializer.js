"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/dom-serializer";
exports.ids = ["vendor-chunks/dom-serializer"];
exports.modules = {

/***/ "(rsc)/./node_modules/dom-serializer/lib/foreignNames.js":
/*!*********************************************************!*\
  !*** ./node_modules/dom-serializer/lib/foreignNames.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.attributeNames = exports.elementNames = void 0;\nexports.elementNames = new Map([\n    \"altGlyph\",\n    \"altGlyphDef\",\n    \"altGlyphItem\",\n    \"animateColor\",\n    \"animateMotion\",\n    \"animateTransform\",\n    \"clipPath\",\n    \"feBlend\",\n    \"feColorMatrix\",\n    \"feComponentTransfer\",\n    \"feComposite\",\n    \"feConvolveMatrix\",\n    \"feDiffuseLighting\",\n    \"feDisplacementMap\",\n    \"feDistantLight\",\n    \"feDropShadow\",\n    \"feFlood\",\n    \"feFuncA\",\n    \"feFuncB\",\n    \"feFuncG\",\n    \"feFuncR\",\n    \"feGaussianBlur\",\n    \"feImage\",\n    \"feMerge\",\n    \"feMergeNode\",\n    \"feMorphology\",\n    \"feOffset\",\n    \"fePointLight\",\n    \"feSpecularLighting\",\n    \"feSpotLight\",\n    \"feTile\",\n    \"feTurbulence\",\n    \"foreignObject\",\n    \"glyphRef\",\n    \"linearGradient\",\n    \"radialGradient\",\n    \"textPath\",\n].map(function (val) { return [val.toLowerCase(), val]; }));\nexports.attributeNames = new Map([\n    \"definitionURL\",\n    \"attributeName\",\n    \"attributeType\",\n    \"baseFrequency\",\n    \"baseProfile\",\n    \"calcMode\",\n    \"clipPathUnits\",\n    \"diffuseConstant\",\n    \"edgeMode\",\n    \"filterUnits\",\n    \"glyphRef\",\n    \"gradientTransform\",\n    \"gradientUnits\",\n    \"kernelMatrix\",\n    \"kernelUnitLength\",\n    \"keyPoints\",\n    \"keySplines\",\n    \"keyTimes\",\n    \"lengthAdjust\",\n    \"limitingConeAngle\",\n    \"markerHeight\",\n    \"markerUnits\",\n    \"markerWidth\",\n    \"maskContentUnits\",\n    \"maskUnits\",\n    \"numOctaves\",\n    \"pathLength\",\n    \"patternContentUnits\",\n    \"patternTransform\",\n    \"patternUnits\",\n    \"pointsAtX\",\n    \"pointsAtY\",\n    \"pointsAtZ\",\n    \"preserveAlpha\",\n    \"preserveAspectRatio\",\n    \"primitiveUnits\",\n    \"refX\",\n    \"refY\",\n    \"repeatCount\",\n    \"repeatDur\",\n    \"requiredExtensions\",\n    \"requiredFeatures\",\n    \"specularConstant\",\n    \"specularExponent\",\n    \"spreadMethod\",\n    \"startOffset\",\n    \"stdDeviation\",\n    \"stitchTiles\",\n    \"surfaceScale\",\n    \"systemLanguage\",\n    \"tableValues\",\n    \"targetX\",\n    \"targetY\",\n    \"textLength\",\n    \"viewBox\",\n    \"viewTarget\",\n    \"xChannelSelector\",\n    \"yChannelSelector\",\n    \"zoomAndPan\",\n].map(function (val) { return [val.toLowerCase(), val]; }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/dom-serializer/lib/foreignNames.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/dom-serializer/lib/index.js":
/*!**************************************************!*\
  !*** ./node_modules/dom-serializer/lib/index.js ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.render = void 0;\n/*\n * Module dependencies\n */\nvar ElementType = __importStar(__webpack_require__(/*! domelementtype */ \"(rsc)/./node_modules/domelementtype/lib/index.js\"));\nvar entities_1 = __webpack_require__(/*! entities */ \"(rsc)/./node_modules/entities/lib/index.js\");\n/**\n * Mixed-case SVG and MathML tags & attributes\n * recognized by the HTML parser.\n *\n * @see https://html.spec.whatwg.org/multipage/parsing.html#parsing-main-inforeign\n */\nvar foreignNames_js_1 = __webpack_require__(/*! ./foreignNames.js */ \"(rsc)/./node_modules/dom-serializer/lib/foreignNames.js\");\nvar unencodedElements = new Set([\n    \"style\",\n    \"script\",\n    \"xmp\",\n    \"iframe\",\n    \"noembed\",\n    \"noframes\",\n    \"plaintext\",\n    \"noscript\",\n]);\nfunction replaceQuotes(value) {\n    return value.replace(/\"/g, \"&quot;\");\n}\n/**\n * Format attributes\n */\nfunction formatAttributes(attributes, opts) {\n    var _a;\n    if (!attributes)\n        return;\n    var encode = ((_a = opts.encodeEntities) !== null && _a !== void 0 ? _a : opts.decodeEntities) === false\n        ? replaceQuotes\n        : opts.xmlMode || opts.encodeEntities !== \"utf8\"\n            ? entities_1.encodeXML\n            : entities_1.escapeAttribute;\n    return Object.keys(attributes)\n        .map(function (key) {\n        var _a, _b;\n        var value = (_a = attributes[key]) !== null && _a !== void 0 ? _a : \"\";\n        if (opts.xmlMode === \"foreign\") {\n            /* Fix up mixed-case attribute names */\n            key = (_b = foreignNames_js_1.attributeNames.get(key)) !== null && _b !== void 0 ? _b : key;\n        }\n        if (!opts.emptyAttrs && !opts.xmlMode && value === \"\") {\n            return key;\n        }\n        return \"\".concat(key, \"=\\\"\").concat(encode(value), \"\\\"\");\n    })\n        .join(\" \");\n}\n/**\n * Self-enclosing tags\n */\nvar singleTag = new Set([\n    \"area\",\n    \"base\",\n    \"basefont\",\n    \"br\",\n    \"col\",\n    \"command\",\n    \"embed\",\n    \"frame\",\n    \"hr\",\n    \"img\",\n    \"input\",\n    \"isindex\",\n    \"keygen\",\n    \"link\",\n    \"meta\",\n    \"param\",\n    \"source\",\n    \"track\",\n    \"wbr\",\n]);\n/**\n * Renders a DOM node or an array of DOM nodes to a string.\n *\n * Can be thought of as the equivalent of the `outerHTML` of the passed node(s).\n *\n * @param node Node to be rendered.\n * @param options Changes serialization behavior\n */\nfunction render(node, options) {\n    if (options === void 0) { options = {}; }\n    var nodes = \"length\" in node ? node : [node];\n    var output = \"\";\n    for (var i = 0; i < nodes.length; i++) {\n        output += renderNode(nodes[i], options);\n    }\n    return output;\n}\nexports.render = render;\nexports[\"default\"] = render;\nfunction renderNode(node, options) {\n    switch (node.type) {\n        case ElementType.Root:\n            return render(node.children, options);\n        // @ts-expect-error We don't use `Doctype` yet\n        case ElementType.Doctype:\n        case ElementType.Directive:\n            return renderDirective(node);\n        case ElementType.Comment:\n            return renderComment(node);\n        case ElementType.CDATA:\n            return renderCdata(node);\n        case ElementType.Script:\n        case ElementType.Style:\n        case ElementType.Tag:\n            return renderTag(node, options);\n        case ElementType.Text:\n            return renderText(node, options);\n    }\n}\nvar foreignModeIntegrationPoints = new Set([\n    \"mi\",\n    \"mo\",\n    \"mn\",\n    \"ms\",\n    \"mtext\",\n    \"annotation-xml\",\n    \"foreignObject\",\n    \"desc\",\n    \"title\",\n]);\nvar foreignElements = new Set([\"svg\", \"math\"]);\nfunction renderTag(elem, opts) {\n    var _a;\n    // Handle SVG / MathML in HTML\n    if (opts.xmlMode === \"foreign\") {\n        /* Fix up mixed-case element names */\n        elem.name = (_a = foreignNames_js_1.elementNames.get(elem.name)) !== null && _a !== void 0 ? _a : elem.name;\n        /* Exit foreign mode at integration points */\n        if (elem.parent &&\n            foreignModeIntegrationPoints.has(elem.parent.name)) {\n            opts = __assign(__assign({}, opts), { xmlMode: false });\n        }\n    }\n    if (!opts.xmlMode && foreignElements.has(elem.name)) {\n        opts = __assign(__assign({}, opts), { xmlMode: \"foreign\" });\n    }\n    var tag = \"<\".concat(elem.name);\n    var attribs = formatAttributes(elem.attribs, opts);\n    if (attribs) {\n        tag += \" \".concat(attribs);\n    }\n    if (elem.children.length === 0 &&\n        (opts.xmlMode\n            ? // In XML mode or foreign mode, and user hasn't explicitly turned off self-closing tags\n                opts.selfClosingTags !== false\n            : // User explicitly asked for self-closing tags, even in HTML mode\n                opts.selfClosingTags && singleTag.has(elem.name))) {\n        if (!opts.xmlMode)\n            tag += \" \";\n        tag += \"/>\";\n    }\n    else {\n        tag += \">\";\n        if (elem.children.length > 0) {\n            tag += render(elem.children, opts);\n        }\n        if (opts.xmlMode || !singleTag.has(elem.name)) {\n            tag += \"</\".concat(elem.name, \">\");\n        }\n    }\n    return tag;\n}\nfunction renderDirective(elem) {\n    return \"<\".concat(elem.data, \">\");\n}\nfunction renderText(elem, opts) {\n    var _a;\n    var data = elem.data || \"\";\n    // If entities weren't decoded, no need to encode them back\n    if (((_a = opts.encodeEntities) !== null && _a !== void 0 ? _a : opts.decodeEntities) !== false &&\n        !(!opts.xmlMode &&\n            elem.parent &&\n            unencodedElements.has(elem.parent.name))) {\n        data =\n            opts.xmlMode || opts.encodeEntities !== \"utf8\"\n                ? (0, entities_1.encodeXML)(data)\n                : (0, entities_1.escapeText)(data);\n    }\n    return data;\n}\nfunction renderCdata(elem) {\n    return \"<![CDATA[\".concat(elem.children[0].data, \"]]>\");\n}\nfunction renderComment(elem) {\n    return \"<!--\".concat(elem.data, \"-->\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/dom-serializer/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dom-serializer/lib/foreignNames.js":
/*!*********************************************************!*\
  !*** ./node_modules/dom-serializer/lib/foreignNames.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.attributeNames = exports.elementNames = void 0;\nexports.elementNames = new Map([\n    \"altGlyph\",\n    \"altGlyphDef\",\n    \"altGlyphItem\",\n    \"animateColor\",\n    \"animateMotion\",\n    \"animateTransform\",\n    \"clipPath\",\n    \"feBlend\",\n    \"feColorMatrix\",\n    \"feComponentTransfer\",\n    \"feComposite\",\n    \"feConvolveMatrix\",\n    \"feDiffuseLighting\",\n    \"feDisplacementMap\",\n    \"feDistantLight\",\n    \"feDropShadow\",\n    \"feFlood\",\n    \"feFuncA\",\n    \"feFuncB\",\n    \"feFuncG\",\n    \"feFuncR\",\n    \"feGaussianBlur\",\n    \"feImage\",\n    \"feMerge\",\n    \"feMergeNode\",\n    \"feMorphology\",\n    \"feOffset\",\n    \"fePointLight\",\n    \"feSpecularLighting\",\n    \"feSpotLight\",\n    \"feTile\",\n    \"feTurbulence\",\n    \"foreignObject\",\n    \"glyphRef\",\n    \"linearGradient\",\n    \"radialGradient\",\n    \"textPath\",\n].map(function (val) { return [val.toLowerCase(), val]; }));\nexports.attributeNames = new Map([\n    \"definitionURL\",\n    \"attributeName\",\n    \"attributeType\",\n    \"baseFrequency\",\n    \"baseProfile\",\n    \"calcMode\",\n    \"clipPathUnits\",\n    \"diffuseConstant\",\n    \"edgeMode\",\n    \"filterUnits\",\n    \"glyphRef\",\n    \"gradientTransform\",\n    \"gradientUnits\",\n    \"kernelMatrix\",\n    \"kernelUnitLength\",\n    \"keyPoints\",\n    \"keySplines\",\n    \"keyTimes\",\n    \"lengthAdjust\",\n    \"limitingConeAngle\",\n    \"markerHeight\",\n    \"markerUnits\",\n    \"markerWidth\",\n    \"maskContentUnits\",\n    \"maskUnits\",\n    \"numOctaves\",\n    \"pathLength\",\n    \"patternContentUnits\",\n    \"patternTransform\",\n    \"patternUnits\",\n    \"pointsAtX\",\n    \"pointsAtY\",\n    \"pointsAtZ\",\n    \"preserveAlpha\",\n    \"preserveAspectRatio\",\n    \"primitiveUnits\",\n    \"refX\",\n    \"refY\",\n    \"repeatCount\",\n    \"repeatDur\",\n    \"requiredExtensions\",\n    \"requiredFeatures\",\n    \"specularConstant\",\n    \"specularExponent\",\n    \"spreadMethod\",\n    \"startOffset\",\n    \"stdDeviation\",\n    \"stitchTiles\",\n    \"surfaceScale\",\n    \"systemLanguage\",\n    \"tableValues\",\n    \"targetX\",\n    \"targetY\",\n    \"textLength\",\n    \"viewBox\",\n    \"viewTarget\",\n    \"xChannelSelector\",\n    \"yChannelSelector\",\n    \"zoomAndPan\",\n].map(function (val) { return [val.toLowerCase(), val]; }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dom-serializer/lib/foreignNames.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dom-serializer/lib/index.js":
/*!**************************************************!*\
  !*** ./node_modules/dom-serializer/lib/index.js ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.render = void 0;\n/*\n * Module dependencies\n */\nvar ElementType = __importStar(__webpack_require__(/*! domelementtype */ \"(ssr)/./node_modules/domelementtype/lib/index.js\"));\nvar entities_1 = __webpack_require__(/*! entities */ \"(ssr)/./node_modules/entities/lib/index.js\");\n/**\n * Mixed-case SVG and MathML tags & attributes\n * recognized by the HTML parser.\n *\n * @see https://html.spec.whatwg.org/multipage/parsing.html#parsing-main-inforeign\n */\nvar foreignNames_js_1 = __webpack_require__(/*! ./foreignNames.js */ \"(ssr)/./node_modules/dom-serializer/lib/foreignNames.js\");\nvar unencodedElements = new Set([\n    \"style\",\n    \"script\",\n    \"xmp\",\n    \"iframe\",\n    \"noembed\",\n    \"noframes\",\n    \"plaintext\",\n    \"noscript\",\n]);\nfunction replaceQuotes(value) {\n    return value.replace(/\"/g, \"&quot;\");\n}\n/**\n * Format attributes\n */\nfunction formatAttributes(attributes, opts) {\n    var _a;\n    if (!attributes)\n        return;\n    var encode = ((_a = opts.encodeEntities) !== null && _a !== void 0 ? _a : opts.decodeEntities) === false\n        ? replaceQuotes\n        : opts.xmlMode || opts.encodeEntities !== \"utf8\"\n            ? entities_1.encodeXML\n            : entities_1.escapeAttribute;\n    return Object.keys(attributes)\n        .map(function (key) {\n        var _a, _b;\n        var value = (_a = attributes[key]) !== null && _a !== void 0 ? _a : \"\";\n        if (opts.xmlMode === \"foreign\") {\n            /* Fix up mixed-case attribute names */\n            key = (_b = foreignNames_js_1.attributeNames.get(key)) !== null && _b !== void 0 ? _b : key;\n        }\n        if (!opts.emptyAttrs && !opts.xmlMode && value === \"\") {\n            return key;\n        }\n        return \"\".concat(key, \"=\\\"\").concat(encode(value), \"\\\"\");\n    })\n        .join(\" \");\n}\n/**\n * Self-enclosing tags\n */\nvar singleTag = new Set([\n    \"area\",\n    \"base\",\n    \"basefont\",\n    \"br\",\n    \"col\",\n    \"command\",\n    \"embed\",\n    \"frame\",\n    \"hr\",\n    \"img\",\n    \"input\",\n    \"isindex\",\n    \"keygen\",\n    \"link\",\n    \"meta\",\n    \"param\",\n    \"source\",\n    \"track\",\n    \"wbr\",\n]);\n/**\n * Renders a DOM node or an array of DOM nodes to a string.\n *\n * Can be thought of as the equivalent of the `outerHTML` of the passed node(s).\n *\n * @param node Node to be rendered.\n * @param options Changes serialization behavior\n */\nfunction render(node, options) {\n    if (options === void 0) { options = {}; }\n    var nodes = \"length\" in node ? node : [node];\n    var output = \"\";\n    for (var i = 0; i < nodes.length; i++) {\n        output += renderNode(nodes[i], options);\n    }\n    return output;\n}\nexports.render = render;\nexports[\"default\"] = render;\nfunction renderNode(node, options) {\n    switch (node.type) {\n        case ElementType.Root:\n            return render(node.children, options);\n        // @ts-expect-error We don't use `Doctype` yet\n        case ElementType.Doctype:\n        case ElementType.Directive:\n            return renderDirective(node);\n        case ElementType.Comment:\n            return renderComment(node);\n        case ElementType.CDATA:\n            return renderCdata(node);\n        case ElementType.Script:\n        case ElementType.Style:\n        case ElementType.Tag:\n            return renderTag(node, options);\n        case ElementType.Text:\n            return renderText(node, options);\n    }\n}\nvar foreignModeIntegrationPoints = new Set([\n    \"mi\",\n    \"mo\",\n    \"mn\",\n    \"ms\",\n    \"mtext\",\n    \"annotation-xml\",\n    \"foreignObject\",\n    \"desc\",\n    \"title\",\n]);\nvar foreignElements = new Set([\"svg\", \"math\"]);\nfunction renderTag(elem, opts) {\n    var _a;\n    // Handle SVG / MathML in HTML\n    if (opts.xmlMode === \"foreign\") {\n        /* Fix up mixed-case element names */\n        elem.name = (_a = foreignNames_js_1.elementNames.get(elem.name)) !== null && _a !== void 0 ? _a : elem.name;\n        /* Exit foreign mode at integration points */\n        if (elem.parent &&\n            foreignModeIntegrationPoints.has(elem.parent.name)) {\n            opts = __assign(__assign({}, opts), { xmlMode: false });\n        }\n    }\n    if (!opts.xmlMode && foreignElements.has(elem.name)) {\n        opts = __assign(__assign({}, opts), { xmlMode: \"foreign\" });\n    }\n    var tag = \"<\".concat(elem.name);\n    var attribs = formatAttributes(elem.attribs, opts);\n    if (attribs) {\n        tag += \" \".concat(attribs);\n    }\n    if (elem.children.length === 0 &&\n        (opts.xmlMode\n            ? // In XML mode or foreign mode, and user hasn't explicitly turned off self-closing tags\n                opts.selfClosingTags !== false\n            : // User explicitly asked for self-closing tags, even in HTML mode\n                opts.selfClosingTags && singleTag.has(elem.name))) {\n        if (!opts.xmlMode)\n            tag += \" \";\n        tag += \"/>\";\n    }\n    else {\n        tag += \">\";\n        if (elem.children.length > 0) {\n            tag += render(elem.children, opts);\n        }\n        if (opts.xmlMode || !singleTag.has(elem.name)) {\n            tag += \"</\".concat(elem.name, \">\");\n        }\n    }\n    return tag;\n}\nfunction renderDirective(elem) {\n    return \"<\".concat(elem.data, \">\");\n}\nfunction renderText(elem, opts) {\n    var _a;\n    var data = elem.data || \"\";\n    // If entities weren't decoded, no need to encode them back\n    if (((_a = opts.encodeEntities) !== null && _a !== void 0 ? _a : opts.decodeEntities) !== false &&\n        !(!opts.xmlMode &&\n            elem.parent &&\n            unencodedElements.has(elem.parent.name))) {\n        data =\n            opts.xmlMode || opts.encodeEntities !== \"utf8\"\n                ? (0, entities_1.encodeXML)(data)\n                : (0, entities_1.escapeText)(data);\n    }\n    return data;\n}\nfunction renderCdata(elem) {\n    return \"<![CDATA[\".concat(elem.children[0].data, \"]]>\");\n}\nfunction renderComment(elem) {\n    return \"<!--\".concat(elem.data, \"-->\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dom-serializer/lib/index.js\n");

/***/ })

};
;