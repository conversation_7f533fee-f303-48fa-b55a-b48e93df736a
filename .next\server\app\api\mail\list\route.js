/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/mail/list/route";
exports.ids = ["app/api/mail/list/route"];
exports.modules = {

/***/ "(rsc)/./app/api/mail/list/route.ts":
/*!************************************!*\
  !*** ./app/api/mail/list/route.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/config */ \"(rsc)/./lib/config.ts\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _lib_imap__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/imap */ \"(rsc)/./lib/imap.ts\");\n\n\n\n\nasync function GET(req) {\n    try {\n        const auth = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.verifyAuth)(req);\n        const { requiredRole, imap } = (0,_lib_config__WEBPACK_IMPORTED_MODULE_0__.getServerEnv)();\n        (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.ensureRole)(auth, requiredRole);\n        const { searchParams } = new URL(req.url);\n        const page = Number(searchParams.get(\"page\") || \"1\");\n        const pageSize = Number(searchParams.get(\"pageSize\") || \"20\");\n        const q = (searchParams.get(\"q\") || \"\").trim();\n        // If IMAP credentials are not configured, return mock data for demo\n        if (!imap.user || !imap.pass) {\n            const mock = mockList(page, pageSize, q);\n            return Response.json(mock, {\n                status: 200\n            });\n        }\n        const result = await (0,_lib_imap__WEBPACK_IMPORTED_MODULE_2__.withImapClient)(imap, async (client)=>{\n            return await (0,_lib_imap__WEBPACK_IMPORTED_MODULE_2__.listMessages)(client, {\n                page,\n                pageSize,\n                recipientFilter: q || undefined\n            });\n        });\n        return Response.json(result, {\n            status: 200\n        });\n    } catch (err) {\n        const status = Number(err?.status) || 500;\n        const message = typeof err?.message === \"string\" ? err.message : \"Internal Server Error\";\n        return new Response(message, {\n            status\n        });\n    }\n}\nfunction mockList(page, pageSize, q) {\n    const totalAll = 48;\n    const base = Array.from({\n        length: totalAll\n    }).map((_, i)=>{\n        const uid = 5000 + (totalAll - i);\n        const recipients = [\n            \"<EMAIL>\",\n            ...i % 3 === 0 ? [\n                \"<EMAIL>\"\n            ] : [],\n            ...i % 5 === 0 ? [\n                \"<EMAIL>\"\n            ] : []\n        ];\n        return {\n            uid,\n            subject: `Demo message #${uid}`,\n            from: i % 2 === 0 ? \"Alice <<EMAIL>>\" : \"Bob <<EMAIL>>\",\n            recipients,\n            date: new Date(Date.now() - i * 36e5).toISOString(),\n            seen: i > 3\n        };\n    });\n    const filtered = q ? base.filter((m)=>m.recipients.join(\" \").toLowerCase().includes(q.toLowerCase())) : base;\n    const total = filtered.length;\n    const start = Math.max(0, (Math.max(1, page) - 1) * Math.min(100, Math.max(5, pageSize)));\n    const items = filtered.slice(start, start + pageSize);\n    return {\n        items,\n        total,\n        page,\n        pageSize\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./app/api/mail/list/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ensureRole: () => (/* binding */ ensureRole),\n/* harmony export */   verifyAuth: () => (/* binding */ verifyAuth)\n/* harmony export */ });\n/* harmony import */ var jose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/jose/dist/webapi/jwks/remote.js\");\n/* harmony import */ var jose__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/jose/dist/webapi/jwt/verify.js\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./config */ \"(rsc)/./lib/config.ts\");\n\n\nasync function verifyAuth(req) {\n    const auth = req.headers.get(\"authorization\") || \"\";\n    const token = auth.startsWith(\"Bearer \") ? auth.slice(7) : null;\n    if (!token) {\n        throw new Error(\"Missing Authorization header\");\n    }\n    const { issuer, clientId, requiredRole } = (0,_config__WEBPACK_IMPORTED_MODULE_0__.getServerEnv)();\n    // Demo fallback: if issuer is not configured, accept \"Bearer demo\"\n    if (!issuer) {\n        if (token === \"demo\") {\n            return {\n                sub: \"demo\",\n                email: \"<EMAIL>\",\n                roles: new Set([\n                    requiredRole\n                ]),\n                token,\n                isDemo: true\n            };\n        }\n        const err = new Error(\"Auth disabled (no issuer). Use 'Bearer demo' for Demo Mode.\");\n        err.status = 401;\n        throw err;\n    }\n    const JWKS = (0,jose__WEBPACK_IMPORTED_MODULE_1__.createRemoteJWKSet)(new URL(`${issuer}/protocol/openid-connect/certs`));\n    const { payload } = await (0,jose__WEBPACK_IMPORTED_MODULE_2__.jwtVerify)(token, JWKS, {\n        issuer,\n        audience: clientId ? [\n            clientId\n        ] : undefined\n    });\n    const roles = extractRoles(payload, clientId || undefined);\n    return {\n        sub: String(payload.sub),\n        email: payload.email,\n        roles,\n        token,\n        isDemo: false\n    };\n}\nfunction extractRoles(payload, clientId) {\n    const realmRoles = payload.realm_access?.roles || [];\n    const clientRoles = clientId ? payload.resource_access?.[clientId]?.roles || [] : [];\n    return new Set([\n        ...realmRoles,\n        ...clientRoles\n    ]);\n}\nfunction ensureRole(auth, requiredRole) {\n    if (!auth.roles.has(requiredRole)) {\n        const err = new Error(\"Forbidden: missing required role\");\n        err.status = 403;\n        throw err;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/config.ts":
/*!***********************!*\
  !*** ./lib/config.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getServerEnv: () => (/* binding */ getServerEnv)\n/* harmony export */ });\nfunction getServerEnv() {\n    const requiredRole = process.env.KEYCLOAK_REQUIRED_ROLE || \"mail-reader\";\n    // Prefer explicit issuer; else derive from public values (safe for server use)\n    const issuer = process.env.KEYCLOAK_ISSUER_URL || (process.env.NEXT_PUBLIC_KEYCLOAK_URL && process.env.NEXT_PUBLIC_KEYCLOAK_REALM ? `${process.env.NEXT_PUBLIC_KEYCLOAK_URL}/realms/${process.env.NEXT_PUBLIC_KEYCLOAK_REALM}` : undefined);\n    const clientId = process.env.KEYCLOAK_CLIENT_ID || process.env.NEXT_PUBLIC_KEYCLOAK_CLIENT_ID;\n    const imapHost = process.env.GMAIL_IMAP_HOST || \"imap.gmail.com\";\n    const imapPort = Number(process.env.GMAIL_IMAP_PORT || \"993\");\n    const imapUser = process.env.GMAIL_IMAP_USER;\n    const imapPass = process.env.GMAIL_IMAP_PASSWORD;\n    return {\n        requiredRole,\n        issuer,\n        clientId,\n        imap: {\n            host: imapHost,\n            port: imapPort,\n            user: imapUser,\n            pass: imapPass,\n            secure: true\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/config.ts\n");

/***/ }),

/***/ "(rsc)/./lib/imap.ts":
/*!*********************!*\
  !*** ./lib/imap.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getMessageDetail: () => (/* binding */ getMessageDetail),\n/* harmony export */   listMessages: () => (/* binding */ listMessages),\n/* harmony export */   withImapClient: () => (/* binding */ withImapClient)\n/* harmony export */ });\n/* harmony import */ var imapflow__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! imapflow */ \"(rsc)/./node_modules/imapflow/lib/imap-flow.js\");\n/* harmony import */ var mailparser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! mailparser */ \"(rsc)/./node_modules/mailparser/index.js\");\n/* harmony import */ var mailparser__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(mailparser__WEBPACK_IMPORTED_MODULE_1__);\n\n\nasync function withImapClient(cfg, fn) {\n    if (!cfg.user || !cfg.pass) {\n        throw new Error(\"IMAP credentials not configured\");\n    }\n    const client = new imapflow__WEBPACK_IMPORTED_MODULE_0__.ImapFlow({\n        host: cfg.host,\n        port: cfg.port,\n        secure: cfg.secure,\n        auth: {\n            user: cfg.user,\n            pass: cfg.pass\n        },\n        logger: false\n    });\n    try {\n        await client.connect();\n        await client.mailboxOpen(\"INBOX\", {\n            readOnly: true\n        });\n        const res = await fn(client);\n        return res;\n    } finally{\n        try {\n            await client.logout();\n        } catch  {\n        // ignore\n        }\n    }\n}\nasync function listMessages(client, opts) {\n    const page = Math.max(1, opts.page);\n    const pageSize = Math.min(100, Math.max(5, opts.pageSize));\n    const q = (opts.recipientFilter || \"\").trim().toLowerCase();\n    // 1) Get all UIDs (ascending), reverse for latest first\n    const allUids = await client.search({\n        all: true\n    }, {\n        uid: true\n    });\n    const uidsDesc = allUids.slice().reverse();\n    let filteredUids = uidsDesc;\n    // 2) If filtering by recipient, scan headers in batches and build filtered uid list\n    if (q) {\n        filteredUids = [];\n        const batchSize = 200;\n        for(let i = 0; i < uidsDesc.length; i += batchSize){\n            const batch = uidsDesc.slice(i, i + batchSize);\n            const matches = await filterBatchByRecipient(client, batch, q);\n            filteredUids.push(...matches);\n        }\n    }\n    const total = filteredUids.length;\n    const start = (page - 1) * pageSize;\n    const pageUids = filteredUids.slice(start, start + pageSize);\n    const items = await fetchListMetadata(client, pageUids);\n    return {\n        items,\n        total,\n        page,\n        pageSize\n    };\n}\nasync function filterBatchByRecipient(client, uids, q) {\n    const result = [];\n    const fetcher = client.fetch(uids, {\n        uid: true,\n        envelope: true,\n        flags: true,\n        headers: true\n    });\n    for await (const msg of fetcher){\n        const headers = normalizeHeaders(msg);\n        const combined = [];\n        for (const field of [\n            \"to\",\n            \"cc\",\n            \"bcc\"\n        ]){\n            const headerVal = headers[field];\n            if (headerVal) combined.push(headerVal);\n        }\n        for (const field of [\n            \"delivered-to\",\n            \"x-forwarded-to\"\n        ]){\n            const headerVal = headers[field];\n            if (headerVal) combined.push(headerVal);\n        }\n        const hay = combined.join(\" \").toLowerCase();\n        if (hay.includes(q)) {\n            result.push(Number(msg.uid));\n        }\n    }\n    return result;\n}\nasync function fetchListMetadata(client, uids) {\n    if (uids.length === 0) return [];\n    const out = [];\n    const fetcher = client.fetch(uids, {\n        uid: true,\n        envelope: true,\n        flags: true,\n        headers: true\n    });\n    for await (const msg of fetcher){\n        const env = msg.envelope;\n        const seen = (msg.flags || []).includes(\"\\\\Seen\");\n        const fromStr = addressListToString(env.from);\n        const recipients = [];\n        if (env.to) recipients.push(...addressesToStrings(env.to));\n        if (env.cc) recipients.push(...addressesToStrings(env.cc));\n        if (env.bcc) recipients.push(...addressesToStrings(env.bcc));\n        const headers = normalizeHeaders(msg);\n        if (headers[\"delivered-to\"]) recipients.push(...splitAddresses(headers[\"delivered-to\"]));\n        if (headers[\"x-forwarded-to\"]) recipients.push(...splitAddresses(headers[\"x-forwarded-to\"]));\n        out.push({\n            uid: Number(msg.uid),\n            subject: env.subject || \"\",\n            from: fromStr,\n            recipients: uniqueStrings(recipients),\n            date: env.date ? new Date(env.date).toISOString() : new Date().toISOString(),\n            seen\n        });\n    }\n    // Keep same order as requested uids\n    const order = new Map(uids.map((u, i)=>[\n            u,\n            i\n        ]));\n    out.sort((a, b)=>order.get(a.uid) - order.get(b.uid));\n    return out;\n}\nfunction normalizeHeaders(msg) {\n    const h = {};\n    if (msg.headers) {\n        for (const [key, val] of msg.headers){\n            h[String(key).toLowerCase()] = Array.isArray(val) ? val.map(String).join(\", \") : String(val);\n        }\n    }\n    return h;\n}\nfunction addressListToString(list) {\n    if (!list || list.length === 0) return \"\";\n    const a = list[0];\n    if (!a) return \"\";\n    if (a.name && a.address) return `${a.name} <${a.address}>`;\n    return a.address || \"\";\n}\nfunction addressesToStrings(list) {\n    if (!list) return [];\n    return list.map((a)=>a?.name && a?.address ? `${a.name} <${a.address}>` : a?.address || \"\").filter(Boolean);\n}\nfunction splitAddresses(v) {\n    // These headers can be comma-separated\n    return v.split(\",\").map((s)=>s.trim()).filter(Boolean);\n}\nfunction uniqueStrings(arr) {\n    return Array.from(new Set(arr));\n}\nasync function getMessageDetail(client, uid) {\n    // Fetch headers + body structure + raw source to parse\n    let envelope;\n    let headersMap;\n    let bodyStructure;\n    let raw = null;\n    for await (const msg of client.fetch([\n        uid\n    ], {\n        uid: true,\n        envelope: true,\n        headers: true,\n        bodyStructure: true,\n        source: true\n    })){\n        envelope = msg.envelope;\n        headersMap = msg.headers;\n        bodyStructure = msg.bodyStructure;\n        // @ts-ignore - imapflow returns source as Buffer in Next.js\n        raw = msg.source;\n    }\n    if (!envelope || !headersMap || !bodyStructure) {\n        throw new Error(\"Message not found\");\n    }\n    const headers = normalizeHeaders({\n        headers: headersMap\n    });\n    const parsed = raw ? await (0,mailparser__WEBPACK_IMPORTED_MODULE_1__.simpleParser)(raw) : null;\n    const attachments = collectAttachments(bodyStructure);\n    return {\n        uid,\n        subject: envelope.subject || \"\",\n        from: addressListToString(envelope.from),\n        to: addressesToStrings(envelope.to),\n        cc: addressesToStrings(envelope.cc),\n        bcc: addressesToStrings(envelope.bcc),\n        deliveredTo: headers[\"delivered-to\"] ? splitAddresses(headers[\"delivered-to\"]) : [],\n        xForwardedTo: headers[\"x-forwarded-to\"] ? splitAddresses(headers[\"x-forwarded-to\"]) : [],\n        date: envelope.date ? new Date(envelope.date).toISOString() : new Date().toISOString(),\n        html: parsed?.html || undefined,\n        text: parsed?.text || undefined,\n        attachments\n    };\n}\nfunction collectAttachments(node) {\n    const out = [];\n    const walk = (n)=>{\n        const contentType = `${(n.type || \"\").toLowerCase()}/${(n.subtype || \"\").toLowerCase()}`;\n        const filename = n.dispositionParameters?.filename || n.params?.name;\n        const isAttachment = (n.disposition || \"\").toLowerCase() === \"attachment\" || Boolean(filename);\n        if (isAttachment && n.part) {\n            out.push({\n                partId: n.part,\n                filename: filename || \"\",\n                contentType,\n                size: n.size || 0\n            });\n        }\n        if (Array.isArray(n.childNodes)) {\n            for (const c of n.childNodes)walk(c);\n        }\n    };\n    walk(node);\n    return out;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/imap.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmail%2Flist%2Froute&page=%2Fapi%2Fmail%2Flist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmail%2Flist%2Froute.ts&appDir=C%3A%5CCode%5Cone-mail%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CCode%5Cone-mail&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmail%2Flist%2Froute&page=%2Fapi%2Fmail%2Flist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmail%2Flist%2Froute.ts&appDir=C%3A%5CCode%5Cone-mail%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CCode%5Cone-mail&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Code_one_mail_app_api_mail_list_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/mail/list/route.ts */ \"(rsc)/./app/api/mail/list/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/mail/list/route\",\n        pathname: \"/api/mail/list\",\n        filename: \"route\",\n        bundlePath: \"app/api/mail/list/route\"\n    },\n    resolvedPagePath: \"C:\\\\Code\\\\one-mail\\\\app\\\\api\\\\mail\\\\list\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Code_one_mail_app_api_mail_list_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmail%2Flist%2Froute&page=%2Fapi%2Fmail%2Flist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmail%2Flist%2Froute.ts&appDir=C%3A%5CCode%5Cone-mail%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CCode%5Cone-mail&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:os":
/*!**************************!*\
  !*** external "node:os" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:os");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("worker_threads");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/entities","vendor-chunks/htmlparser2","vendor-chunks/domutils","vendor-chunks/domhandler","vendor-chunks/dom-serializer","vendor-chunks/deepmerge","vendor-chunks/domelementtype","vendor-chunks/imapflow","vendor-chunks/encoding-japanese","vendor-chunks/iconv-lite","vendor-chunks/libmime","vendor-chunks/he","vendor-chunks/mailparser","vendor-chunks/html-to-text","vendor-chunks/jose","vendor-chunks/ip-address","vendor-chunks/socks","vendor-chunks/smart-buffer","vendor-chunks/pino","vendor-chunks/mailsplit","vendor-chunks/jsbn","vendor-chunks/linkify-it","vendor-chunks/fast-redact","vendor-chunks/safe-stable-stringify","vendor-chunks/sonic-boom","vendor-chunks/thread-stream","vendor-chunks/selderee","vendor-chunks/parseley","vendor-chunks/punycode.js","vendor-chunks/peberminta","vendor-chunks/tlds","vendor-chunks/pino-std-serializers","vendor-chunks/sprintf-js","vendor-chunks/libqp","vendor-chunks/libbase64","vendor-chunks/uc.micro","vendor-chunks/nodemailer","vendor-chunks/@selderee","vendor-chunks/quick-format-unescaped","vendor-chunks/safer-buffer","vendor-chunks/on-exit-leak-free","vendor-chunks/leac","vendor-chunks/atomic-sleep"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmail%2Flist%2Froute&page=%2Fapi%2Fmail%2Flist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmail%2Flist%2Froute.ts&appDir=C%3A%5CCode%5Cone-mail%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CCode%5Cone-mail&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();