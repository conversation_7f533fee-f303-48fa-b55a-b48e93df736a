"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/domutils";
exports.ids = ["vendor-chunks/domutils"];
exports.modules = {

/***/ "(rsc)/./node_modules/domutils/lib/feeds.js":
/*!********************************************!*\
  !*** ./node_modules/domutils/lib/feeds.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getFeed = getFeed;\nvar stringify_js_1 = __webpack_require__(/*! ./stringify.js */ \"(rsc)/./node_modules/domutils/lib/stringify.js\");\nvar legacy_js_1 = __webpack_require__(/*! ./legacy.js */ \"(rsc)/./node_modules/domutils/lib/legacy.js\");\n/**\n * Get the feed object from the root of a DOM tree.\n *\n * @category Feeds\n * @param doc - The DOM to to extract the feed from.\n * @returns The feed.\n */\nfunction getFeed(doc) {\n    var feedRoot = getOneElement(isValidFeed, doc);\n    return !feedRoot\n        ? null\n        : feedRoot.name === \"feed\"\n            ? getAtomFeed(feedRoot)\n            : getRssFeed(feedRoot);\n}\n/**\n * Parse an Atom feed.\n *\n * @param feedRoot The root of the feed.\n * @returns The parsed feed.\n */\nfunction getAtomFeed(feedRoot) {\n    var _a;\n    var childs = feedRoot.children;\n    var feed = {\n        type: \"atom\",\n        items: (0, legacy_js_1.getElementsByTagName)(\"entry\", childs).map(function (item) {\n            var _a;\n            var children = item.children;\n            var entry = { media: getMediaElements(children) };\n            addConditionally(entry, \"id\", \"id\", children);\n            addConditionally(entry, \"title\", \"title\", children);\n            var href = (_a = getOneElement(\"link\", children)) === null || _a === void 0 ? void 0 : _a.attribs[\"href\"];\n            if (href) {\n                entry.link = href;\n            }\n            var description = fetch(\"summary\", children) || fetch(\"content\", children);\n            if (description) {\n                entry.description = description;\n            }\n            var pubDate = fetch(\"updated\", children);\n            if (pubDate) {\n                entry.pubDate = new Date(pubDate);\n            }\n            return entry;\n        }),\n    };\n    addConditionally(feed, \"id\", \"id\", childs);\n    addConditionally(feed, \"title\", \"title\", childs);\n    var href = (_a = getOneElement(\"link\", childs)) === null || _a === void 0 ? void 0 : _a.attribs[\"href\"];\n    if (href) {\n        feed.link = href;\n    }\n    addConditionally(feed, \"description\", \"subtitle\", childs);\n    var updated = fetch(\"updated\", childs);\n    if (updated) {\n        feed.updated = new Date(updated);\n    }\n    addConditionally(feed, \"author\", \"email\", childs, true);\n    return feed;\n}\n/**\n * Parse a RSS feed.\n *\n * @param feedRoot The root of the feed.\n * @returns The parsed feed.\n */\nfunction getRssFeed(feedRoot) {\n    var _a, _b;\n    var childs = (_b = (_a = getOneElement(\"channel\", feedRoot.children)) === null || _a === void 0 ? void 0 : _a.children) !== null && _b !== void 0 ? _b : [];\n    var feed = {\n        type: feedRoot.name.substr(0, 3),\n        id: \"\",\n        items: (0, legacy_js_1.getElementsByTagName)(\"item\", feedRoot.children).map(function (item) {\n            var children = item.children;\n            var entry = { media: getMediaElements(children) };\n            addConditionally(entry, \"id\", \"guid\", children);\n            addConditionally(entry, \"title\", \"title\", children);\n            addConditionally(entry, \"link\", \"link\", children);\n            addConditionally(entry, \"description\", \"description\", children);\n            var pubDate = fetch(\"pubDate\", children) || fetch(\"dc:date\", children);\n            if (pubDate)\n                entry.pubDate = new Date(pubDate);\n            return entry;\n        }),\n    };\n    addConditionally(feed, \"title\", \"title\", childs);\n    addConditionally(feed, \"link\", \"link\", childs);\n    addConditionally(feed, \"description\", \"description\", childs);\n    var updated = fetch(\"lastBuildDate\", childs);\n    if (updated) {\n        feed.updated = new Date(updated);\n    }\n    addConditionally(feed, \"author\", \"managingEditor\", childs, true);\n    return feed;\n}\nvar MEDIA_KEYS_STRING = [\"url\", \"type\", \"lang\"];\nvar MEDIA_KEYS_INT = [\n    \"fileSize\",\n    \"bitrate\",\n    \"framerate\",\n    \"samplingrate\",\n    \"channels\",\n    \"duration\",\n    \"height\",\n    \"width\",\n];\n/**\n * Get all media elements of a feed item.\n *\n * @param where Nodes to search in.\n * @returns Media elements.\n */\nfunction getMediaElements(where) {\n    return (0, legacy_js_1.getElementsByTagName)(\"media:content\", where).map(function (elem) {\n        var attribs = elem.attribs;\n        var media = {\n            medium: attribs[\"medium\"],\n            isDefault: !!attribs[\"isDefault\"],\n        };\n        for (var _i = 0, MEDIA_KEYS_STRING_1 = MEDIA_KEYS_STRING; _i < MEDIA_KEYS_STRING_1.length; _i++) {\n            var attrib = MEDIA_KEYS_STRING_1[_i];\n            if (attribs[attrib]) {\n                media[attrib] = attribs[attrib];\n            }\n        }\n        for (var _a = 0, MEDIA_KEYS_INT_1 = MEDIA_KEYS_INT; _a < MEDIA_KEYS_INT_1.length; _a++) {\n            var attrib = MEDIA_KEYS_INT_1[_a];\n            if (attribs[attrib]) {\n                media[attrib] = parseInt(attribs[attrib], 10);\n            }\n        }\n        if (attribs[\"expression\"]) {\n            media.expression = attribs[\"expression\"];\n        }\n        return media;\n    });\n}\n/**\n * Get one element by tag name.\n *\n * @param tagName Tag name to look for\n * @param node Node to search in\n * @returns The element or null\n */\nfunction getOneElement(tagName, node) {\n    return (0, legacy_js_1.getElementsByTagName)(tagName, node, true, 1)[0];\n}\n/**\n * Get the text content of an element with a certain tag name.\n *\n * @param tagName Tag name to look for.\n * @param where Node to search in.\n * @param recurse Whether to recurse into child nodes.\n * @returns The text content of the element.\n */\nfunction fetch(tagName, where, recurse) {\n    if (recurse === void 0) { recurse = false; }\n    return (0, stringify_js_1.textContent)((0, legacy_js_1.getElementsByTagName)(tagName, where, recurse, 1)).trim();\n}\n/**\n * Adds a property to an object if it has a value.\n *\n * @param obj Object to be extended\n * @param prop Property name\n * @param tagName Tag name that contains the conditionally added property\n * @param where Element to search for the property\n * @param recurse Whether to recurse into child nodes.\n */\nfunction addConditionally(obj, prop, tagName, where, recurse) {\n    if (recurse === void 0) { recurse = false; }\n    var val = fetch(tagName, where, recurse);\n    if (val)\n        obj[prop] = val;\n}\n/**\n * Checks if an element is a feed root node.\n *\n * @param value The name of the element to check.\n * @returns Whether an element is a feed root node.\n */\nfunction isValidFeed(value) {\n    return value === \"rss\" || value === \"feed\" || value === \"rdf:RDF\";\n}\n//# sourceMappingURL=feeds.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domutils/lib/feeds.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/domutils/lib/helpers.js":
/*!**********************************************!*\
  !*** ./node_modules/domutils/lib/helpers.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DocumentPosition = void 0;\nexports.removeSubsets = removeSubsets;\nexports.compareDocumentPosition = compareDocumentPosition;\nexports.uniqueSort = uniqueSort;\nvar domhandler_1 = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/index.js\");\n/**\n * Given an array of nodes, remove any member that is contained by another\n * member.\n *\n * @category Helpers\n * @param nodes Nodes to filter.\n * @returns Remaining nodes that aren't contained by other nodes.\n */\nfunction removeSubsets(nodes) {\n    var idx = nodes.length;\n    /*\n     * Check if each node (or one of its ancestors) is already contained in the\n     * array.\n     */\n    while (--idx >= 0) {\n        var node = nodes[idx];\n        /*\n         * Remove the node if it is not unique.\n         * We are going through the array from the end, so we only\n         * have to check nodes that preceed the node under consideration in the array.\n         */\n        if (idx > 0 && nodes.lastIndexOf(node, idx - 1) >= 0) {\n            nodes.splice(idx, 1);\n            continue;\n        }\n        for (var ancestor = node.parent; ancestor; ancestor = ancestor.parent) {\n            if (nodes.includes(ancestor)) {\n                nodes.splice(idx, 1);\n                break;\n            }\n        }\n    }\n    return nodes;\n}\n/**\n * @category Helpers\n * @see {@link http://dom.spec.whatwg.org/#dom-node-comparedocumentposition}\n */\nvar DocumentPosition;\n(function (DocumentPosition) {\n    DocumentPosition[DocumentPosition[\"DISCONNECTED\"] = 1] = \"DISCONNECTED\";\n    DocumentPosition[DocumentPosition[\"PRECEDING\"] = 2] = \"PRECEDING\";\n    DocumentPosition[DocumentPosition[\"FOLLOWING\"] = 4] = \"FOLLOWING\";\n    DocumentPosition[DocumentPosition[\"CONTAINS\"] = 8] = \"CONTAINS\";\n    DocumentPosition[DocumentPosition[\"CONTAINED_BY\"] = 16] = \"CONTAINED_BY\";\n})(DocumentPosition || (exports.DocumentPosition = DocumentPosition = {}));\n/**\n * Compare the position of one node against another node in any other document,\n * returning a bitmask with the values from {@link DocumentPosition}.\n *\n * Document order:\n * > There is an ordering, document order, defined on all the nodes in the\n * > document corresponding to the order in which the first character of the\n * > XML representation of each node occurs in the XML representation of the\n * > document after expansion of general entities. Thus, the document element\n * > node will be the first node. Element nodes occur before their children.\n * > Thus, document order orders element nodes in order of the occurrence of\n * > their start-tag in the XML (after expansion of entities). The attribute\n * > nodes of an element occur after the element and before its children. The\n * > relative order of attribute nodes is implementation-dependent.\n *\n * Source:\n * http://www.w3.org/TR/DOM-Level-3-Core/glossary.html#dt-document-order\n *\n * @category Helpers\n * @param nodeA The first node to use in the comparison\n * @param nodeB The second node to use in the comparison\n * @returns A bitmask describing the input nodes' relative position.\n *\n * See http://dom.spec.whatwg.org/#dom-node-comparedocumentposition for\n * a description of these values.\n */\nfunction compareDocumentPosition(nodeA, nodeB) {\n    var aParents = [];\n    var bParents = [];\n    if (nodeA === nodeB) {\n        return 0;\n    }\n    var current = (0, domhandler_1.hasChildren)(nodeA) ? nodeA : nodeA.parent;\n    while (current) {\n        aParents.unshift(current);\n        current = current.parent;\n    }\n    current = (0, domhandler_1.hasChildren)(nodeB) ? nodeB : nodeB.parent;\n    while (current) {\n        bParents.unshift(current);\n        current = current.parent;\n    }\n    var maxIdx = Math.min(aParents.length, bParents.length);\n    var idx = 0;\n    while (idx < maxIdx && aParents[idx] === bParents[idx]) {\n        idx++;\n    }\n    if (idx === 0) {\n        return DocumentPosition.DISCONNECTED;\n    }\n    var sharedParent = aParents[idx - 1];\n    var siblings = sharedParent.children;\n    var aSibling = aParents[idx];\n    var bSibling = bParents[idx];\n    if (siblings.indexOf(aSibling) > siblings.indexOf(bSibling)) {\n        if (sharedParent === nodeB) {\n            return DocumentPosition.FOLLOWING | DocumentPosition.CONTAINED_BY;\n        }\n        return DocumentPosition.FOLLOWING;\n    }\n    if (sharedParent === nodeA) {\n        return DocumentPosition.PRECEDING | DocumentPosition.CONTAINS;\n    }\n    return DocumentPosition.PRECEDING;\n}\n/**\n * Sort an array of nodes based on their relative position in the document,\n * removing any duplicate nodes. If the array contains nodes that do not belong\n * to the same document, sort order is unspecified.\n *\n * @category Helpers\n * @param nodes Array of DOM nodes.\n * @returns Collection of unique nodes, sorted in document order.\n */\nfunction uniqueSort(nodes) {\n    nodes = nodes.filter(function (node, i, arr) { return !arr.includes(node, i + 1); });\n    nodes.sort(function (a, b) {\n        var relative = compareDocumentPosition(a, b);\n        if (relative & DocumentPosition.PRECEDING) {\n            return -1;\n        }\n        else if (relative & DocumentPosition.FOLLOWING) {\n            return 1;\n        }\n        return 0;\n    });\n    return nodes;\n}\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domutils/lib/helpers.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/domutils/lib/index.js":
/*!********************************************!*\
  !*** ./node_modules/domutils/lib/index.js ***!
  \********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.hasChildren = exports.isDocument = exports.isComment = exports.isText = exports.isCDATA = exports.isTag = void 0;\n__exportStar(__webpack_require__(/*! ./stringify.js */ \"(rsc)/./node_modules/domutils/lib/stringify.js\"), exports);\n__exportStar(__webpack_require__(/*! ./traversal.js */ \"(rsc)/./node_modules/domutils/lib/traversal.js\"), exports);\n__exportStar(__webpack_require__(/*! ./manipulation.js */ \"(rsc)/./node_modules/domutils/lib/manipulation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./querying.js */ \"(rsc)/./node_modules/domutils/lib/querying.js\"), exports);\n__exportStar(__webpack_require__(/*! ./legacy.js */ \"(rsc)/./node_modules/domutils/lib/legacy.js\"), exports);\n__exportStar(__webpack_require__(/*! ./helpers.js */ \"(rsc)/./node_modules/domutils/lib/helpers.js\"), exports);\n__exportStar(__webpack_require__(/*! ./feeds.js */ \"(rsc)/./node_modules/domutils/lib/feeds.js\"), exports);\n/** @deprecated Use these methods from `domhandler` directly. */\nvar domhandler_1 = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/index.js\");\nObject.defineProperty(exports, \"isTag\", ({ enumerable: true, get: function () { return domhandler_1.isTag; } }));\nObject.defineProperty(exports, \"isCDATA\", ({ enumerable: true, get: function () { return domhandler_1.isCDATA; } }));\nObject.defineProperty(exports, \"isText\", ({ enumerable: true, get: function () { return domhandler_1.isText; } }));\nObject.defineProperty(exports, \"isComment\", ({ enumerable: true, get: function () { return domhandler_1.isComment; } }));\nObject.defineProperty(exports, \"isDocument\", ({ enumerable: true, get: function () { return domhandler_1.isDocument; } }));\nObject.defineProperty(exports, \"hasChildren\", ({ enumerable: true, get: function () { return domhandler_1.hasChildren; } }));\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domutils/lib/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/domutils/lib/legacy.js":
/*!*********************************************!*\
  !*** ./node_modules/domutils/lib/legacy.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.testElement = testElement;\nexports.getElements = getElements;\nexports.getElementById = getElementById;\nexports.getElementsByTagName = getElementsByTagName;\nexports.getElementsByClassName = getElementsByClassName;\nexports.getElementsByTagType = getElementsByTagType;\nvar domhandler_1 = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/index.js\");\nvar querying_js_1 = __webpack_require__(/*! ./querying.js */ \"(rsc)/./node_modules/domutils/lib/querying.js\");\n/**\n * A map of functions to check nodes against.\n */\nvar Checks = {\n    tag_name: function (name) {\n        if (typeof name === \"function\") {\n            return function (elem) { return (0, domhandler_1.isTag)(elem) && name(elem.name); };\n        }\n        else if (name === \"*\") {\n            return domhandler_1.isTag;\n        }\n        return function (elem) { return (0, domhandler_1.isTag)(elem) && elem.name === name; };\n    },\n    tag_type: function (type) {\n        if (typeof type === \"function\") {\n            return function (elem) { return type(elem.type); };\n        }\n        return function (elem) { return elem.type === type; };\n    },\n    tag_contains: function (data) {\n        if (typeof data === \"function\") {\n            return function (elem) { return (0, domhandler_1.isText)(elem) && data(elem.data); };\n        }\n        return function (elem) { return (0, domhandler_1.isText)(elem) && elem.data === data; };\n    },\n};\n/**\n * Returns a function to check whether a node has an attribute with a particular\n * value.\n *\n * @param attrib Attribute to check.\n * @param value Attribute value to look for.\n * @returns A function to check whether the a node has an attribute with a\n *   particular value.\n */\nfunction getAttribCheck(attrib, value) {\n    if (typeof value === \"function\") {\n        return function (elem) { return (0, domhandler_1.isTag)(elem) && value(elem.attribs[attrib]); };\n    }\n    return function (elem) { return (0, domhandler_1.isTag)(elem) && elem.attribs[attrib] === value; };\n}\n/**\n * Returns a function that returns `true` if either of the input functions\n * returns `true` for a node.\n *\n * @param a First function to combine.\n * @param b Second function to combine.\n * @returns A function taking a node and returning `true` if either of the input\n *   functions returns `true` for the node.\n */\nfunction combineFuncs(a, b) {\n    return function (elem) { return a(elem) || b(elem); };\n}\n/**\n * Returns a function that executes all checks in `options` and returns `true`\n * if any of them match a node.\n *\n * @param options An object describing nodes to look for.\n * @returns A function that executes all checks in `options` and returns `true`\n *   if any of them match a node.\n */\nfunction compileTest(options) {\n    var funcs = Object.keys(options).map(function (key) {\n        var value = options[key];\n        return Object.prototype.hasOwnProperty.call(Checks, key)\n            ? Checks[key](value)\n            : getAttribCheck(key, value);\n    });\n    return funcs.length === 0 ? null : funcs.reduce(combineFuncs);\n}\n/**\n * Checks whether a node matches the description in `options`.\n *\n * @category Legacy Query Functions\n * @param options An object describing nodes to look for.\n * @param node The element to test.\n * @returns Whether the element matches the description in `options`.\n */\nfunction testElement(options, node) {\n    var test = compileTest(options);\n    return test ? test(node) : true;\n}\n/**\n * Returns all nodes that match `options`.\n *\n * @category Legacy Query Functions\n * @param options An object describing nodes to look for.\n * @param nodes Nodes to search through.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes that match `options`.\n */\nfunction getElements(options, nodes, recurse, limit) {\n    if (limit === void 0) { limit = Infinity; }\n    var test = compileTest(options);\n    return test ? (0, querying_js_1.filter)(test, nodes, recurse, limit) : [];\n}\n/**\n * Returns the node with the supplied ID.\n *\n * @category Legacy Query Functions\n * @param id The unique ID attribute value to look for.\n * @param nodes Nodes to search through.\n * @param recurse Also consider child nodes.\n * @returns The node with the supplied ID.\n */\nfunction getElementById(id, nodes, recurse) {\n    if (recurse === void 0) { recurse = true; }\n    if (!Array.isArray(nodes))\n        nodes = [nodes];\n    return (0, querying_js_1.findOne)(getAttribCheck(\"id\", id), nodes, recurse);\n}\n/**\n * Returns all nodes with the supplied `tagName`.\n *\n * @category Legacy Query Functions\n * @param tagName Tag name to search for.\n * @param nodes Nodes to search through.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes with the supplied `tagName`.\n */\nfunction getElementsByTagName(tagName, nodes, recurse, limit) {\n    if (recurse === void 0) { recurse = true; }\n    if (limit === void 0) { limit = Infinity; }\n    return (0, querying_js_1.filter)(Checks[\"tag_name\"](tagName), nodes, recurse, limit);\n}\n/**\n * Returns all nodes with the supplied `className`.\n *\n * @category Legacy Query Functions\n * @param className Class name to search for.\n * @param nodes Nodes to search through.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes with the supplied `className`.\n */\nfunction getElementsByClassName(className, nodes, recurse, limit) {\n    if (recurse === void 0) { recurse = true; }\n    if (limit === void 0) { limit = Infinity; }\n    return (0, querying_js_1.filter)(getAttribCheck(\"class\", className), nodes, recurse, limit);\n}\n/**\n * Returns all nodes with the supplied `type`.\n *\n * @category Legacy Query Functions\n * @param type Element type to look for.\n * @param nodes Nodes to search through.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes with the supplied `type`.\n */\nfunction getElementsByTagType(type, nodes, recurse, limit) {\n    if (recurse === void 0) { recurse = true; }\n    if (limit === void 0) { limit = Infinity; }\n    return (0, querying_js_1.filter)(Checks[\"tag_type\"](type), nodes, recurse, limit);\n}\n//# sourceMappingURL=legacy.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domutils/lib/legacy.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/domutils/lib/manipulation.js":
/*!***************************************************!*\
  !*** ./node_modules/domutils/lib/manipulation.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.removeElement = removeElement;\nexports.replaceElement = replaceElement;\nexports.appendChild = appendChild;\nexports.append = append;\nexports.prependChild = prependChild;\nexports.prepend = prepend;\n/**\n * Remove an element from the dom\n *\n * @category Manipulation\n * @param elem The element to be removed\n */\nfunction removeElement(elem) {\n    if (elem.prev)\n        elem.prev.next = elem.next;\n    if (elem.next)\n        elem.next.prev = elem.prev;\n    if (elem.parent) {\n        var childs = elem.parent.children;\n        var childsIndex = childs.lastIndexOf(elem);\n        if (childsIndex >= 0) {\n            childs.splice(childsIndex, 1);\n        }\n    }\n    elem.next = null;\n    elem.prev = null;\n    elem.parent = null;\n}\n/**\n * Replace an element in the dom\n *\n * @category Manipulation\n * @param elem The element to be replaced\n * @param replacement The element to be added\n */\nfunction replaceElement(elem, replacement) {\n    var prev = (replacement.prev = elem.prev);\n    if (prev) {\n        prev.next = replacement;\n    }\n    var next = (replacement.next = elem.next);\n    if (next) {\n        next.prev = replacement;\n    }\n    var parent = (replacement.parent = elem.parent);\n    if (parent) {\n        var childs = parent.children;\n        childs[childs.lastIndexOf(elem)] = replacement;\n        elem.parent = null;\n    }\n}\n/**\n * Append a child to an element.\n *\n * @category Manipulation\n * @param parent The element to append to.\n * @param child The element to be added as a child.\n */\nfunction appendChild(parent, child) {\n    removeElement(child);\n    child.next = null;\n    child.parent = parent;\n    if (parent.children.push(child) > 1) {\n        var sibling = parent.children[parent.children.length - 2];\n        sibling.next = child;\n        child.prev = sibling;\n    }\n    else {\n        child.prev = null;\n    }\n}\n/**\n * Append an element after another.\n *\n * @category Manipulation\n * @param elem The element to append after.\n * @param next The element be added.\n */\nfunction append(elem, next) {\n    removeElement(next);\n    var parent = elem.parent;\n    var currNext = elem.next;\n    next.next = currNext;\n    next.prev = elem;\n    elem.next = next;\n    next.parent = parent;\n    if (currNext) {\n        currNext.prev = next;\n        if (parent) {\n            var childs = parent.children;\n            childs.splice(childs.lastIndexOf(currNext), 0, next);\n        }\n    }\n    else if (parent) {\n        parent.children.push(next);\n    }\n}\n/**\n * Prepend a child to an element.\n *\n * @category Manipulation\n * @param parent The element to prepend before.\n * @param child The element to be added as a child.\n */\nfunction prependChild(parent, child) {\n    removeElement(child);\n    child.parent = parent;\n    child.prev = null;\n    if (parent.children.unshift(child) !== 1) {\n        var sibling = parent.children[1];\n        sibling.prev = child;\n        child.next = sibling;\n    }\n    else {\n        child.next = null;\n    }\n}\n/**\n * Prepend an element before another.\n *\n * @category Manipulation\n * @param elem The element to prepend before.\n * @param prev The element be added.\n */\nfunction prepend(elem, prev) {\n    removeElement(prev);\n    var parent = elem.parent;\n    if (parent) {\n        var childs = parent.children;\n        childs.splice(childs.indexOf(elem), 0, prev);\n    }\n    if (elem.prev) {\n        elem.prev.next = prev;\n    }\n    prev.parent = parent;\n    prev.prev = elem.prev;\n    prev.next = elem;\n    elem.prev = prev;\n}\n//# sourceMappingURL=manipulation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domutils/lib/manipulation.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/domutils/lib/querying.js":
/*!***********************************************!*\
  !*** ./node_modules/domutils/lib/querying.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.filter = filter;\nexports.find = find;\nexports.findOneChild = findOneChild;\nexports.findOne = findOne;\nexports.existsOne = existsOne;\nexports.findAll = findAll;\nvar domhandler_1 = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/index.js\");\n/**\n * Search a node and its children for nodes passing a test function. If `node` is not an array, it will be wrapped in one.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param node Node to search. Will be included in the result set if it matches.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes passing `test`.\n */\nfunction filter(test, node, recurse, limit) {\n    if (recurse === void 0) { recurse = true; }\n    if (limit === void 0) { limit = Infinity; }\n    return find(test, Array.isArray(node) ? node : [node], recurse, limit);\n}\n/**\n * Search an array of nodes and their children for nodes passing a test function.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param nodes Array of nodes to search.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes passing `test`.\n */\nfunction find(test, nodes, recurse, limit) {\n    var result = [];\n    /** Stack of the arrays we are looking at. */\n    var nodeStack = [Array.isArray(nodes) ? nodes : [nodes]];\n    /** Stack of the indices within the arrays. */\n    var indexStack = [0];\n    for (;;) {\n        // First, check if the current array has any more elements to look at.\n        if (indexStack[0] >= nodeStack[0].length) {\n            // If we have no more arrays to look at, we are done.\n            if (indexStack.length === 1) {\n                return result;\n            }\n            // Otherwise, remove the current array from the stack.\n            nodeStack.shift();\n            indexStack.shift();\n            // Loop back to the start to continue with the next array.\n            continue;\n        }\n        var elem = nodeStack[0][indexStack[0]++];\n        if (test(elem)) {\n            result.push(elem);\n            if (--limit <= 0)\n                return result;\n        }\n        if (recurse && (0, domhandler_1.hasChildren)(elem) && elem.children.length > 0) {\n            /*\n             * Add the children to the stack. We are depth-first, so this is\n             * the next array we look at.\n             */\n            indexStack.unshift(0);\n            nodeStack.unshift(elem.children);\n        }\n    }\n}\n/**\n * Finds the first element inside of an array that matches a test function. This is an alias for `Array.prototype.find`.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param nodes Array of nodes to search.\n * @returns The first node in the array that passes `test`.\n * @deprecated Use `Array.prototype.find` directly.\n */\nfunction findOneChild(test, nodes) {\n    return nodes.find(test);\n}\n/**\n * Finds one element in a tree that passes a test.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param nodes Node or array of nodes to search.\n * @param recurse Also consider child nodes.\n * @returns The first node that passes `test`.\n */\nfunction findOne(test, nodes, recurse) {\n    if (recurse === void 0) { recurse = true; }\n    var searchedNodes = Array.isArray(nodes) ? nodes : [nodes];\n    for (var i = 0; i < searchedNodes.length; i++) {\n        var node = searchedNodes[i];\n        if ((0, domhandler_1.isTag)(node) && test(node)) {\n            return node;\n        }\n        if (recurse && (0, domhandler_1.hasChildren)(node) && node.children.length > 0) {\n            var found = findOne(test, node.children, true);\n            if (found)\n                return found;\n        }\n    }\n    return null;\n}\n/**\n * Checks if a tree of nodes contains at least one node passing a test.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param nodes Array of nodes to search.\n * @returns Whether a tree of nodes contains at least one node passing the test.\n */\nfunction existsOne(test, nodes) {\n    return (Array.isArray(nodes) ? nodes : [nodes]).some(function (node) {\n        return ((0, domhandler_1.isTag)(node) && test(node)) ||\n            ((0, domhandler_1.hasChildren)(node) && existsOne(test, node.children));\n    });\n}\n/**\n * Search an array of nodes and their children for elements passing a test function.\n *\n * Same as `find`, but limited to elements and with less options, leading to reduced complexity.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param nodes Array of nodes to search.\n * @returns All nodes passing `test`.\n */\nfunction findAll(test, nodes) {\n    var result = [];\n    var nodeStack = [Array.isArray(nodes) ? nodes : [nodes]];\n    var indexStack = [0];\n    for (;;) {\n        if (indexStack[0] >= nodeStack[0].length) {\n            if (nodeStack.length === 1) {\n                return result;\n            }\n            // Otherwise, remove the current array from the stack.\n            nodeStack.shift();\n            indexStack.shift();\n            // Loop back to the start to continue with the next array.\n            continue;\n        }\n        var elem = nodeStack[0][indexStack[0]++];\n        if ((0, domhandler_1.isTag)(elem) && test(elem))\n            result.push(elem);\n        if ((0, domhandler_1.hasChildren)(elem) && elem.children.length > 0) {\n            indexStack.unshift(0);\n            nodeStack.unshift(elem.children);\n        }\n    }\n}\n//# sourceMappingURL=querying.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZG9tdXRpbHMvbGliL3F1ZXJ5aW5nLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGNBQWM7QUFDZCxZQUFZO0FBQ1osb0JBQW9CO0FBQ3BCLGVBQWU7QUFDZixpQkFBaUI7QUFDakIsZUFBZTtBQUNmLG1CQUFtQixtQkFBTyxDQUFDLGdFQUFZO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4QkFBOEI7QUFDOUIsNEJBQTRCO0FBQzVCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEJBQThCO0FBQzlCO0FBQ0Esb0JBQW9CLDBCQUEwQjtBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVztBQUNYO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxDb2RlXFxvbmUtbWFpbFxcbm9kZV9tb2R1bGVzXFxkb211dGlsc1xcbGliXFxxdWVyeWluZy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuZmlsdGVyID0gZmlsdGVyO1xuZXhwb3J0cy5maW5kID0gZmluZDtcbmV4cG9ydHMuZmluZE9uZUNoaWxkID0gZmluZE9uZUNoaWxkO1xuZXhwb3J0cy5maW5kT25lID0gZmluZE9uZTtcbmV4cG9ydHMuZXhpc3RzT25lID0gZXhpc3RzT25lO1xuZXhwb3J0cy5maW5kQWxsID0gZmluZEFsbDtcbnZhciBkb21oYW5kbGVyXzEgPSByZXF1aXJlKFwiZG9taGFuZGxlclwiKTtcbi8qKlxuICogU2VhcmNoIGEgbm9kZSBhbmQgaXRzIGNoaWxkcmVuIGZvciBub2RlcyBwYXNzaW5nIGEgdGVzdCBmdW5jdGlvbi4gSWYgYG5vZGVgIGlzIG5vdCBhbiBhcnJheSwgaXQgd2lsbCBiZSB3cmFwcGVkIGluIG9uZS5cbiAqXG4gKiBAY2F0ZWdvcnkgUXVlcnlpbmdcbiAqIEBwYXJhbSB0ZXN0IEZ1bmN0aW9uIHRvIHRlc3Qgbm9kZXMgb24uXG4gKiBAcGFyYW0gbm9kZSBOb2RlIHRvIHNlYXJjaC4gV2lsbCBiZSBpbmNsdWRlZCBpbiB0aGUgcmVzdWx0IHNldCBpZiBpdCBtYXRjaGVzLlxuICogQHBhcmFtIHJlY3Vyc2UgQWxzbyBjb25zaWRlciBjaGlsZCBub2Rlcy5cbiAqIEBwYXJhbSBsaW1pdCBNYXhpbXVtIG51bWJlciBvZiBub2RlcyB0byByZXR1cm4uXG4gKiBAcmV0dXJucyBBbGwgbm9kZXMgcGFzc2luZyBgdGVzdGAuXG4gKi9cbmZ1bmN0aW9uIGZpbHRlcih0ZXN0LCBub2RlLCByZWN1cnNlLCBsaW1pdCkge1xuICAgIGlmIChyZWN1cnNlID09PSB2b2lkIDApIHsgcmVjdXJzZSA9IHRydWU7IH1cbiAgICBpZiAobGltaXQgPT09IHZvaWQgMCkgeyBsaW1pdCA9IEluZmluaXR5OyB9XG4gICAgcmV0dXJuIGZpbmQodGVzdCwgQXJyYXkuaXNBcnJheShub2RlKSA/IG5vZGUgOiBbbm9kZV0sIHJlY3Vyc2UsIGxpbWl0KTtcbn1cbi8qKlxuICogU2VhcmNoIGFuIGFycmF5IG9mIG5vZGVzIGFuZCB0aGVpciBjaGlsZHJlbiBmb3Igbm9kZXMgcGFzc2luZyBhIHRlc3QgZnVuY3Rpb24uXG4gKlxuICogQGNhdGVnb3J5IFF1ZXJ5aW5nXG4gKiBAcGFyYW0gdGVzdCBGdW5jdGlvbiB0byB0ZXN0IG5vZGVzIG9uLlxuICogQHBhcmFtIG5vZGVzIEFycmF5IG9mIG5vZGVzIHRvIHNlYXJjaC5cbiAqIEBwYXJhbSByZWN1cnNlIEFsc28gY29uc2lkZXIgY2hpbGQgbm9kZXMuXG4gKiBAcGFyYW0gbGltaXQgTWF4aW11bSBudW1iZXIgb2Ygbm9kZXMgdG8gcmV0dXJuLlxuICogQHJldHVybnMgQWxsIG5vZGVzIHBhc3NpbmcgYHRlc3RgLlxuICovXG5mdW5jdGlvbiBmaW5kKHRlc3QsIG5vZGVzLCByZWN1cnNlLCBsaW1pdCkge1xuICAgIHZhciByZXN1bHQgPSBbXTtcbiAgICAvKiogU3RhY2sgb2YgdGhlIGFycmF5cyB3ZSBhcmUgbG9va2luZyBhdC4gKi9cbiAgICB2YXIgbm9kZVN0YWNrID0gW0FycmF5LmlzQXJyYXkobm9kZXMpID8gbm9kZXMgOiBbbm9kZXNdXTtcbiAgICAvKiogU3RhY2sgb2YgdGhlIGluZGljZXMgd2l0aGluIHRoZSBhcnJheXMuICovXG4gICAgdmFyIGluZGV4U3RhY2sgPSBbMF07XG4gICAgZm9yICg7Oykge1xuICAgICAgICAvLyBGaXJzdCwgY2hlY2sgaWYgdGhlIGN1cnJlbnQgYXJyYXkgaGFzIGFueSBtb3JlIGVsZW1lbnRzIHRvIGxvb2sgYXQuXG4gICAgICAgIGlmIChpbmRleFN0YWNrWzBdID49IG5vZGVTdGFja1swXS5sZW5ndGgpIHtcbiAgICAgICAgICAgIC8vIElmIHdlIGhhdmUgbm8gbW9yZSBhcnJheXMgdG8gbG9vayBhdCwgd2UgYXJlIGRvbmUuXG4gICAgICAgICAgICBpZiAoaW5kZXhTdGFjay5sZW5ndGggPT09IDEpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gcmVzdWx0O1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8gT3RoZXJ3aXNlLCByZW1vdmUgdGhlIGN1cnJlbnQgYXJyYXkgZnJvbSB0aGUgc3RhY2suXG4gICAgICAgICAgICBub2RlU3RhY2suc2hpZnQoKTtcbiAgICAgICAgICAgIGluZGV4U3RhY2suc2hpZnQoKTtcbiAgICAgICAgICAgIC8vIExvb3AgYmFjayB0byB0aGUgc3RhcnQgdG8gY29udGludWUgd2l0aCB0aGUgbmV4dCBhcnJheS5cbiAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICB9XG4gICAgICAgIHZhciBlbGVtID0gbm9kZVN0YWNrWzBdW2luZGV4U3RhY2tbMF0rK107XG4gICAgICAgIGlmICh0ZXN0KGVsZW0pKSB7XG4gICAgICAgICAgICByZXN1bHQucHVzaChlbGVtKTtcbiAgICAgICAgICAgIGlmICgtLWxpbWl0IDw9IDApXG4gICAgICAgICAgICAgICAgcmV0dXJuIHJlc3VsdDtcbiAgICAgICAgfVxuICAgICAgICBpZiAocmVjdXJzZSAmJiAoMCwgZG9taGFuZGxlcl8xLmhhc0NoaWxkcmVuKShlbGVtKSAmJiBlbGVtLmNoaWxkcmVuLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICAgIC8qXG4gICAgICAgICAgICAgKiBBZGQgdGhlIGNoaWxkcmVuIHRvIHRoZSBzdGFjay4gV2UgYXJlIGRlcHRoLWZpcnN0LCBzbyB0aGlzIGlzXG4gICAgICAgICAgICAgKiB0aGUgbmV4dCBhcnJheSB3ZSBsb29rIGF0LlxuICAgICAgICAgICAgICovXG4gICAgICAgICAgICBpbmRleFN0YWNrLnVuc2hpZnQoMCk7XG4gICAgICAgICAgICBub2RlU3RhY2sudW5zaGlmdChlbGVtLmNoaWxkcmVuKTtcbiAgICAgICAgfVxuICAgIH1cbn1cbi8qKlxuICogRmluZHMgdGhlIGZpcnN0IGVsZW1lbnQgaW5zaWRlIG9mIGFuIGFycmF5IHRoYXQgbWF0Y2hlcyBhIHRlc3QgZnVuY3Rpb24uIFRoaXMgaXMgYW4gYWxpYXMgZm9yIGBBcnJheS5wcm90b3R5cGUuZmluZGAuXG4gKlxuICogQGNhdGVnb3J5IFF1ZXJ5aW5nXG4gKiBAcGFyYW0gdGVzdCBGdW5jdGlvbiB0byB0ZXN0IG5vZGVzIG9uLlxuICogQHBhcmFtIG5vZGVzIEFycmF5IG9mIG5vZGVzIHRvIHNlYXJjaC5cbiAqIEByZXR1cm5zIFRoZSBmaXJzdCBub2RlIGluIHRoZSBhcnJheSB0aGF0IHBhc3NlcyBgdGVzdGAuXG4gKiBAZGVwcmVjYXRlZCBVc2UgYEFycmF5LnByb3RvdHlwZS5maW5kYCBkaXJlY3RseS5cbiAqL1xuZnVuY3Rpb24gZmluZE9uZUNoaWxkKHRlc3QsIG5vZGVzKSB7XG4gICAgcmV0dXJuIG5vZGVzLmZpbmQodGVzdCk7XG59XG4vKipcbiAqIEZpbmRzIG9uZSBlbGVtZW50IGluIGEgdHJlZSB0aGF0IHBhc3NlcyBhIHRlc3QuXG4gKlxuICogQGNhdGVnb3J5IFF1ZXJ5aW5nXG4gKiBAcGFyYW0gdGVzdCBGdW5jdGlvbiB0byB0ZXN0IG5vZGVzIG9uLlxuICogQHBhcmFtIG5vZGVzIE5vZGUgb3IgYXJyYXkgb2Ygbm9kZXMgdG8gc2VhcmNoLlxuICogQHBhcmFtIHJlY3Vyc2UgQWxzbyBjb25zaWRlciBjaGlsZCBub2Rlcy5cbiAqIEByZXR1cm5zIFRoZSBmaXJzdCBub2RlIHRoYXQgcGFzc2VzIGB0ZXN0YC5cbiAqL1xuZnVuY3Rpb24gZmluZE9uZSh0ZXN0LCBub2RlcywgcmVjdXJzZSkge1xuICAgIGlmIChyZWN1cnNlID09PSB2b2lkIDApIHsgcmVjdXJzZSA9IHRydWU7IH1cbiAgICB2YXIgc2VhcmNoZWROb2RlcyA9IEFycmF5LmlzQXJyYXkobm9kZXMpID8gbm9kZXMgOiBbbm9kZXNdO1xuICAgIGZvciAodmFyIGkgPSAwOyBpIDwgc2VhcmNoZWROb2Rlcy5sZW5ndGg7IGkrKykge1xuICAgICAgICB2YXIgbm9kZSA9IHNlYXJjaGVkTm9kZXNbaV07XG4gICAgICAgIGlmICgoMCwgZG9taGFuZGxlcl8xLmlzVGFnKShub2RlKSAmJiB0ZXN0KG5vZGUpKSB7XG4gICAgICAgICAgICByZXR1cm4gbm9kZTtcbiAgICAgICAgfVxuICAgICAgICBpZiAocmVjdXJzZSAmJiAoMCwgZG9taGFuZGxlcl8xLmhhc0NoaWxkcmVuKShub2RlKSAmJiBub2RlLmNoaWxkcmVuLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICAgIHZhciBmb3VuZCA9IGZpbmRPbmUodGVzdCwgbm9kZS5jaGlsZHJlbiwgdHJ1ZSk7XG4gICAgICAgICAgICBpZiAoZm91bmQpXG4gICAgICAgICAgICAgICAgcmV0dXJuIGZvdW5kO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiBudWxsO1xufVxuLyoqXG4gKiBDaGVja3MgaWYgYSB0cmVlIG9mIG5vZGVzIGNvbnRhaW5zIGF0IGxlYXN0IG9uZSBub2RlIHBhc3NpbmcgYSB0ZXN0LlxuICpcbiAqIEBjYXRlZ29yeSBRdWVyeWluZ1xuICogQHBhcmFtIHRlc3QgRnVuY3Rpb24gdG8gdGVzdCBub2RlcyBvbi5cbiAqIEBwYXJhbSBub2RlcyBBcnJheSBvZiBub2RlcyB0byBzZWFyY2guXG4gKiBAcmV0dXJucyBXaGV0aGVyIGEgdHJlZSBvZiBub2RlcyBjb250YWlucyBhdCBsZWFzdCBvbmUgbm9kZSBwYXNzaW5nIHRoZSB0ZXN0LlxuICovXG5mdW5jdGlvbiBleGlzdHNPbmUodGVzdCwgbm9kZXMpIHtcbiAgICByZXR1cm4gKEFycmF5LmlzQXJyYXkobm9kZXMpID8gbm9kZXMgOiBbbm9kZXNdKS5zb21lKGZ1bmN0aW9uIChub2RlKSB7XG4gICAgICAgIHJldHVybiAoKDAsIGRvbWhhbmRsZXJfMS5pc1RhZykobm9kZSkgJiYgdGVzdChub2RlKSkgfHxcbiAgICAgICAgICAgICgoMCwgZG9taGFuZGxlcl8xLmhhc0NoaWxkcmVuKShub2RlKSAmJiBleGlzdHNPbmUodGVzdCwgbm9kZS5jaGlsZHJlbikpO1xuICAgIH0pO1xufVxuLyoqXG4gKiBTZWFyY2ggYW4gYXJyYXkgb2Ygbm9kZXMgYW5kIHRoZWlyIGNoaWxkcmVuIGZvciBlbGVtZW50cyBwYXNzaW5nIGEgdGVzdCBmdW5jdGlvbi5cbiAqXG4gKiBTYW1lIGFzIGBmaW5kYCwgYnV0IGxpbWl0ZWQgdG8gZWxlbWVudHMgYW5kIHdpdGggbGVzcyBvcHRpb25zLCBsZWFkaW5nIHRvIHJlZHVjZWQgY29tcGxleGl0eS5cbiAqXG4gKiBAY2F0ZWdvcnkgUXVlcnlpbmdcbiAqIEBwYXJhbSB0ZXN0IEZ1bmN0aW9uIHRvIHRlc3Qgbm9kZXMgb24uXG4gKiBAcGFyYW0gbm9kZXMgQXJyYXkgb2Ygbm9kZXMgdG8gc2VhcmNoLlxuICogQHJldHVybnMgQWxsIG5vZGVzIHBhc3NpbmcgYHRlc3RgLlxuICovXG5mdW5jdGlvbiBmaW5kQWxsKHRlc3QsIG5vZGVzKSB7XG4gICAgdmFyIHJlc3VsdCA9IFtdO1xuICAgIHZhciBub2RlU3RhY2sgPSBbQXJyYXkuaXNBcnJheShub2RlcykgPyBub2RlcyA6IFtub2Rlc11dO1xuICAgIHZhciBpbmRleFN0YWNrID0gWzBdO1xuICAgIGZvciAoOzspIHtcbiAgICAgICAgaWYgKGluZGV4U3RhY2tbMF0gPj0gbm9kZVN0YWNrWzBdLmxlbmd0aCkge1xuICAgICAgICAgICAgaWYgKG5vZGVTdGFjay5sZW5ndGggPT09IDEpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gcmVzdWx0O1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8gT3RoZXJ3aXNlLCByZW1vdmUgdGhlIGN1cnJlbnQgYXJyYXkgZnJvbSB0aGUgc3RhY2suXG4gICAgICAgICAgICBub2RlU3RhY2suc2hpZnQoKTtcbiAgICAgICAgICAgIGluZGV4U3RhY2suc2hpZnQoKTtcbiAgICAgICAgICAgIC8vIExvb3AgYmFjayB0byB0aGUgc3RhcnQgdG8gY29udGludWUgd2l0aCB0aGUgbmV4dCBhcnJheS5cbiAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICB9XG4gICAgICAgIHZhciBlbGVtID0gbm9kZVN0YWNrWzBdW2luZGV4U3RhY2tbMF0rK107XG4gICAgICAgIGlmICgoMCwgZG9taGFuZGxlcl8xLmlzVGFnKShlbGVtKSAmJiB0ZXN0KGVsZW0pKVxuICAgICAgICAgICAgcmVzdWx0LnB1c2goZWxlbSk7XG4gICAgICAgIGlmICgoMCwgZG9taGFuZGxlcl8xLmhhc0NoaWxkcmVuKShlbGVtKSAmJiBlbGVtLmNoaWxkcmVuLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICAgIGluZGV4U3RhY2sudW5zaGlmdCgwKTtcbiAgICAgICAgICAgIG5vZGVTdGFjay51bnNoaWZ0KGVsZW0uY2hpbGRyZW4pO1xuICAgICAgICB9XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cXVlcnlpbmcuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domutils/lib/querying.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/domutils/lib/stringify.js":
/*!************************************************!*\
  !*** ./node_modules/domutils/lib/stringify.js ***!
  \************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getOuterHTML = getOuterHTML;\nexports.getInnerHTML = getInnerHTML;\nexports.getText = getText;\nexports.textContent = textContent;\nexports.innerText = innerText;\nvar domhandler_1 = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/index.js\");\nvar dom_serializer_1 = __importDefault(__webpack_require__(/*! dom-serializer */ \"(rsc)/./node_modules/dom-serializer/lib/index.js\"));\nvar domelementtype_1 = __webpack_require__(/*! domelementtype */ \"(rsc)/./node_modules/domelementtype/lib/index.js\");\n/**\n * @category Stringify\n * @deprecated Use the `dom-serializer` module directly.\n * @param node Node to get the outer HTML of.\n * @param options Options for serialization.\n * @returns `node`'s outer HTML.\n */\nfunction getOuterHTML(node, options) {\n    return (0, dom_serializer_1.default)(node, options);\n}\n/**\n * @category Stringify\n * @deprecated Use the `dom-serializer` module directly.\n * @param node Node to get the inner HTML of.\n * @param options Options for serialization.\n * @returns `node`'s inner HTML.\n */\nfunction getInnerHTML(node, options) {\n    return (0, domhandler_1.hasChildren)(node)\n        ? node.children.map(function (node) { return getOuterHTML(node, options); }).join(\"\")\n        : \"\";\n}\n/**\n * Get a node's inner text. Same as `textContent`, but inserts newlines for `<br>` tags. Ignores comments.\n *\n * @category Stringify\n * @deprecated Use `textContent` instead.\n * @param node Node to get the inner text of.\n * @returns `node`'s inner text.\n */\nfunction getText(node) {\n    if (Array.isArray(node))\n        return node.map(getText).join(\"\");\n    if ((0, domhandler_1.isTag)(node))\n        return node.name === \"br\" ? \"\\n\" : getText(node.children);\n    if ((0, domhandler_1.isCDATA)(node))\n        return getText(node.children);\n    if ((0, domhandler_1.isText)(node))\n        return node.data;\n    return \"\";\n}\n/**\n * Get a node's text content. Ignores comments.\n *\n * @category Stringify\n * @param node Node to get the text content of.\n * @returns `node`'s text content.\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/Node/textContent}\n */\nfunction textContent(node) {\n    if (Array.isArray(node))\n        return node.map(textContent).join(\"\");\n    if ((0, domhandler_1.hasChildren)(node) && !(0, domhandler_1.isComment)(node)) {\n        return textContent(node.children);\n    }\n    if ((0, domhandler_1.isText)(node))\n        return node.data;\n    return \"\";\n}\n/**\n * Get a node's inner text, ignoring `<script>` and `<style>` tags. Ignores comments.\n *\n * @category Stringify\n * @param node Node to get the inner text of.\n * @returns `node`'s inner text.\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/Node/innerText}\n */\nfunction innerText(node) {\n    if (Array.isArray(node))\n        return node.map(innerText).join(\"\");\n    if ((0, domhandler_1.hasChildren)(node) && (node.type === domelementtype_1.ElementType.Tag || (0, domhandler_1.isCDATA)(node))) {\n        return innerText(node.children);\n    }\n    if ((0, domhandler_1.isText)(node))\n        return node.data;\n    return \"\";\n}\n//# sourceMappingURL=stringify.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domutils/lib/stringify.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/domutils/lib/traversal.js":
/*!************************************************!*\
  !*** ./node_modules/domutils/lib/traversal.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getChildren = getChildren;\nexports.getParent = getParent;\nexports.getSiblings = getSiblings;\nexports.getAttributeValue = getAttributeValue;\nexports.hasAttrib = hasAttrib;\nexports.getName = getName;\nexports.nextElementSibling = nextElementSibling;\nexports.prevElementSibling = prevElementSibling;\nvar domhandler_1 = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/index.js\");\n/**\n * Get a node's children.\n *\n * @category Traversal\n * @param elem Node to get the children of.\n * @returns `elem`'s children, or an empty array.\n */\nfunction getChildren(elem) {\n    return (0, domhandler_1.hasChildren)(elem) ? elem.children : [];\n}\n/**\n * Get a node's parent.\n *\n * @category Traversal\n * @param elem Node to get the parent of.\n * @returns `elem`'s parent node, or `null` if `elem` is a root node.\n */\nfunction getParent(elem) {\n    return elem.parent || null;\n}\n/**\n * Gets an elements siblings, including the element itself.\n *\n * Attempts to get the children through the element's parent first. If we don't\n * have a parent (the element is a root node), we walk the element's `prev` &\n * `next` to get all remaining nodes.\n *\n * @category Traversal\n * @param elem Element to get the siblings of.\n * @returns `elem`'s siblings, including `elem`.\n */\nfunction getSiblings(elem) {\n    var _a, _b;\n    var parent = getParent(elem);\n    if (parent != null)\n        return getChildren(parent);\n    var siblings = [elem];\n    var prev = elem.prev, next = elem.next;\n    while (prev != null) {\n        siblings.unshift(prev);\n        (_a = prev, prev = _a.prev);\n    }\n    while (next != null) {\n        siblings.push(next);\n        (_b = next, next = _b.next);\n    }\n    return siblings;\n}\n/**\n * Gets an attribute from an element.\n *\n * @category Traversal\n * @param elem Element to check.\n * @param name Attribute name to retrieve.\n * @returns The element's attribute value, or `undefined`.\n */\nfunction getAttributeValue(elem, name) {\n    var _a;\n    return (_a = elem.attribs) === null || _a === void 0 ? void 0 : _a[name];\n}\n/**\n * Checks whether an element has an attribute.\n *\n * @category Traversal\n * @param elem Element to check.\n * @param name Attribute name to look for.\n * @returns Returns whether `elem` has the attribute `name`.\n */\nfunction hasAttrib(elem, name) {\n    return (elem.attribs != null &&\n        Object.prototype.hasOwnProperty.call(elem.attribs, name) &&\n        elem.attribs[name] != null);\n}\n/**\n * Get the tag name of an element.\n *\n * @category Traversal\n * @param elem The element to get the name for.\n * @returns The tag name of `elem`.\n */\nfunction getName(elem) {\n    return elem.name;\n}\n/**\n * Returns the next element sibling of a node.\n *\n * @category Traversal\n * @param elem The element to get the next sibling of.\n * @returns `elem`'s next sibling that is a tag, or `null` if there is no next\n * sibling.\n */\nfunction nextElementSibling(elem) {\n    var _a;\n    var next = elem.next;\n    while (next !== null && !(0, domhandler_1.isTag)(next))\n        (_a = next, next = _a.next);\n    return next;\n}\n/**\n * Returns the previous element sibling of a node.\n *\n * @category Traversal\n * @param elem The element to get the previous sibling of.\n * @returns `elem`'s previous sibling that is a tag, or `null` if there is no\n * previous sibling.\n */\nfunction prevElementSibling(elem) {\n    var _a;\n    var prev = elem.prev;\n    while (prev !== null && !(0, domhandler_1.isTag)(prev))\n        (_a = prev, prev = _a.prev);\n    return prev;\n}\n//# sourceMappingURL=traversal.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domutils/lib/traversal.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/domutils/lib/feeds.js":
/*!********************************************!*\
  !*** ./node_modules/domutils/lib/feeds.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getFeed = getFeed;\nvar stringify_js_1 = __webpack_require__(/*! ./stringify.js */ \"(ssr)/./node_modules/domutils/lib/stringify.js\");\nvar legacy_js_1 = __webpack_require__(/*! ./legacy.js */ \"(ssr)/./node_modules/domutils/lib/legacy.js\");\n/**\n * Get the feed object from the root of a DOM tree.\n *\n * @category Feeds\n * @param doc - The DOM to to extract the feed from.\n * @returns The feed.\n */\nfunction getFeed(doc) {\n    var feedRoot = getOneElement(isValidFeed, doc);\n    return !feedRoot\n        ? null\n        : feedRoot.name === \"feed\"\n            ? getAtomFeed(feedRoot)\n            : getRssFeed(feedRoot);\n}\n/**\n * Parse an Atom feed.\n *\n * @param feedRoot The root of the feed.\n * @returns The parsed feed.\n */\nfunction getAtomFeed(feedRoot) {\n    var _a;\n    var childs = feedRoot.children;\n    var feed = {\n        type: \"atom\",\n        items: (0, legacy_js_1.getElementsByTagName)(\"entry\", childs).map(function (item) {\n            var _a;\n            var children = item.children;\n            var entry = { media: getMediaElements(children) };\n            addConditionally(entry, \"id\", \"id\", children);\n            addConditionally(entry, \"title\", \"title\", children);\n            var href = (_a = getOneElement(\"link\", children)) === null || _a === void 0 ? void 0 : _a.attribs[\"href\"];\n            if (href) {\n                entry.link = href;\n            }\n            var description = fetch(\"summary\", children) || fetch(\"content\", children);\n            if (description) {\n                entry.description = description;\n            }\n            var pubDate = fetch(\"updated\", children);\n            if (pubDate) {\n                entry.pubDate = new Date(pubDate);\n            }\n            return entry;\n        }),\n    };\n    addConditionally(feed, \"id\", \"id\", childs);\n    addConditionally(feed, \"title\", \"title\", childs);\n    var href = (_a = getOneElement(\"link\", childs)) === null || _a === void 0 ? void 0 : _a.attribs[\"href\"];\n    if (href) {\n        feed.link = href;\n    }\n    addConditionally(feed, \"description\", \"subtitle\", childs);\n    var updated = fetch(\"updated\", childs);\n    if (updated) {\n        feed.updated = new Date(updated);\n    }\n    addConditionally(feed, \"author\", \"email\", childs, true);\n    return feed;\n}\n/**\n * Parse a RSS feed.\n *\n * @param feedRoot The root of the feed.\n * @returns The parsed feed.\n */\nfunction getRssFeed(feedRoot) {\n    var _a, _b;\n    var childs = (_b = (_a = getOneElement(\"channel\", feedRoot.children)) === null || _a === void 0 ? void 0 : _a.children) !== null && _b !== void 0 ? _b : [];\n    var feed = {\n        type: feedRoot.name.substr(0, 3),\n        id: \"\",\n        items: (0, legacy_js_1.getElementsByTagName)(\"item\", feedRoot.children).map(function (item) {\n            var children = item.children;\n            var entry = { media: getMediaElements(children) };\n            addConditionally(entry, \"id\", \"guid\", children);\n            addConditionally(entry, \"title\", \"title\", children);\n            addConditionally(entry, \"link\", \"link\", children);\n            addConditionally(entry, \"description\", \"description\", children);\n            var pubDate = fetch(\"pubDate\", children) || fetch(\"dc:date\", children);\n            if (pubDate)\n                entry.pubDate = new Date(pubDate);\n            return entry;\n        }),\n    };\n    addConditionally(feed, \"title\", \"title\", childs);\n    addConditionally(feed, \"link\", \"link\", childs);\n    addConditionally(feed, \"description\", \"description\", childs);\n    var updated = fetch(\"lastBuildDate\", childs);\n    if (updated) {\n        feed.updated = new Date(updated);\n    }\n    addConditionally(feed, \"author\", \"managingEditor\", childs, true);\n    return feed;\n}\nvar MEDIA_KEYS_STRING = [\"url\", \"type\", \"lang\"];\nvar MEDIA_KEYS_INT = [\n    \"fileSize\",\n    \"bitrate\",\n    \"framerate\",\n    \"samplingrate\",\n    \"channels\",\n    \"duration\",\n    \"height\",\n    \"width\",\n];\n/**\n * Get all media elements of a feed item.\n *\n * @param where Nodes to search in.\n * @returns Media elements.\n */\nfunction getMediaElements(where) {\n    return (0, legacy_js_1.getElementsByTagName)(\"media:content\", where).map(function (elem) {\n        var attribs = elem.attribs;\n        var media = {\n            medium: attribs[\"medium\"],\n            isDefault: !!attribs[\"isDefault\"],\n        };\n        for (var _i = 0, MEDIA_KEYS_STRING_1 = MEDIA_KEYS_STRING; _i < MEDIA_KEYS_STRING_1.length; _i++) {\n            var attrib = MEDIA_KEYS_STRING_1[_i];\n            if (attribs[attrib]) {\n                media[attrib] = attribs[attrib];\n            }\n        }\n        for (var _a = 0, MEDIA_KEYS_INT_1 = MEDIA_KEYS_INT; _a < MEDIA_KEYS_INT_1.length; _a++) {\n            var attrib = MEDIA_KEYS_INT_1[_a];\n            if (attribs[attrib]) {\n                media[attrib] = parseInt(attribs[attrib], 10);\n            }\n        }\n        if (attribs[\"expression\"]) {\n            media.expression = attribs[\"expression\"];\n        }\n        return media;\n    });\n}\n/**\n * Get one element by tag name.\n *\n * @param tagName Tag name to look for\n * @param node Node to search in\n * @returns The element or null\n */\nfunction getOneElement(tagName, node) {\n    return (0, legacy_js_1.getElementsByTagName)(tagName, node, true, 1)[0];\n}\n/**\n * Get the text content of an element with a certain tag name.\n *\n * @param tagName Tag name to look for.\n * @param where Node to search in.\n * @param recurse Whether to recurse into child nodes.\n * @returns The text content of the element.\n */\nfunction fetch(tagName, where, recurse) {\n    if (recurse === void 0) { recurse = false; }\n    return (0, stringify_js_1.textContent)((0, legacy_js_1.getElementsByTagName)(tagName, where, recurse, 1)).trim();\n}\n/**\n * Adds a property to an object if it has a value.\n *\n * @param obj Object to be extended\n * @param prop Property name\n * @param tagName Tag name that contains the conditionally added property\n * @param where Element to search for the property\n * @param recurse Whether to recurse into child nodes.\n */\nfunction addConditionally(obj, prop, tagName, where, recurse) {\n    if (recurse === void 0) { recurse = false; }\n    var val = fetch(tagName, where, recurse);\n    if (val)\n        obj[prop] = val;\n}\n/**\n * Checks if an element is a feed root node.\n *\n * @param value The name of the element to check.\n * @returns Whether an element is a feed root node.\n */\nfunction isValidFeed(value) {\n    return value === \"rss\" || value === \"feed\" || value === \"rdf:RDF\";\n}\n//# sourceMappingURL=feeds.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/domutils/lib/feeds.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/domutils/lib/helpers.js":
/*!**********************************************!*\
  !*** ./node_modules/domutils/lib/helpers.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DocumentPosition = void 0;\nexports.removeSubsets = removeSubsets;\nexports.compareDocumentPosition = compareDocumentPosition;\nexports.uniqueSort = uniqueSort;\nvar domhandler_1 = __webpack_require__(/*! domhandler */ \"(ssr)/./node_modules/domhandler/lib/index.js\");\n/**\n * Given an array of nodes, remove any member that is contained by another\n * member.\n *\n * @category Helpers\n * @param nodes Nodes to filter.\n * @returns Remaining nodes that aren't contained by other nodes.\n */\nfunction removeSubsets(nodes) {\n    var idx = nodes.length;\n    /*\n     * Check if each node (or one of its ancestors) is already contained in the\n     * array.\n     */\n    while (--idx >= 0) {\n        var node = nodes[idx];\n        /*\n         * Remove the node if it is not unique.\n         * We are going through the array from the end, so we only\n         * have to check nodes that preceed the node under consideration in the array.\n         */\n        if (idx > 0 && nodes.lastIndexOf(node, idx - 1) >= 0) {\n            nodes.splice(idx, 1);\n            continue;\n        }\n        for (var ancestor = node.parent; ancestor; ancestor = ancestor.parent) {\n            if (nodes.includes(ancestor)) {\n                nodes.splice(idx, 1);\n                break;\n            }\n        }\n    }\n    return nodes;\n}\n/**\n * @category Helpers\n * @see {@link http://dom.spec.whatwg.org/#dom-node-comparedocumentposition}\n */\nvar DocumentPosition;\n(function (DocumentPosition) {\n    DocumentPosition[DocumentPosition[\"DISCONNECTED\"] = 1] = \"DISCONNECTED\";\n    DocumentPosition[DocumentPosition[\"PRECEDING\"] = 2] = \"PRECEDING\";\n    DocumentPosition[DocumentPosition[\"FOLLOWING\"] = 4] = \"FOLLOWING\";\n    DocumentPosition[DocumentPosition[\"CONTAINS\"] = 8] = \"CONTAINS\";\n    DocumentPosition[DocumentPosition[\"CONTAINED_BY\"] = 16] = \"CONTAINED_BY\";\n})(DocumentPosition || (exports.DocumentPosition = DocumentPosition = {}));\n/**\n * Compare the position of one node against another node in any other document,\n * returning a bitmask with the values from {@link DocumentPosition}.\n *\n * Document order:\n * > There is an ordering, document order, defined on all the nodes in the\n * > document corresponding to the order in which the first character of the\n * > XML representation of each node occurs in the XML representation of the\n * > document after expansion of general entities. Thus, the document element\n * > node will be the first node. Element nodes occur before their children.\n * > Thus, document order orders element nodes in order of the occurrence of\n * > their start-tag in the XML (after expansion of entities). The attribute\n * > nodes of an element occur after the element and before its children. The\n * > relative order of attribute nodes is implementation-dependent.\n *\n * Source:\n * http://www.w3.org/TR/DOM-Level-3-Core/glossary.html#dt-document-order\n *\n * @category Helpers\n * @param nodeA The first node to use in the comparison\n * @param nodeB The second node to use in the comparison\n * @returns A bitmask describing the input nodes' relative position.\n *\n * See http://dom.spec.whatwg.org/#dom-node-comparedocumentposition for\n * a description of these values.\n */\nfunction compareDocumentPosition(nodeA, nodeB) {\n    var aParents = [];\n    var bParents = [];\n    if (nodeA === nodeB) {\n        return 0;\n    }\n    var current = (0, domhandler_1.hasChildren)(nodeA) ? nodeA : nodeA.parent;\n    while (current) {\n        aParents.unshift(current);\n        current = current.parent;\n    }\n    current = (0, domhandler_1.hasChildren)(nodeB) ? nodeB : nodeB.parent;\n    while (current) {\n        bParents.unshift(current);\n        current = current.parent;\n    }\n    var maxIdx = Math.min(aParents.length, bParents.length);\n    var idx = 0;\n    while (idx < maxIdx && aParents[idx] === bParents[idx]) {\n        idx++;\n    }\n    if (idx === 0) {\n        return DocumentPosition.DISCONNECTED;\n    }\n    var sharedParent = aParents[idx - 1];\n    var siblings = sharedParent.children;\n    var aSibling = aParents[idx];\n    var bSibling = bParents[idx];\n    if (siblings.indexOf(aSibling) > siblings.indexOf(bSibling)) {\n        if (sharedParent === nodeB) {\n            return DocumentPosition.FOLLOWING | DocumentPosition.CONTAINED_BY;\n        }\n        return DocumentPosition.FOLLOWING;\n    }\n    if (sharedParent === nodeA) {\n        return DocumentPosition.PRECEDING | DocumentPosition.CONTAINS;\n    }\n    return DocumentPosition.PRECEDING;\n}\n/**\n * Sort an array of nodes based on their relative position in the document,\n * removing any duplicate nodes. If the array contains nodes that do not belong\n * to the same document, sort order is unspecified.\n *\n * @category Helpers\n * @param nodes Array of DOM nodes.\n * @returns Collection of unique nodes, sorted in document order.\n */\nfunction uniqueSort(nodes) {\n    nodes = nodes.filter(function (node, i, arr) { return !arr.includes(node, i + 1); });\n    nodes.sort(function (a, b) {\n        var relative = compareDocumentPosition(a, b);\n        if (relative & DocumentPosition.PRECEDING) {\n            return -1;\n        }\n        else if (relative & DocumentPosition.FOLLOWING) {\n            return 1;\n        }\n        return 0;\n    });\n    return nodes;\n}\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/domutils/lib/helpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/domutils/lib/index.js":
/*!********************************************!*\
  !*** ./node_modules/domutils/lib/index.js ***!
  \********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.hasChildren = exports.isDocument = exports.isComment = exports.isText = exports.isCDATA = exports.isTag = void 0;\n__exportStar(__webpack_require__(/*! ./stringify.js */ \"(ssr)/./node_modules/domutils/lib/stringify.js\"), exports);\n__exportStar(__webpack_require__(/*! ./traversal.js */ \"(ssr)/./node_modules/domutils/lib/traversal.js\"), exports);\n__exportStar(__webpack_require__(/*! ./manipulation.js */ \"(ssr)/./node_modules/domutils/lib/manipulation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./querying.js */ \"(ssr)/./node_modules/domutils/lib/querying.js\"), exports);\n__exportStar(__webpack_require__(/*! ./legacy.js */ \"(ssr)/./node_modules/domutils/lib/legacy.js\"), exports);\n__exportStar(__webpack_require__(/*! ./helpers.js */ \"(ssr)/./node_modules/domutils/lib/helpers.js\"), exports);\n__exportStar(__webpack_require__(/*! ./feeds.js */ \"(ssr)/./node_modules/domutils/lib/feeds.js\"), exports);\n/** @deprecated Use these methods from `domhandler` directly. */\nvar domhandler_1 = __webpack_require__(/*! domhandler */ \"(ssr)/./node_modules/domhandler/lib/index.js\");\nObject.defineProperty(exports, \"isTag\", ({ enumerable: true, get: function () { return domhandler_1.isTag; } }));\nObject.defineProperty(exports, \"isCDATA\", ({ enumerable: true, get: function () { return domhandler_1.isCDATA; } }));\nObject.defineProperty(exports, \"isText\", ({ enumerable: true, get: function () { return domhandler_1.isText; } }));\nObject.defineProperty(exports, \"isComment\", ({ enumerable: true, get: function () { return domhandler_1.isComment; } }));\nObject.defineProperty(exports, \"isDocument\", ({ enumerable: true, get: function () { return domhandler_1.isDocument; } }));\nObject.defineProperty(exports, \"hasChildren\", ({ enumerable: true, get: function () { return domhandler_1.hasChildren; } }));\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/domutils/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/domutils/lib/legacy.js":
/*!*********************************************!*\
  !*** ./node_modules/domutils/lib/legacy.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.testElement = testElement;\nexports.getElements = getElements;\nexports.getElementById = getElementById;\nexports.getElementsByTagName = getElementsByTagName;\nexports.getElementsByClassName = getElementsByClassName;\nexports.getElementsByTagType = getElementsByTagType;\nvar domhandler_1 = __webpack_require__(/*! domhandler */ \"(ssr)/./node_modules/domhandler/lib/index.js\");\nvar querying_js_1 = __webpack_require__(/*! ./querying.js */ \"(ssr)/./node_modules/domutils/lib/querying.js\");\n/**\n * A map of functions to check nodes against.\n */\nvar Checks = {\n    tag_name: function (name) {\n        if (typeof name === \"function\") {\n            return function (elem) { return (0, domhandler_1.isTag)(elem) && name(elem.name); };\n        }\n        else if (name === \"*\") {\n            return domhandler_1.isTag;\n        }\n        return function (elem) { return (0, domhandler_1.isTag)(elem) && elem.name === name; };\n    },\n    tag_type: function (type) {\n        if (typeof type === \"function\") {\n            return function (elem) { return type(elem.type); };\n        }\n        return function (elem) { return elem.type === type; };\n    },\n    tag_contains: function (data) {\n        if (typeof data === \"function\") {\n            return function (elem) { return (0, domhandler_1.isText)(elem) && data(elem.data); };\n        }\n        return function (elem) { return (0, domhandler_1.isText)(elem) && elem.data === data; };\n    },\n};\n/**\n * Returns a function to check whether a node has an attribute with a particular\n * value.\n *\n * @param attrib Attribute to check.\n * @param value Attribute value to look for.\n * @returns A function to check whether the a node has an attribute with a\n *   particular value.\n */\nfunction getAttribCheck(attrib, value) {\n    if (typeof value === \"function\") {\n        return function (elem) { return (0, domhandler_1.isTag)(elem) && value(elem.attribs[attrib]); };\n    }\n    return function (elem) { return (0, domhandler_1.isTag)(elem) && elem.attribs[attrib] === value; };\n}\n/**\n * Returns a function that returns `true` if either of the input functions\n * returns `true` for a node.\n *\n * @param a First function to combine.\n * @param b Second function to combine.\n * @returns A function taking a node and returning `true` if either of the input\n *   functions returns `true` for the node.\n */\nfunction combineFuncs(a, b) {\n    return function (elem) { return a(elem) || b(elem); };\n}\n/**\n * Returns a function that executes all checks in `options` and returns `true`\n * if any of them match a node.\n *\n * @param options An object describing nodes to look for.\n * @returns A function that executes all checks in `options` and returns `true`\n *   if any of them match a node.\n */\nfunction compileTest(options) {\n    var funcs = Object.keys(options).map(function (key) {\n        var value = options[key];\n        return Object.prototype.hasOwnProperty.call(Checks, key)\n            ? Checks[key](value)\n            : getAttribCheck(key, value);\n    });\n    return funcs.length === 0 ? null : funcs.reduce(combineFuncs);\n}\n/**\n * Checks whether a node matches the description in `options`.\n *\n * @category Legacy Query Functions\n * @param options An object describing nodes to look for.\n * @param node The element to test.\n * @returns Whether the element matches the description in `options`.\n */\nfunction testElement(options, node) {\n    var test = compileTest(options);\n    return test ? test(node) : true;\n}\n/**\n * Returns all nodes that match `options`.\n *\n * @category Legacy Query Functions\n * @param options An object describing nodes to look for.\n * @param nodes Nodes to search through.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes that match `options`.\n */\nfunction getElements(options, nodes, recurse, limit) {\n    if (limit === void 0) { limit = Infinity; }\n    var test = compileTest(options);\n    return test ? (0, querying_js_1.filter)(test, nodes, recurse, limit) : [];\n}\n/**\n * Returns the node with the supplied ID.\n *\n * @category Legacy Query Functions\n * @param id The unique ID attribute value to look for.\n * @param nodes Nodes to search through.\n * @param recurse Also consider child nodes.\n * @returns The node with the supplied ID.\n */\nfunction getElementById(id, nodes, recurse) {\n    if (recurse === void 0) { recurse = true; }\n    if (!Array.isArray(nodes))\n        nodes = [nodes];\n    return (0, querying_js_1.findOne)(getAttribCheck(\"id\", id), nodes, recurse);\n}\n/**\n * Returns all nodes with the supplied `tagName`.\n *\n * @category Legacy Query Functions\n * @param tagName Tag name to search for.\n * @param nodes Nodes to search through.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes with the supplied `tagName`.\n */\nfunction getElementsByTagName(tagName, nodes, recurse, limit) {\n    if (recurse === void 0) { recurse = true; }\n    if (limit === void 0) { limit = Infinity; }\n    return (0, querying_js_1.filter)(Checks[\"tag_name\"](tagName), nodes, recurse, limit);\n}\n/**\n * Returns all nodes with the supplied `className`.\n *\n * @category Legacy Query Functions\n * @param className Class name to search for.\n * @param nodes Nodes to search through.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes with the supplied `className`.\n */\nfunction getElementsByClassName(className, nodes, recurse, limit) {\n    if (recurse === void 0) { recurse = true; }\n    if (limit === void 0) { limit = Infinity; }\n    return (0, querying_js_1.filter)(getAttribCheck(\"class\", className), nodes, recurse, limit);\n}\n/**\n * Returns all nodes with the supplied `type`.\n *\n * @category Legacy Query Functions\n * @param type Element type to look for.\n * @param nodes Nodes to search through.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes with the supplied `type`.\n */\nfunction getElementsByTagType(type, nodes, recurse, limit) {\n    if (recurse === void 0) { recurse = true; }\n    if (limit === void 0) { limit = Infinity; }\n    return (0, querying_js_1.filter)(Checks[\"tag_type\"](type), nodes, recurse, limit);\n}\n//# sourceMappingURL=legacy.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/domutils/lib/legacy.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/domutils/lib/manipulation.js":
/*!***************************************************!*\
  !*** ./node_modules/domutils/lib/manipulation.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.removeElement = removeElement;\nexports.replaceElement = replaceElement;\nexports.appendChild = appendChild;\nexports.append = append;\nexports.prependChild = prependChild;\nexports.prepend = prepend;\n/**\n * Remove an element from the dom\n *\n * @category Manipulation\n * @param elem The element to be removed\n */\nfunction removeElement(elem) {\n    if (elem.prev)\n        elem.prev.next = elem.next;\n    if (elem.next)\n        elem.next.prev = elem.prev;\n    if (elem.parent) {\n        var childs = elem.parent.children;\n        var childsIndex = childs.lastIndexOf(elem);\n        if (childsIndex >= 0) {\n            childs.splice(childsIndex, 1);\n        }\n    }\n    elem.next = null;\n    elem.prev = null;\n    elem.parent = null;\n}\n/**\n * Replace an element in the dom\n *\n * @category Manipulation\n * @param elem The element to be replaced\n * @param replacement The element to be added\n */\nfunction replaceElement(elem, replacement) {\n    var prev = (replacement.prev = elem.prev);\n    if (prev) {\n        prev.next = replacement;\n    }\n    var next = (replacement.next = elem.next);\n    if (next) {\n        next.prev = replacement;\n    }\n    var parent = (replacement.parent = elem.parent);\n    if (parent) {\n        var childs = parent.children;\n        childs[childs.lastIndexOf(elem)] = replacement;\n        elem.parent = null;\n    }\n}\n/**\n * Append a child to an element.\n *\n * @category Manipulation\n * @param parent The element to append to.\n * @param child The element to be added as a child.\n */\nfunction appendChild(parent, child) {\n    removeElement(child);\n    child.next = null;\n    child.parent = parent;\n    if (parent.children.push(child) > 1) {\n        var sibling = parent.children[parent.children.length - 2];\n        sibling.next = child;\n        child.prev = sibling;\n    }\n    else {\n        child.prev = null;\n    }\n}\n/**\n * Append an element after another.\n *\n * @category Manipulation\n * @param elem The element to append after.\n * @param next The element be added.\n */\nfunction append(elem, next) {\n    removeElement(next);\n    var parent = elem.parent;\n    var currNext = elem.next;\n    next.next = currNext;\n    next.prev = elem;\n    elem.next = next;\n    next.parent = parent;\n    if (currNext) {\n        currNext.prev = next;\n        if (parent) {\n            var childs = parent.children;\n            childs.splice(childs.lastIndexOf(currNext), 0, next);\n        }\n    }\n    else if (parent) {\n        parent.children.push(next);\n    }\n}\n/**\n * Prepend a child to an element.\n *\n * @category Manipulation\n * @param parent The element to prepend before.\n * @param child The element to be added as a child.\n */\nfunction prependChild(parent, child) {\n    removeElement(child);\n    child.parent = parent;\n    child.prev = null;\n    if (parent.children.unshift(child) !== 1) {\n        var sibling = parent.children[1];\n        sibling.prev = child;\n        child.next = sibling;\n    }\n    else {\n        child.next = null;\n    }\n}\n/**\n * Prepend an element before another.\n *\n * @category Manipulation\n * @param elem The element to prepend before.\n * @param prev The element be added.\n */\nfunction prepend(elem, prev) {\n    removeElement(prev);\n    var parent = elem.parent;\n    if (parent) {\n        var childs = parent.children;\n        childs.splice(childs.indexOf(elem), 0, prev);\n    }\n    if (elem.prev) {\n        elem.prev.next = prev;\n    }\n    prev.parent = parent;\n    prev.prev = elem.prev;\n    prev.next = elem;\n    elem.prev = prev;\n}\n//# sourceMappingURL=manipulation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/domutils/lib/manipulation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/domutils/lib/querying.js":
/*!***********************************************!*\
  !*** ./node_modules/domutils/lib/querying.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.filter = filter;\nexports.find = find;\nexports.findOneChild = findOneChild;\nexports.findOne = findOne;\nexports.existsOne = existsOne;\nexports.findAll = findAll;\nvar domhandler_1 = __webpack_require__(/*! domhandler */ \"(ssr)/./node_modules/domhandler/lib/index.js\");\n/**\n * Search a node and its children for nodes passing a test function. If `node` is not an array, it will be wrapped in one.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param node Node to search. Will be included in the result set if it matches.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes passing `test`.\n */\nfunction filter(test, node, recurse, limit) {\n    if (recurse === void 0) { recurse = true; }\n    if (limit === void 0) { limit = Infinity; }\n    return find(test, Array.isArray(node) ? node : [node], recurse, limit);\n}\n/**\n * Search an array of nodes and their children for nodes passing a test function.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param nodes Array of nodes to search.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes passing `test`.\n */\nfunction find(test, nodes, recurse, limit) {\n    var result = [];\n    /** Stack of the arrays we are looking at. */\n    var nodeStack = [Array.isArray(nodes) ? nodes : [nodes]];\n    /** Stack of the indices within the arrays. */\n    var indexStack = [0];\n    for (;;) {\n        // First, check if the current array has any more elements to look at.\n        if (indexStack[0] >= nodeStack[0].length) {\n            // If we have no more arrays to look at, we are done.\n            if (indexStack.length === 1) {\n                return result;\n            }\n            // Otherwise, remove the current array from the stack.\n            nodeStack.shift();\n            indexStack.shift();\n            // Loop back to the start to continue with the next array.\n            continue;\n        }\n        var elem = nodeStack[0][indexStack[0]++];\n        if (test(elem)) {\n            result.push(elem);\n            if (--limit <= 0)\n                return result;\n        }\n        if (recurse && (0, domhandler_1.hasChildren)(elem) && elem.children.length > 0) {\n            /*\n             * Add the children to the stack. We are depth-first, so this is\n             * the next array we look at.\n             */\n            indexStack.unshift(0);\n            nodeStack.unshift(elem.children);\n        }\n    }\n}\n/**\n * Finds the first element inside of an array that matches a test function. This is an alias for `Array.prototype.find`.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param nodes Array of nodes to search.\n * @returns The first node in the array that passes `test`.\n * @deprecated Use `Array.prototype.find` directly.\n */\nfunction findOneChild(test, nodes) {\n    return nodes.find(test);\n}\n/**\n * Finds one element in a tree that passes a test.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param nodes Node or array of nodes to search.\n * @param recurse Also consider child nodes.\n * @returns The first node that passes `test`.\n */\nfunction findOne(test, nodes, recurse) {\n    if (recurse === void 0) { recurse = true; }\n    var searchedNodes = Array.isArray(nodes) ? nodes : [nodes];\n    for (var i = 0; i < searchedNodes.length; i++) {\n        var node = searchedNodes[i];\n        if ((0, domhandler_1.isTag)(node) && test(node)) {\n            return node;\n        }\n        if (recurse && (0, domhandler_1.hasChildren)(node) && node.children.length > 0) {\n            var found = findOne(test, node.children, true);\n            if (found)\n                return found;\n        }\n    }\n    return null;\n}\n/**\n * Checks if a tree of nodes contains at least one node passing a test.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param nodes Array of nodes to search.\n * @returns Whether a tree of nodes contains at least one node passing the test.\n */\nfunction existsOne(test, nodes) {\n    return (Array.isArray(nodes) ? nodes : [nodes]).some(function (node) {\n        return ((0, domhandler_1.isTag)(node) && test(node)) ||\n            ((0, domhandler_1.hasChildren)(node) && existsOne(test, node.children));\n    });\n}\n/**\n * Search an array of nodes and their children for elements passing a test function.\n *\n * Same as `find`, but limited to elements and with less options, leading to reduced complexity.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param nodes Array of nodes to search.\n * @returns All nodes passing `test`.\n */\nfunction findAll(test, nodes) {\n    var result = [];\n    var nodeStack = [Array.isArray(nodes) ? nodes : [nodes]];\n    var indexStack = [0];\n    for (;;) {\n        if (indexStack[0] >= nodeStack[0].length) {\n            if (nodeStack.length === 1) {\n                return result;\n            }\n            // Otherwise, remove the current array from the stack.\n            nodeStack.shift();\n            indexStack.shift();\n            // Loop back to the start to continue with the next array.\n            continue;\n        }\n        var elem = nodeStack[0][indexStack[0]++];\n        if ((0, domhandler_1.isTag)(elem) && test(elem))\n            result.push(elem);\n        if ((0, domhandler_1.hasChildren)(elem) && elem.children.length > 0) {\n            indexStack.unshift(0);\n            nodeStack.unshift(elem.children);\n        }\n    }\n}\n//# sourceMappingURL=querying.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/domutils/lib/querying.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/domutils/lib/stringify.js":
/*!************************************************!*\
  !*** ./node_modules/domutils/lib/stringify.js ***!
  \************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getOuterHTML = getOuterHTML;\nexports.getInnerHTML = getInnerHTML;\nexports.getText = getText;\nexports.textContent = textContent;\nexports.innerText = innerText;\nvar domhandler_1 = __webpack_require__(/*! domhandler */ \"(ssr)/./node_modules/domhandler/lib/index.js\");\nvar dom_serializer_1 = __importDefault(__webpack_require__(/*! dom-serializer */ \"(ssr)/./node_modules/dom-serializer/lib/index.js\"));\nvar domelementtype_1 = __webpack_require__(/*! domelementtype */ \"(ssr)/./node_modules/domelementtype/lib/index.js\");\n/**\n * @category Stringify\n * @deprecated Use the `dom-serializer` module directly.\n * @param node Node to get the outer HTML of.\n * @param options Options for serialization.\n * @returns `node`'s outer HTML.\n */\nfunction getOuterHTML(node, options) {\n    return (0, dom_serializer_1.default)(node, options);\n}\n/**\n * @category Stringify\n * @deprecated Use the `dom-serializer` module directly.\n * @param node Node to get the inner HTML of.\n * @param options Options for serialization.\n * @returns `node`'s inner HTML.\n */\nfunction getInnerHTML(node, options) {\n    return (0, domhandler_1.hasChildren)(node)\n        ? node.children.map(function (node) { return getOuterHTML(node, options); }).join(\"\")\n        : \"\";\n}\n/**\n * Get a node's inner text. Same as `textContent`, but inserts newlines for `<br>` tags. Ignores comments.\n *\n * @category Stringify\n * @deprecated Use `textContent` instead.\n * @param node Node to get the inner text of.\n * @returns `node`'s inner text.\n */\nfunction getText(node) {\n    if (Array.isArray(node))\n        return node.map(getText).join(\"\");\n    if ((0, domhandler_1.isTag)(node))\n        return node.name === \"br\" ? \"\\n\" : getText(node.children);\n    if ((0, domhandler_1.isCDATA)(node))\n        return getText(node.children);\n    if ((0, domhandler_1.isText)(node))\n        return node.data;\n    return \"\";\n}\n/**\n * Get a node's text content. Ignores comments.\n *\n * @category Stringify\n * @param node Node to get the text content of.\n * @returns `node`'s text content.\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/Node/textContent}\n */\nfunction textContent(node) {\n    if (Array.isArray(node))\n        return node.map(textContent).join(\"\");\n    if ((0, domhandler_1.hasChildren)(node) && !(0, domhandler_1.isComment)(node)) {\n        return textContent(node.children);\n    }\n    if ((0, domhandler_1.isText)(node))\n        return node.data;\n    return \"\";\n}\n/**\n * Get a node's inner text, ignoring `<script>` and `<style>` tags. Ignores comments.\n *\n * @category Stringify\n * @param node Node to get the inner text of.\n * @returns `node`'s inner text.\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/Node/innerText}\n */\nfunction innerText(node) {\n    if (Array.isArray(node))\n        return node.map(innerText).join(\"\");\n    if ((0, domhandler_1.hasChildren)(node) && (node.type === domelementtype_1.ElementType.Tag || (0, domhandler_1.isCDATA)(node))) {\n        return innerText(node.children);\n    }\n    if ((0, domhandler_1.isText)(node))\n        return node.data;\n    return \"\";\n}\n//# sourceMappingURL=stringify.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG9tdXRpbHMvbGliL3N0cmluZ2lmeS5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0EsNkNBQTZDO0FBQzdDO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELG9CQUFvQjtBQUNwQixvQkFBb0I7QUFDcEIsZUFBZTtBQUNmLG1CQUFtQjtBQUNuQixpQkFBaUI7QUFDakIsbUJBQW1CLG1CQUFPLENBQUMsZ0VBQVk7QUFDdkMsdUNBQXVDLG1CQUFPLENBQUMsd0VBQWdCO0FBQy9ELHVCQUF1QixtQkFBTyxDQUFDLHdFQUFnQjtBQUMvQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE4QyxxQ0FBcUM7QUFDbkY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcQ29kZVxcb25lLW1haWxcXG5vZGVfbW9kdWxlc1xcZG9tdXRpbHNcXGxpYlxcc3RyaW5naWZ5LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xudmFyIF9faW1wb3J0RGVmYXVsdCA9ICh0aGlzICYmIHRoaXMuX19pbXBvcnREZWZhdWx0KSB8fCBmdW5jdGlvbiAobW9kKSB7XG4gICAgcmV0dXJuIChtb2QgJiYgbW9kLl9fZXNNb2R1bGUpID8gbW9kIDogeyBcImRlZmF1bHRcIjogbW9kIH07XG59O1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5nZXRPdXRlckhUTUwgPSBnZXRPdXRlckhUTUw7XG5leHBvcnRzLmdldElubmVySFRNTCA9IGdldElubmVySFRNTDtcbmV4cG9ydHMuZ2V0VGV4dCA9IGdldFRleHQ7XG5leHBvcnRzLnRleHRDb250ZW50ID0gdGV4dENvbnRlbnQ7XG5leHBvcnRzLmlubmVyVGV4dCA9IGlubmVyVGV4dDtcbnZhciBkb21oYW5kbGVyXzEgPSByZXF1aXJlKFwiZG9taGFuZGxlclwiKTtcbnZhciBkb21fc2VyaWFsaXplcl8xID0gX19pbXBvcnREZWZhdWx0KHJlcXVpcmUoXCJkb20tc2VyaWFsaXplclwiKSk7XG52YXIgZG9tZWxlbWVudHR5cGVfMSA9IHJlcXVpcmUoXCJkb21lbGVtZW50dHlwZVwiKTtcbi8qKlxuICogQGNhdGVnb3J5IFN0cmluZ2lmeVxuICogQGRlcHJlY2F0ZWQgVXNlIHRoZSBgZG9tLXNlcmlhbGl6ZXJgIG1vZHVsZSBkaXJlY3RseS5cbiAqIEBwYXJhbSBub2RlIE5vZGUgdG8gZ2V0IHRoZSBvdXRlciBIVE1MIG9mLlxuICogQHBhcmFtIG9wdGlvbnMgT3B0aW9ucyBmb3Igc2VyaWFsaXphdGlvbi5cbiAqIEByZXR1cm5zIGBub2RlYCdzIG91dGVyIEhUTUwuXG4gKi9cbmZ1bmN0aW9uIGdldE91dGVySFRNTChub2RlLCBvcHRpb25zKSB7XG4gICAgcmV0dXJuICgwLCBkb21fc2VyaWFsaXplcl8xLmRlZmF1bHQpKG5vZGUsIG9wdGlvbnMpO1xufVxuLyoqXG4gKiBAY2F0ZWdvcnkgU3RyaW5naWZ5XG4gKiBAZGVwcmVjYXRlZCBVc2UgdGhlIGBkb20tc2VyaWFsaXplcmAgbW9kdWxlIGRpcmVjdGx5LlxuICogQHBhcmFtIG5vZGUgTm9kZSB0byBnZXQgdGhlIGlubmVyIEhUTUwgb2YuXG4gKiBAcGFyYW0gb3B0aW9ucyBPcHRpb25zIGZvciBzZXJpYWxpemF0aW9uLlxuICogQHJldHVybnMgYG5vZGVgJ3MgaW5uZXIgSFRNTC5cbiAqL1xuZnVuY3Rpb24gZ2V0SW5uZXJIVE1MKG5vZGUsIG9wdGlvbnMpIHtcbiAgICByZXR1cm4gKDAsIGRvbWhhbmRsZXJfMS5oYXNDaGlsZHJlbikobm9kZSlcbiAgICAgICAgPyBub2RlLmNoaWxkcmVuLm1hcChmdW5jdGlvbiAobm9kZSkgeyByZXR1cm4gZ2V0T3V0ZXJIVE1MKG5vZGUsIG9wdGlvbnMpOyB9KS5qb2luKFwiXCIpXG4gICAgICAgIDogXCJcIjtcbn1cbi8qKlxuICogR2V0IGEgbm9kZSdzIGlubmVyIHRleHQuIFNhbWUgYXMgYHRleHRDb250ZW50YCwgYnV0IGluc2VydHMgbmV3bGluZXMgZm9yIGA8YnI+YCB0YWdzLiBJZ25vcmVzIGNvbW1lbnRzLlxuICpcbiAqIEBjYXRlZ29yeSBTdHJpbmdpZnlcbiAqIEBkZXByZWNhdGVkIFVzZSBgdGV4dENvbnRlbnRgIGluc3RlYWQuXG4gKiBAcGFyYW0gbm9kZSBOb2RlIHRvIGdldCB0aGUgaW5uZXIgdGV4dCBvZi5cbiAqIEByZXR1cm5zIGBub2RlYCdzIGlubmVyIHRleHQuXG4gKi9cbmZ1bmN0aW9uIGdldFRleHQobm9kZSkge1xuICAgIGlmIChBcnJheS5pc0FycmF5KG5vZGUpKVxuICAgICAgICByZXR1cm4gbm9kZS5tYXAoZ2V0VGV4dCkuam9pbihcIlwiKTtcbiAgICBpZiAoKDAsIGRvbWhhbmRsZXJfMS5pc1RhZykobm9kZSkpXG4gICAgICAgIHJldHVybiBub2RlLm5hbWUgPT09IFwiYnJcIiA/IFwiXFxuXCIgOiBnZXRUZXh0KG5vZGUuY2hpbGRyZW4pO1xuICAgIGlmICgoMCwgZG9taGFuZGxlcl8xLmlzQ0RBVEEpKG5vZGUpKVxuICAgICAgICByZXR1cm4gZ2V0VGV4dChub2RlLmNoaWxkcmVuKTtcbiAgICBpZiAoKDAsIGRvbWhhbmRsZXJfMS5pc1RleHQpKG5vZGUpKVxuICAgICAgICByZXR1cm4gbm9kZS5kYXRhO1xuICAgIHJldHVybiBcIlwiO1xufVxuLyoqXG4gKiBHZXQgYSBub2RlJ3MgdGV4dCBjb250ZW50LiBJZ25vcmVzIGNvbW1lbnRzLlxuICpcbiAqIEBjYXRlZ29yeSBTdHJpbmdpZnlcbiAqIEBwYXJhbSBub2RlIE5vZGUgdG8gZ2V0IHRoZSB0ZXh0IGNvbnRlbnQgb2YuXG4gKiBAcmV0dXJucyBgbm9kZWAncyB0ZXh0IGNvbnRlbnQuXG4gKiBAc2VlIHtAbGluayBodHRwczovL2RldmVsb3Blci5tb3ppbGxhLm9yZy9lbi1VUy9kb2NzL1dlYi9BUEkvTm9kZS90ZXh0Q29udGVudH1cbiAqL1xuZnVuY3Rpb24gdGV4dENvbnRlbnQobm9kZSkge1xuICAgIGlmIChBcnJheS5pc0FycmF5KG5vZGUpKVxuICAgICAgICByZXR1cm4gbm9kZS5tYXAodGV4dENvbnRlbnQpLmpvaW4oXCJcIik7XG4gICAgaWYgKCgwLCBkb21oYW5kbGVyXzEuaGFzQ2hpbGRyZW4pKG5vZGUpICYmICEoMCwgZG9taGFuZGxlcl8xLmlzQ29tbWVudCkobm9kZSkpIHtcbiAgICAgICAgcmV0dXJuIHRleHRDb250ZW50KG5vZGUuY2hpbGRyZW4pO1xuICAgIH1cbiAgICBpZiAoKDAsIGRvbWhhbmRsZXJfMS5pc1RleHQpKG5vZGUpKVxuICAgICAgICByZXR1cm4gbm9kZS5kYXRhO1xuICAgIHJldHVybiBcIlwiO1xufVxuLyoqXG4gKiBHZXQgYSBub2RlJ3MgaW5uZXIgdGV4dCwgaWdub3JpbmcgYDxzY3JpcHQ+YCBhbmQgYDxzdHlsZT5gIHRhZ3MuIElnbm9yZXMgY29tbWVudHMuXG4gKlxuICogQGNhdGVnb3J5IFN0cmluZ2lmeVxuICogQHBhcmFtIG5vZGUgTm9kZSB0byBnZXQgdGhlIGlubmVyIHRleHQgb2YuXG4gKiBAcmV0dXJucyBgbm9kZWAncyBpbm5lciB0ZXh0LlxuICogQHNlZSB7QGxpbmsgaHR0cHM6Ly9kZXZlbG9wZXIubW96aWxsYS5vcmcvZW4tVVMvZG9jcy9XZWIvQVBJL05vZGUvaW5uZXJUZXh0fVxuICovXG5mdW5jdGlvbiBpbm5lclRleHQobm9kZSkge1xuICAgIGlmIChBcnJheS5pc0FycmF5KG5vZGUpKVxuICAgICAgICByZXR1cm4gbm9kZS5tYXAoaW5uZXJUZXh0KS5qb2luKFwiXCIpO1xuICAgIGlmICgoMCwgZG9taGFuZGxlcl8xLmhhc0NoaWxkcmVuKShub2RlKSAmJiAobm9kZS50eXBlID09PSBkb21lbGVtZW50dHlwZV8xLkVsZW1lbnRUeXBlLlRhZyB8fCAoMCwgZG9taGFuZGxlcl8xLmlzQ0RBVEEpKG5vZGUpKSkge1xuICAgICAgICByZXR1cm4gaW5uZXJUZXh0KG5vZGUuY2hpbGRyZW4pO1xuICAgIH1cbiAgICBpZiAoKDAsIGRvbWhhbmRsZXJfMS5pc1RleHQpKG5vZGUpKVxuICAgICAgICByZXR1cm4gbm9kZS5kYXRhO1xuICAgIHJldHVybiBcIlwiO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c3RyaW5naWZ5LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/domutils/lib/stringify.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/domutils/lib/traversal.js":
/*!************************************************!*\
  !*** ./node_modules/domutils/lib/traversal.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getChildren = getChildren;\nexports.getParent = getParent;\nexports.getSiblings = getSiblings;\nexports.getAttributeValue = getAttributeValue;\nexports.hasAttrib = hasAttrib;\nexports.getName = getName;\nexports.nextElementSibling = nextElementSibling;\nexports.prevElementSibling = prevElementSibling;\nvar domhandler_1 = __webpack_require__(/*! domhandler */ \"(ssr)/./node_modules/domhandler/lib/index.js\");\n/**\n * Get a node's children.\n *\n * @category Traversal\n * @param elem Node to get the children of.\n * @returns `elem`'s children, or an empty array.\n */\nfunction getChildren(elem) {\n    return (0, domhandler_1.hasChildren)(elem) ? elem.children : [];\n}\n/**\n * Get a node's parent.\n *\n * @category Traversal\n * @param elem Node to get the parent of.\n * @returns `elem`'s parent node, or `null` if `elem` is a root node.\n */\nfunction getParent(elem) {\n    return elem.parent || null;\n}\n/**\n * Gets an elements siblings, including the element itself.\n *\n * Attempts to get the children through the element's parent first. If we don't\n * have a parent (the element is a root node), we walk the element's `prev` &\n * `next` to get all remaining nodes.\n *\n * @category Traversal\n * @param elem Element to get the siblings of.\n * @returns `elem`'s siblings, including `elem`.\n */\nfunction getSiblings(elem) {\n    var _a, _b;\n    var parent = getParent(elem);\n    if (parent != null)\n        return getChildren(parent);\n    var siblings = [elem];\n    var prev = elem.prev, next = elem.next;\n    while (prev != null) {\n        siblings.unshift(prev);\n        (_a = prev, prev = _a.prev);\n    }\n    while (next != null) {\n        siblings.push(next);\n        (_b = next, next = _b.next);\n    }\n    return siblings;\n}\n/**\n * Gets an attribute from an element.\n *\n * @category Traversal\n * @param elem Element to check.\n * @param name Attribute name to retrieve.\n * @returns The element's attribute value, or `undefined`.\n */\nfunction getAttributeValue(elem, name) {\n    var _a;\n    return (_a = elem.attribs) === null || _a === void 0 ? void 0 : _a[name];\n}\n/**\n * Checks whether an element has an attribute.\n *\n * @category Traversal\n * @param elem Element to check.\n * @param name Attribute name to look for.\n * @returns Returns whether `elem` has the attribute `name`.\n */\nfunction hasAttrib(elem, name) {\n    return (elem.attribs != null &&\n        Object.prototype.hasOwnProperty.call(elem.attribs, name) &&\n        elem.attribs[name] != null);\n}\n/**\n * Get the tag name of an element.\n *\n * @category Traversal\n * @param elem The element to get the name for.\n * @returns The tag name of `elem`.\n */\nfunction getName(elem) {\n    return elem.name;\n}\n/**\n * Returns the next element sibling of a node.\n *\n * @category Traversal\n * @param elem The element to get the next sibling of.\n * @returns `elem`'s next sibling that is a tag, or `null` if there is no next\n * sibling.\n */\nfunction nextElementSibling(elem) {\n    var _a;\n    var next = elem.next;\n    while (next !== null && !(0, domhandler_1.isTag)(next))\n        (_a = next, next = _a.next);\n    return next;\n}\n/**\n * Returns the previous element sibling of a node.\n *\n * @category Traversal\n * @param elem The element to get the previous sibling of.\n * @returns `elem`'s previous sibling that is a tag, or `null` if there is no\n * previous sibling.\n */\nfunction prevElementSibling(elem) {\n    var _a;\n    var prev = elem.prev;\n    while (prev !== null && !(0, domhandler_1.isTag)(prev))\n        (_a = prev, prev = _a.prev);\n    return prev;\n}\n//# sourceMappingURL=traversal.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/domutils/lib/traversal.js\n");

/***/ })

};
;