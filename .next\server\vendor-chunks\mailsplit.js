"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mailsplit";
exports.ids = ["vendor-chunks/mailsplit"];
exports.modules = {

/***/ "(rsc)/./node_modules/mailsplit/index.js":
/*!*****************************************!*\
  !*** ./node_modules/mailsplit/index.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst MessageSplitter = __webpack_require__(/*! ./lib/message-splitter */ \"(rsc)/./node_modules/mailsplit/lib/message-splitter.js\");\nconst MessageJoiner = __webpack_require__(/*! ./lib/message-joiner */ \"(rsc)/./node_modules/mailsplit/lib/message-joiner.js\");\nconst NodeRewriter = __webpack_require__(/*! ./lib/node-rewriter */ \"(rsc)/./node_modules/mailsplit/lib/node-rewriter.js\");\nconst NodeStreamer = __webpack_require__(/*! ./lib/node-streamer */ \"(rsc)/./node_modules/mailsplit/lib/node-streamer.js\");\nconst Headers = __webpack_require__(/*! ./lib/headers */ \"(rsc)/./node_modules/mailsplit/lib/headers.js\");\nconst ChunkedPassthrough = __webpack_require__(/*! ./lib/chunked-passthrough */ \"(rsc)/./node_modules/mailsplit/lib/chunked-passthrough.js\");\n\nmodule.exports = {\n    Splitter: MessageSplitter,\n    Joiner: MessageJoiner,\n    Rewriter: NodeRewriter,\n    Streamer: NodeStreamer,\n    ChunkedPassthrough,\n    Headers\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWFpbHNwbGl0L2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLHdCQUF3QixtQkFBTyxDQUFDLHNGQUF3QjtBQUN4RCxzQkFBc0IsbUJBQU8sQ0FBQyxrRkFBc0I7QUFDcEQscUJBQXFCLG1CQUFPLENBQUMsZ0ZBQXFCO0FBQ2xELHFCQUFxQixtQkFBTyxDQUFDLGdGQUFxQjtBQUNsRCxnQkFBZ0IsbUJBQU8sQ0FBQyxvRUFBZTtBQUN2QywyQkFBMkIsbUJBQU8sQ0FBQyw0RkFBMkI7O0FBRTlEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxDb2RlXFxvbmUtbWFpbFxcbm9kZV9tb2R1bGVzXFxtYWlsc3BsaXRcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuY29uc3QgTWVzc2FnZVNwbGl0dGVyID0gcmVxdWlyZSgnLi9saWIvbWVzc2FnZS1zcGxpdHRlcicpO1xuY29uc3QgTWVzc2FnZUpvaW5lciA9IHJlcXVpcmUoJy4vbGliL21lc3NhZ2Utam9pbmVyJyk7XG5jb25zdCBOb2RlUmV3cml0ZXIgPSByZXF1aXJlKCcuL2xpYi9ub2RlLXJld3JpdGVyJyk7XG5jb25zdCBOb2RlU3RyZWFtZXIgPSByZXF1aXJlKCcuL2xpYi9ub2RlLXN0cmVhbWVyJyk7XG5jb25zdCBIZWFkZXJzID0gcmVxdWlyZSgnLi9saWIvaGVhZGVycycpO1xuY29uc3QgQ2h1bmtlZFBhc3N0aHJvdWdoID0gcmVxdWlyZSgnLi9saWIvY2h1bmtlZC1wYXNzdGhyb3VnaCcpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IHtcbiAgICBTcGxpdHRlcjogTWVzc2FnZVNwbGl0dGVyLFxuICAgIEpvaW5lcjogTWVzc2FnZUpvaW5lcixcbiAgICBSZXdyaXRlcjogTm9kZVJld3JpdGVyLFxuICAgIFN0cmVhbWVyOiBOb2RlU3RyZWFtZXIsXG4gICAgQ2h1bmtlZFBhc3N0aHJvdWdoLFxuICAgIEhlYWRlcnNcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mailsplit/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mailsplit/lib/chunked-passthrough.js":
/*!***********************************************************!*\
  !*** ./node_modules/mailsplit/lib/chunked-passthrough.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst { Transform } = __webpack_require__(/*! stream */ \"stream\");\n\nclass ChunkedPassthrough extends Transform {\n    constructor(options = {}) {\n        let config = {\n            readableObjectMode: true,\n            writableObjectMode: false\n        };\n        super(config);\n        this.chunkSize = options.chunkSize || 64 * 1024; // 64KB default\n        this.buffer = Buffer.alloc(0);\n    }\n\n    _transform(chunk, encoding, callback) {\n        this.buffer = Buffer.concat([this.buffer, chunk]);\n\n        if (this.buffer.length >= this.chunkSize) {\n            this.push(this.buffer);\n            this.buffer = Buffer.alloc(0);\n        }\n\n        callback();\n    }\n\n    _flush(callback) {\n        // Send remaining data\n        if (this.buffer.length > 0) {\n            this.push(this.buffer);\n            this.buffer = Buffer.alloc(0);\n        }\n        callback();\n    }\n}\n\nmodule.exports = ChunkedPassthrough;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWFpbHNwbGl0L2xpYi9jaHVua2VkLXBhc3N0aHJvdWdoLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLFFBQVEsWUFBWSxFQUFFLG1CQUFPLENBQUMsc0JBQVE7O0FBRXRDO0FBQ0EsNEJBQTRCO0FBQzVCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5REFBeUQ7QUFDekQ7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJDOlxcQ29kZVxcb25lLW1haWxcXG5vZGVfbW9kdWxlc1xcbWFpbHNwbGl0XFxsaWJcXGNodW5rZWQtcGFzc3Rocm91Z2guanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5jb25zdCB7IFRyYW5zZm9ybSB9ID0gcmVxdWlyZSgnc3RyZWFtJyk7XG5cbmNsYXNzIENodW5rZWRQYXNzdGhyb3VnaCBleHRlbmRzIFRyYW5zZm9ybSB7XG4gICAgY29uc3RydWN0b3Iob3B0aW9ucyA9IHt9KSB7XG4gICAgICAgIGxldCBjb25maWcgPSB7XG4gICAgICAgICAgICByZWFkYWJsZU9iamVjdE1vZGU6IHRydWUsXG4gICAgICAgICAgICB3cml0YWJsZU9iamVjdE1vZGU6IGZhbHNlXG4gICAgICAgIH07XG4gICAgICAgIHN1cGVyKGNvbmZpZyk7XG4gICAgICAgIHRoaXMuY2h1bmtTaXplID0gb3B0aW9ucy5jaHVua1NpemUgfHwgNjQgKiAxMDI0OyAvLyA2NEtCIGRlZmF1bHRcbiAgICAgICAgdGhpcy5idWZmZXIgPSBCdWZmZXIuYWxsb2MoMCk7XG4gICAgfVxuXG4gICAgX3RyYW5zZm9ybShjaHVuaywgZW5jb2RpbmcsIGNhbGxiYWNrKSB7XG4gICAgICAgIHRoaXMuYnVmZmVyID0gQnVmZmVyLmNvbmNhdChbdGhpcy5idWZmZXIsIGNodW5rXSk7XG5cbiAgICAgICAgaWYgKHRoaXMuYnVmZmVyLmxlbmd0aCA+PSB0aGlzLmNodW5rU2l6ZSkge1xuICAgICAgICAgICAgdGhpcy5wdXNoKHRoaXMuYnVmZmVyKTtcbiAgICAgICAgICAgIHRoaXMuYnVmZmVyID0gQnVmZmVyLmFsbG9jKDApO1xuICAgICAgICB9XG5cbiAgICAgICAgY2FsbGJhY2soKTtcbiAgICB9XG5cbiAgICBfZmx1c2goY2FsbGJhY2spIHtcbiAgICAgICAgLy8gU2VuZCByZW1haW5pbmcgZGF0YVxuICAgICAgICBpZiAodGhpcy5idWZmZXIubGVuZ3RoID4gMCkge1xuICAgICAgICAgICAgdGhpcy5wdXNoKHRoaXMuYnVmZmVyKTtcbiAgICAgICAgICAgIHRoaXMuYnVmZmVyID0gQnVmZmVyLmFsbG9jKDApO1xuICAgICAgICB9XG4gICAgICAgIGNhbGxiYWNrKCk7XG4gICAgfVxufVxuXG5tb2R1bGUuZXhwb3J0cyA9IENodW5rZWRQYXNzdGhyb3VnaDtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mailsplit/lib/chunked-passthrough.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mailsplit/lib/flowed-decoder.js":
/*!******************************************************!*\
  !*** ./node_modules/mailsplit/lib/flowed-decoder.js ***!
  \******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n// Helper class to rewrite nodes with specific mime type\n\nconst Transform = (__webpack_require__(/*! stream */ \"stream\").Transform);\nconst libmime = __webpack_require__(/*! libmime */ \"(rsc)/./node_modules/libmime/lib/libmime.js\");\n\n/**\n * Really bad \"stream\" transform to parse format=flowed content\n *\n * @constructor\n * @param {String} delSp True if delsp option was used\n */\nclass FlowedDecoder extends Transform {\n    constructor(config) {\n        super();\n        this.config = config || {};\n\n        this.chunks = [];\n        this.chunklen = 0;\n\n        this.libmime = new libmime.Libmime({ Iconv: config.Iconv });\n    }\n\n    _transform(chunk, encoding, callback) {\n        if (!chunk || !chunk.length) {\n            return callback();\n        }\n\n        if (!encoding !== 'buffer') {\n            chunk = Buffer.from(chunk, encoding);\n        }\n\n        this.chunks.push(chunk);\n        this.chunklen += chunk.length;\n\n        callback();\n    }\n\n    _flush(callback) {\n        if (this.chunklen) {\n            let currentBody = Buffer.concat(this.chunks, this.chunklen);\n\n            if (this.config.encoding === 'base64') {\n                currentBody = Buffer.from(currentBody.toString('binary'), 'base64');\n            }\n\n            let content = this.libmime.decodeFlowed(currentBody.toString('binary'), this.config.delSp);\n            this.push(Buffer.from(content, 'binary'));\n        }\n        return callback();\n    }\n}\n\nmodule.exports = FlowedDecoder;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWFpbHNwbGl0L2xpYi9mbG93ZWQtZGVjb2Rlci5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjs7QUFFQSxrQkFBa0IsdURBQTJCO0FBQzdDLGdCQUFnQixtQkFBTyxDQUFDLDREQUFTOztBQUVqQztBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUEsNkNBQTZDLHFCQUFxQjtBQUNsRTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJDOlxcQ29kZVxcb25lLW1haWxcXG5vZGVfbW9kdWxlc1xcbWFpbHNwbGl0XFxsaWJcXGZsb3dlZC1kZWNvZGVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuLy8gSGVscGVyIGNsYXNzIHRvIHJld3JpdGUgbm9kZXMgd2l0aCBzcGVjaWZpYyBtaW1lIHR5cGVcblxuY29uc3QgVHJhbnNmb3JtID0gcmVxdWlyZSgnc3RyZWFtJykuVHJhbnNmb3JtO1xuY29uc3QgbGlibWltZSA9IHJlcXVpcmUoJ2xpYm1pbWUnKTtcblxuLyoqXG4gKiBSZWFsbHkgYmFkIFwic3RyZWFtXCIgdHJhbnNmb3JtIHRvIHBhcnNlIGZvcm1hdD1mbG93ZWQgY29udGVudFxuICpcbiAqIEBjb25zdHJ1Y3RvclxuICogQHBhcmFtIHtTdHJpbmd9IGRlbFNwIFRydWUgaWYgZGVsc3Agb3B0aW9uIHdhcyB1c2VkXG4gKi9cbmNsYXNzIEZsb3dlZERlY29kZXIgZXh0ZW5kcyBUcmFuc2Zvcm0ge1xuICAgIGNvbnN0cnVjdG9yKGNvbmZpZykge1xuICAgICAgICBzdXBlcigpO1xuICAgICAgICB0aGlzLmNvbmZpZyA9IGNvbmZpZyB8fCB7fTtcblxuICAgICAgICB0aGlzLmNodW5rcyA9IFtdO1xuICAgICAgICB0aGlzLmNodW5rbGVuID0gMDtcblxuICAgICAgICB0aGlzLmxpYm1pbWUgPSBuZXcgbGlibWltZS5MaWJtaW1lKHsgSWNvbnY6IGNvbmZpZy5JY29udiB9KTtcbiAgICB9XG5cbiAgICBfdHJhbnNmb3JtKGNodW5rLCBlbmNvZGluZywgY2FsbGJhY2spIHtcbiAgICAgICAgaWYgKCFjaHVuayB8fCAhY2h1bmsubGVuZ3RoKSB7XG4gICAgICAgICAgICByZXR1cm4gY2FsbGJhY2soKTtcbiAgICAgICAgfVxuXG4gICAgICAgIGlmICghZW5jb2RpbmcgIT09ICdidWZmZXInKSB7XG4gICAgICAgICAgICBjaHVuayA9IEJ1ZmZlci5mcm9tKGNodW5rLCBlbmNvZGluZyk7XG4gICAgICAgIH1cblxuICAgICAgICB0aGlzLmNodW5rcy5wdXNoKGNodW5rKTtcbiAgICAgICAgdGhpcy5jaHVua2xlbiArPSBjaHVuay5sZW5ndGg7XG5cbiAgICAgICAgY2FsbGJhY2soKTtcbiAgICB9XG5cbiAgICBfZmx1c2goY2FsbGJhY2spIHtcbiAgICAgICAgaWYgKHRoaXMuY2h1bmtsZW4pIHtcbiAgICAgICAgICAgIGxldCBjdXJyZW50Qm9keSA9IEJ1ZmZlci5jb25jYXQodGhpcy5jaHVua3MsIHRoaXMuY2h1bmtsZW4pO1xuXG4gICAgICAgICAgICBpZiAodGhpcy5jb25maWcuZW5jb2RpbmcgPT09ICdiYXNlNjQnKSB7XG4gICAgICAgICAgICAgICAgY3VycmVudEJvZHkgPSBCdWZmZXIuZnJvbShjdXJyZW50Qm9keS50b1N0cmluZygnYmluYXJ5JyksICdiYXNlNjQnKTtcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgbGV0IGNvbnRlbnQgPSB0aGlzLmxpYm1pbWUuZGVjb2RlRmxvd2VkKGN1cnJlbnRCb2R5LnRvU3RyaW5nKCdiaW5hcnknKSwgdGhpcy5jb25maWcuZGVsU3ApO1xuICAgICAgICAgICAgdGhpcy5wdXNoKEJ1ZmZlci5mcm9tKGNvbnRlbnQsICdiaW5hcnknKSk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGNhbGxiYWNrKCk7XG4gICAgfVxufVxuXG5tb2R1bGUuZXhwb3J0cyA9IEZsb3dlZERlY29kZXI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mailsplit/lib/flowed-decoder.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mailsplit/lib/headers.js":
/*!***********************************************!*\
  !*** ./node_modules/mailsplit/lib/headers.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst libmime = __webpack_require__(/*! libmime */ \"(rsc)/./node_modules/libmime/lib/libmime.js\");\n\n/**\n * Class Headers to parse and handle message headers. Headers instance allows to\n * check existing, delete or add new headers\n */\nclass Headers {\n    constructor(headers, config) {\n        config = config || {};\n\n        if (Array.isArray(headers)) {\n            // already using parsed headers\n            this.changed = true;\n            this.headers = false;\n            this.parsed = true;\n            this.lines = headers;\n        } else {\n            // using original string/buffer headers\n            this.changed = false;\n            this.headers = headers;\n            this.parsed = false;\n            this.lines = false;\n        }\n        this.mbox = false;\n        this.http = false;\n\n        this.libmime = new libmime.Libmime({ Iconv: config.Iconv });\n    }\n\n    hasHeader(key) {\n        if (!this.parsed) {\n            this._parseHeaders();\n        }\n        key = this._normalizeHeader(key);\n        return typeof this.lines.find(line => line.key === key) === 'object';\n    }\n\n    get(key) {\n        if (!this.parsed) {\n            this._parseHeaders();\n        }\n        key = this._normalizeHeader(key);\n        let lines = this.lines.filter(line => line.key === key).map(line => line.line);\n\n        return lines;\n    }\n\n    getDecoded(key) {\n        return this.get(key)\n            .map(line => this.libmime.decodeHeader(line))\n            .filter(line => line && line.value);\n    }\n\n    getFirst(key) {\n        if (!this.parsed) {\n            this._parseHeaders();\n        }\n        key = this._normalizeHeader(key);\n        let header = this.lines.find(line => line.key === key);\n        if (!header) {\n            return '';\n        }\n        return ((this.libmime.decodeHeader(header.line) || {}).value || '').toString().trim();\n    }\n\n    getList() {\n        if (!this.parsed) {\n            this._parseHeaders();\n        }\n        return this.lines;\n    }\n\n    add(key, value, index) {\n        if (typeof value === 'undefined') {\n            return;\n        }\n\n        if (typeof value === 'number') {\n            value = value.toString();\n        }\n\n        if (typeof value === 'string') {\n            value = Buffer.from(value);\n        }\n\n        value = value.toString('binary');\n        this.addFormatted(key, this.libmime.foldLines(key + ': ' + value.replace(/\\r?\\n/g, ''), 76, false), index);\n    }\n\n    addFormatted(key, line, index) {\n        if (!this.parsed) {\n            this._parseHeaders();\n        }\n        index = index || 0;\n        this.changed = true;\n\n        if (!line) {\n            return;\n        }\n\n        if (typeof line !== 'string') {\n            line = line.toString('binary');\n        }\n\n        let header = {\n            key: this._normalizeHeader(key),\n            line\n        };\n\n        if (index < 1) {\n            this.lines.unshift(header);\n        } else if (index >= this.lines.length) {\n            this.lines.push(header);\n        } else {\n            this.lines.splice(index, 0, header);\n        }\n    }\n\n    remove(key) {\n        if (!this.parsed) {\n            this._parseHeaders();\n        }\n        key = this._normalizeHeader(key);\n        for (let i = this.lines.length - 1; i >= 0; i--) {\n            if (this.lines[i].key === key) {\n                this.changed = true;\n                this.lines.splice(i, 1);\n            }\n        }\n    }\n\n    update(key, value, relativeIndex) {\n        if (!this.parsed) {\n            this._parseHeaders();\n        }\n        let keyName = key;\n        let index = 0;\n        key = this._normalizeHeader(key);\n        let relativeIndexCount = 0;\n        let relativeMatchFound = false;\n        for (let i = this.lines.length - 1; i >= 0; i--) {\n            if (this.lines[i].key === key) {\n                if (relativeIndex && relativeIndex !== relativeIndexCount) {\n                    relativeIndexCount++;\n                    continue;\n                }\n                index = i;\n                this.changed = true;\n                this.lines.splice(i, 1);\n                if (relativeIndex) {\n                    relativeMatchFound = true;\n                    break;\n                }\n            }\n        }\n\n        if (relativeIndex && !relativeMatchFound) {\n            return;\n        }\n\n        this.add(keyName, value, index);\n    }\n\n    build(lineEnd) {\n        if (!this.changed && !lineEnd) {\n            return typeof this.headers === 'string' ? Buffer.from(this.headers, 'binary') : this.headers;\n        }\n\n        if (!this.parsed) {\n            this._parseHeaders();\n        }\n\n        lineEnd = lineEnd || '\\r\\n';\n\n        let headers = this.lines.map(line => line.line.replace(/\\r?\\n/g, lineEnd)).join(lineEnd) + `${lineEnd}${lineEnd}`;\n\n        if (this.mbox) {\n            headers = this.mbox + lineEnd + headers;\n        }\n\n        if (this.http) {\n            headers = this.http + lineEnd + headers;\n        }\n\n        return Buffer.from(headers, 'binary');\n    }\n\n    _normalizeHeader(key) {\n        return (key || '').toLowerCase().trim();\n    }\n\n    _parseHeaders() {\n        if (!this.headers) {\n            this.lines = [];\n            this.parsed = true;\n            return;\n        }\n\n        let lines = this.headers\n            .toString('binary')\n            .replace(/[\\r\\n]+$/, '')\n            .split(/\\r?\\n/);\n\n        for (let i = lines.length - 1; i >= 0; i--) {\n            let chr = lines[i].charAt(0);\n            if (i && (chr === ' ' || chr === '\\t')) {\n                lines[i - 1] += '\\r\\n' + lines[i];\n                lines.splice(i, 1);\n            } else {\n                let line = lines[i];\n                if (!i && /^From /i.test(line)) {\n                    // mbox file\n                    this.mbox = line;\n                    lines.splice(i, 1);\n                    continue;\n                } else if (!i && /^POST /i.test(line)) {\n                    // HTTP POST request\n                    this.http = line;\n                    lines.splice(i, 1);\n                    continue;\n                }\n                let key = this._normalizeHeader(line.substr(0, line.indexOf(':')));\n                lines[i] = {\n                    key,\n                    line\n                };\n            }\n        }\n\n        this.lines = lines;\n        this.parsed = true;\n    }\n}\n\n// expose to the world\nmodule.exports = Headers;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mailsplit/lib/headers.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mailsplit/lib/message-joiner.js":
/*!******************************************************!*\
  !*** ./node_modules/mailsplit/lib/message-joiner.js ***!
  \******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst Transform = (__webpack_require__(/*! stream */ \"stream\").Transform);\n\nclass MessageJoiner extends Transform {\n    constructor() {\n        let options = {\n            readableObjectMode: false,\n            writableObjectMode: true\n        };\n        super(options);\n    }\n\n    _transform(obj, encoding, callback) {\n        if (Buffer.isBuffer(obj)) {\n            this.push(obj);\n        } else if (obj.type === 'node') {\n            this.push(obj.getHeaders());\n        } else if (obj.value) {\n            this.push(obj.value);\n        }\n        return callback();\n    }\n\n    _flush(callback) {\n        return callback();\n    }\n}\n\nmodule.exports = MessageJoiner;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWFpbHNwbGl0L2xpYi9tZXNzYWdlLWpvaW5lci5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYixrQkFBa0IsdURBQTJCOztBQUU3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsiQzpcXENvZGVcXG9uZS1tYWlsXFxub2RlX21vZHVsZXNcXG1haWxzcGxpdFxcbGliXFxtZXNzYWdlLWpvaW5lci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmNvbnN0IFRyYW5zZm9ybSA9IHJlcXVpcmUoJ3N0cmVhbScpLlRyYW5zZm9ybTtcblxuY2xhc3MgTWVzc2FnZUpvaW5lciBleHRlbmRzIFRyYW5zZm9ybSB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIGxldCBvcHRpb25zID0ge1xuICAgICAgICAgICAgcmVhZGFibGVPYmplY3RNb2RlOiBmYWxzZSxcbiAgICAgICAgICAgIHdyaXRhYmxlT2JqZWN0TW9kZTogdHJ1ZVxuICAgICAgICB9O1xuICAgICAgICBzdXBlcihvcHRpb25zKTtcbiAgICB9XG5cbiAgICBfdHJhbnNmb3JtKG9iaiwgZW5jb2RpbmcsIGNhbGxiYWNrKSB7XG4gICAgICAgIGlmIChCdWZmZXIuaXNCdWZmZXIob2JqKSkge1xuICAgICAgICAgICAgdGhpcy5wdXNoKG9iaik7XG4gICAgICAgIH0gZWxzZSBpZiAob2JqLnR5cGUgPT09ICdub2RlJykge1xuICAgICAgICAgICAgdGhpcy5wdXNoKG9iai5nZXRIZWFkZXJzKCkpO1xuICAgICAgICB9IGVsc2UgaWYgKG9iai52YWx1ZSkge1xuICAgICAgICAgICAgdGhpcy5wdXNoKG9iai52YWx1ZSk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGNhbGxiYWNrKCk7XG4gICAgfVxuXG4gICAgX2ZsdXNoKGNhbGxiYWNrKSB7XG4gICAgICAgIHJldHVybiBjYWxsYmFjaygpO1xuICAgIH1cbn1cblxubW9kdWxlLmV4cG9ydHMgPSBNZXNzYWdlSm9pbmVyO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mailsplit/lib/message-joiner.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mailsplit/lib/message-splitter.js":
/*!********************************************************!*\
  !*** ./node_modules/mailsplit/lib/message-splitter.js ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst Transform = (__webpack_require__(/*! stream */ \"stream\").Transform);\nconst MimeNode = __webpack_require__(/*! ./mime-node */ \"(rsc)/./node_modules/mailsplit/lib/mime-node.js\");\n\nconst MAX_HEAD_SIZE = 1 * 1024 * 1024;\nconst MAX_CHILD_NODES = 1000;\n\nconst HEAD = 0x01;\nconst BODY = 0x02;\n\nclass MessageSplitter extends Transform {\n    constructor(config) {\n        let options = {\n            readableObjectMode: true,\n            writableObjectMode: false\n        };\n        super(options);\n\n        this.config = config || {};\n        this.maxHeadSize = this.config.maxHeadSize || MAX_HEAD_SIZE;\n        this.maxChildNodes = this.config.maxChildNodes || MAX_CHILD_NODES;\n        this.tree = [];\n        this.nodeCounter = 0;\n        this.newNode();\n        this.tree.push(this.node);\n        this.line = false;\n        this.hasFailed = false;\n    }\n\n    _transform(chunk, encoding, callback) {\n        // process line by line\n        // find next line ending\n        let pos = 0;\n        let i = 0;\n        let group = {\n            type: 'none'\n        };\n        let groupstart = this.line ? -this.line.length : 0;\n        let groupend = 0;\n\n        let checkTrailingLinebreak = data => {\n            if (data.type === 'body' && data.node.parentNode && data.value && data.value.length) {\n                if (data.value[data.value.length - 1] === 0x0a) {\n                    groupstart--;\n                    groupend--;\n                    pos--;\n                    if (data.value.length > 1 && data.value[data.value.length - 2] === 0x0d) {\n                        groupstart--;\n                        groupend--;\n                        pos--;\n                        if (groupstart < 0 && !this.line) {\n                            // store only <CR> as <LF> should be on the positive side\n                            this.line = Buffer.allocUnsafe(1);\n                            this.line[0] = 0x0d;\n                        }\n                        data.value = data.value.slice(0, data.value.length - 2);\n                    } else {\n                        data.value = data.value.slice(0, data.value.length - 1);\n                    }\n                } else if (data.value[data.value.length - 1] === 0x0d) {\n                    groupstart--;\n                    groupend--;\n                    pos--;\n                    data.value = data.value.slice(0, data.value.length - 1);\n                }\n            }\n        };\n\n        let iterateData = () => {\n            for (let len = chunk.length; i < len; i++) {\n                // find next <LF>\n                if (chunk[i] === 0x0a) {\n                    // line end\n\n                    let start = Math.max(pos, 0);\n                    pos = ++i;\n\n                    return this.processLine(chunk.slice(start, i), false, (err, data, flush) => {\n                        if (err) {\n                            this.hasFailed = true;\n                            return setImmediate(() => callback(err));\n                        }\n\n                        if (!data) {\n                            return setImmediate(iterateData);\n                        }\n\n                        if (flush) {\n                            if (group && group.type !== 'none') {\n                                if (group.type === 'body' && groupend >= groupstart && group.node.parentNode) {\n                                    // do not include the last line ending for body\n                                    if (chunk[groupend - 1] === 0x0a) {\n                                        groupend--;\n                                        if (groupend >= groupstart && chunk[groupend - 1] === 0x0d) {\n                                            groupend--;\n                                        }\n                                    }\n                                }\n                                if (groupstart !== groupend) {\n                                    group.value = chunk.slice(groupstart, groupend);\n                                    if (groupend < i) {\n                                        data.value = chunk.slice(groupend, i);\n                                    }\n                                }\n                                this.push(group);\n                                group = {\n                                    type: 'none'\n                                };\n                                groupstart = groupend = i;\n                            }\n                            this.push(data);\n                            groupend = i;\n                            return setImmediate(iterateData);\n                        }\n\n                        if (data.type === group.type) {\n                            // shift slice end position forward\n                            groupend = i;\n                        } else {\n                            if (group.type === 'body' && groupend >= groupstart && group.node.parentNode) {\n                                // do not include the last line ending for body\n                                if (chunk[groupend - 1] === 0x0a) {\n                                    groupend--;\n                                    if (groupend >= groupstart && chunk[groupend - 1] === 0x0d) {\n                                        groupend--;\n                                    }\n                                }\n                            }\n\n                            if (group.type !== 'none' && group.type !== 'node') {\n                                // we have a previous data/body chunk to output\n                                if (groupstart !== groupend) {\n                                    group.value = chunk.slice(groupstart, groupend);\n                                    if (group.value && group.value.length) {\n                                        this.push(group);\n                                        group = {\n                                            type: 'none'\n                                        };\n                                    }\n                                }\n                            }\n\n                            if (data.type === 'node') {\n                                this.push(data);\n                                groupstart = i;\n                                groupend = i;\n                            } else if (groupstart < 0) {\n                                groupstart = i;\n                                groupend = i;\n                                checkTrailingLinebreak(data);\n                                if (data.value && data.value.length) {\n                                    this.push(data);\n                                }\n                            } else {\n                                // start new body/data chunk\n                                group = data;\n                                groupstart = groupend;\n                                groupend = i;\n                            }\n                        }\n                        return setImmediate(iterateData);\n                    });\n                }\n            }\n\n            // skip last linebreak for body\n            if (pos >= groupstart + 1 && group.type === 'body' && group.node.parentNode) {\n                // do not include the last line ending for body\n                if (chunk[pos - 1] === 0x0a) {\n                    pos--;\n                    if (pos >= groupstart && chunk[pos - 1] === 0x0d) {\n                        pos--;\n                    }\n                }\n            }\n\n            if (group.type !== 'none' && group.type !== 'node' && pos > groupstart) {\n                // we have a leftover data/body chunk to push out\n                group.value = chunk.slice(groupstart, pos);\n\n                if (group.value && group.value.length) {\n                    this.push(group);\n                    group = {\n                        type: 'none'\n                    };\n                }\n            }\n\n            if (pos < chunk.length) {\n                if (this.line) {\n                    this.line = Buffer.concat([this.line, chunk.slice(pos)]);\n                } else {\n                    this.line = chunk.slice(pos);\n                }\n            }\n            callback();\n        };\n\n        setImmediate(iterateData);\n    }\n\n    _flush(callback) {\n        if (this.hasFailed) {\n            return callback();\n        }\n        this.processLine(false, true, (err, data) => {\n            if (err) {\n                return setImmediate(() => callback(err));\n            }\n            if (data && (data.type === 'node' || (data.value && data.value.length))) {\n                this.push(data);\n            }\n            callback();\n        });\n    }\n\n    compareBoundary(line, startpos, boundary) {\n        // --{boundary}\\r\\n or --{boundary}--\\r\\n\n        if (line.length < boundary.length + 3 + startpos || line.length > boundary.length + 6 + startpos) {\n            return false;\n        }\n        for (let i = 0; i < boundary.length; i++) {\n            if (line[i + 2 + startpos] !== boundary[i]) {\n                return false;\n            }\n        }\n\n        let pos = 0;\n        for (let i = boundary.length + 2 + startpos; i < line.length; i++) {\n            let c = line[i];\n            if (pos === 0 && (c === 0x0d || c === 0x0a)) {\n                // 1: next node\n                return 1;\n            }\n            if (pos === 0 && c !== 0x2d) {\n                // expecting \"-\"\n                return false;\n            }\n            if (pos === 1 && c !== 0x2d) {\n                // expecting \"-\"\n                return false;\n            }\n            if (pos === 2 && c !== 0x0d && c !== 0x0a) {\n                // expecting line terminator, either <CR> or <LF>\n                return false;\n            }\n            if (pos === 3 && c !== 0x0a) {\n                // expecting line terminator <LF>\n                return false;\n            }\n            pos++;\n        }\n\n        // 2: multipart end\n        return 2;\n    }\n\n    checkBoundary(line) {\n        let startpos = 0;\n        if (line.length >= 1 && (line[0] === 0x0d || line[0] === 0x0a)) {\n            startpos++;\n            if (line.length >= 2 && (line[0] === 0x0d || line[1] === 0x0a)) {\n                startpos++;\n            }\n        }\n        if (line.length < 4 || line[startpos] !== 0x2d || line[startpos + 1] !== 0x2d) {\n            // defnitely not a boundary\n            return false;\n        }\n\n        let boundary;\n        if (this.node._boundary && (boundary = this.compareBoundary(line, startpos, this.node._boundary))) {\n            // 1: next child\n            // 2: multipart end\n            return boundary;\n        }\n\n        if (this.node._parentBoundary && (boundary = this.compareBoundary(line, startpos, this.node._parentBoundary))) {\n            // 3: next sibling\n            // 4: parent end\n            return boundary + 2;\n        }\n\n        return false;\n    }\n\n    processLine(line, final, next) {\n        let flush = false;\n\n        if (this.line && line) {\n            line = Buffer.concat([this.line, line]);\n            this.line = false;\n        } else if (this.line && !line) {\n            line = this.line;\n            this.line = false;\n        }\n\n        if (!line) {\n            line = Buffer.alloc(0);\n        }\n\n        if (this.nodeCounter > this.maxChildNodes) {\n            let err = new Error('Max allowed child nodes exceeded');\n            err.code = 'EMAXLEN';\n            return next(err);\n        }\n\n        // we check boundary outside the HEAD/BODY scope as it may appear anywhere\n        let boundary = this.checkBoundary(line);\n        if (boundary) {\n            // reached boundary, switch context\n            switch (boundary) {\n                case 1:\n                    // next child\n                    this.newNode(this.node);\n                    flush = true;\n                    break;\n                case 2:\n                    // reached end of children, keep current node\n                    break;\n                case 3: {\n                    // next sibling\n                    let parentNode = this.node.parentNode;\n                    if (parentNode && parentNode.contentType === 'message/rfc822') {\n                        // special case where immediate parent is an inline message block\n                        // move up another step\n                        parentNode = parentNode.parentNode;\n                    }\n                    this.newNode(parentNode);\n                    flush = true;\n                    break;\n                }\n                case 4:\n                    // special case when boundary close a node with only header.\n                    if (this.node && this.node._headerlen && !this.node.headers) {\n                        this.node.parseHeaders();\n                        this.push(this.node);\n                    }\n                    // move up\n                    if (this.tree.length) {\n                        this.node = this.tree.pop();\n                    }\n                    this.state = BODY;\n                    break;\n            }\n\n            return next(\n                null,\n                {\n                    node: this.node,\n                    type: 'data',\n                    value: line\n                },\n                flush\n            );\n        }\n\n        switch (this.state) {\n            case HEAD: {\n                this.node.addHeaderChunk(line);\n                if (this.node._headerlen > this.maxHeadSize) {\n                    let err = new Error('Max header size for a MIME node exceeded');\n                    err.code = 'EMAXLEN';\n                    return next(err);\n                }\n                if (final || (line.length === 1 && line[0] === 0x0a) || (line.length === 2 && line[0] === 0x0d && line[1] === 0x0a)) {\n                    let currentNode = this.node;\n\n                    currentNode.parseHeaders();\n\n                    // if the content is attached message then just continue\n                    if (\n                        currentNode.contentType === 'message/rfc822' &&\n                        !this.config.ignoreEmbedded &&\n                        (!currentNode.encoding || ['7bit', '8bit', 'binary'].includes(currentNode.encoding)) &&\n                        (this.config.defaultInlineEmbedded ? currentNode.disposition !== 'attachment' : currentNode.disposition === 'inline')\n                    ) {\n                        currentNode.messageNode = true;\n                        this.newNode(currentNode);\n                        if (currentNode.parentNode) {\n                            this.node._parentBoundary = currentNode.parentNode._boundary;\n                        }\n                    } else {\n                        if (currentNode.contentType === 'message/rfc822') {\n                            currentNode.messageNode = false;\n                        }\n                        this.state = BODY;\n                        if (currentNode.multipart && currentNode._boundary) {\n                            this.tree.push(currentNode);\n                        }\n                    }\n\n                    return next(null, currentNode, flush);\n                }\n\n                return next();\n            }\n            case BODY: {\n                return next(\n                    null,\n                    {\n                        node: this.node,\n                        type: this.node.multipart ? 'data' : 'body',\n                        value: line\n                    },\n                    flush\n                );\n            }\n        }\n\n        next(null, false);\n    }\n\n    newNode(parent) {\n        this.node = new MimeNode(parent || false, this.config);\n        this.state = HEAD;\n        this.nodeCounter++;\n    }\n}\n\nmodule.exports = MessageSplitter;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mailsplit/lib/message-splitter.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mailsplit/lib/mime-node.js":
/*!*************************************************!*\
  !*** ./node_modules/mailsplit/lib/mime-node.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst Headers = __webpack_require__(/*! ./headers */ \"(rsc)/./node_modules/mailsplit/lib/headers.js\");\nconst libmime = __webpack_require__(/*! libmime */ \"(rsc)/./node_modules/libmime/lib/libmime.js\");\nconst libqp = __webpack_require__(/*! libqp */ \"(rsc)/./node_modules/libqp/lib/libqp.js\");\nconst libbase64 = __webpack_require__(/*! libbase64 */ \"(rsc)/./node_modules/libbase64/lib/libbase64.js\");\nconst PassThrough = (__webpack_require__(/*! stream */ \"stream\").PassThrough);\nconst pathlib = __webpack_require__(/*! path */ \"path\");\n\nclass MimeNode {\n    constructor(parentNode, config) {\n        this.type = 'node';\n        this.root = !parentNode;\n        this.parentNode = parentNode;\n\n        this._parentBoundary = this.parentNode && this.parentNode._boundary;\n        this._headersLines = [];\n        this._headerlen = 0;\n\n        this._parsedContentType = false;\n        this._boundary = false;\n\n        this.multipart = false;\n        this.encoding = false;\n        this.headers = false;\n        this.contentType = false;\n        this.flowed = false;\n        this.delSp = false;\n\n        this.config = config || {};\n        this.libmime = new libmime.Libmime({ Iconv: this.config.Iconv });\n\n        this.parentPartNumber = (parentNode && this.partNr) || [];\n        this.partNr = false; // resolved later\n        this.childPartNumbers = 0;\n    }\n\n    getPartNr(provided) {\n        if (provided) {\n            return []\n                .concat(this.partNr || [])\n                .filter(nr => !isNaN(nr))\n                .concat(provided);\n        }\n        let childPartNr = ++this.childPartNumbers;\n        return []\n            .concat(this.partNr || [])\n            .filter(nr => !isNaN(nr))\n            .concat(childPartNr);\n    }\n\n    addHeaderChunk(line) {\n        if (!line) {\n            return;\n        }\n        this._headersLines.push(line);\n        this._headerlen += line.length;\n    }\n\n    parseHeaders() {\n        if (this.headers) {\n            return;\n        }\n        this.headers = new Headers(Buffer.concat(this._headersLines, this._headerlen), this.config);\n\n        this._parsedContentDisposition = this.libmime.parseHeaderValue(this.headers.getFirst('Content-Disposition'));\n\n        // if content-type is missing default to plaintext\n        let contentHeader;\n        if (this.headers.get('Content-Type').length) {\n            contentHeader = this.headers.getFirst('Content-Type');\n        } else {\n            if (this._parsedContentDisposition.params.filename) {\n                let extension = pathlib.parse(this._parsedContentDisposition.params.filename).ext.replace(/^\\./, '');\n                if (extension) {\n                    contentHeader = libmime.detectMimeType(extension);\n                }\n            }\n            if (!contentHeader) {\n                if (/^attachment$/i.test(this._parsedContentDisposition.value)) {\n                    contentHeader = 'application/octet-stream';\n                } else {\n                    contentHeader = 'text/plain';\n                }\n            }\n        }\n\n        this._parsedContentType = this.libmime.parseHeaderValue(contentHeader);\n\n        this.encoding = this.headers\n            .getFirst('Content-Transfer-Encoding')\n            .replace(/\\(.*\\)/g, '')\n            .toLowerCase()\n            .trim();\n        this.contentType = (this._parsedContentType.value || '').toLowerCase().trim() || false;\n        this.charset = this._parsedContentType.params.charset || false;\n        this.disposition = (this._parsedContentDisposition.value || '').toLowerCase().trim() || false;\n\n        // fix invalidly encoded disposition values\n        if (this.disposition) {\n            try {\n                this.disposition = this.libmime.decodeWords(this.disposition);\n            } catch (E) {\n                // failed to parse disposition, keep as is (most probably an unknown charset is used)\n            }\n        }\n\n        this.filename = this._parsedContentDisposition.params.filename || this._parsedContentType.params.name || false;\n\n        if (this._parsedContentType.params.format && this._parsedContentType.params.format.toLowerCase().trim() === 'flowed') {\n            this.flowed = true;\n            if (this._parsedContentType.params.delsp && this._parsedContentType.params.delsp.toLowerCase().trim() === 'yes') {\n                this.delSp = true;\n            }\n        }\n\n        if (this.filename) {\n            try {\n                this.filename = this.libmime.decodeWords(this.filename);\n            } catch (E) {\n                // failed to parse filename, keep as is (most probably an unknown charset is used)\n            }\n        }\n\n        this.multipart =\n            (this.contentType &&\n                this.contentType.substr(0, this.contentType.indexOf('/')) === 'multipart' &&\n                this.contentType.substr(this.contentType.indexOf('/') + 1)) ||\n            false;\n        this._boundary = (this._parsedContentType.params.boundary && Buffer.from(this._parsedContentType.params.boundary)) || false;\n\n        this.rfc822 = this.contentType === 'message/rfc822';\n\n        if (!this.parentNode || this.parentNode.rfc822) {\n            this.partNr = this.parentNode ? this.parentNode.getPartNr('TEXT') : ['TEXT'];\n        } else {\n            this.partNr = this.parentNode ? this.parentNode.getPartNr() : [];\n        }\n    }\n\n    getHeaders() {\n        if (!this.headers) {\n            this.parseHeaders();\n        }\n        return this.headers.build();\n    }\n\n    setContentType(contentType) {\n        if (!this.headers) {\n            this.parseHeaders();\n        }\n\n        contentType = (contentType || '').toLowerCase().trim();\n        if (contentType) {\n            this._parsedContentType.value = contentType;\n        }\n\n        if (!this.flowed && this._parsedContentType.params.format) {\n            delete this._parsedContentType.params.format;\n        }\n\n        if (!this.delSp && this._parsedContentType.params.delsp) {\n            delete this._parsedContentType.params.delsp;\n        }\n\n        this.headers.update('Content-Type', this.libmime.buildHeaderValue(this._parsedContentType));\n    }\n\n    setCharset(charset) {\n        if (!this.headers) {\n            this.parseHeaders();\n        }\n\n        charset = (charset || '').toLowerCase().trim();\n\n        if (charset === 'ascii') {\n            charset = '';\n        }\n\n        if (!charset) {\n            if (!this._parsedContentType.value) {\n                // nothing to set or update\n                return;\n            }\n            delete this._parsedContentType.params.charset;\n        } else {\n            this._parsedContentType.params.charset = charset;\n        }\n\n        if (!this._parsedContentType.value) {\n            this._parsedContentType.value = 'text/plain';\n        }\n\n        this.headers.update('Content-Type', this.libmime.buildHeaderValue(this._parsedContentType));\n    }\n\n    setFilename(filename) {\n        if (!this.headers) {\n            this.parseHeaders();\n        }\n\n        this.filename = (filename || '').toLowerCase().trim();\n\n        if (this._parsedContentType.params.name) {\n            delete this._parsedContentType.params.name;\n            this.headers.update('Content-Type', this.libmime.buildHeaderValue(this._parsedContentType));\n        }\n\n        if (!this.filename) {\n            if (!this._parsedContentDisposition.value) {\n                // nothing to set or update\n                return;\n            }\n            delete this._parsedContentDisposition.params.filename;\n        } else {\n            this._parsedContentDisposition.params.filename = this.filename;\n        }\n\n        if (!this._parsedContentDisposition.value) {\n            this._parsedContentDisposition.value = 'attachment';\n        }\n\n        this.headers.update('Content-Disposition', this.libmime.buildHeaderValue(this._parsedContentDisposition));\n    }\n\n    getDecoder() {\n        if (!this.headers) {\n            this.parseHeaders();\n        }\n\n        switch (this.encoding) {\n            case 'base64':\n                return new libbase64.Decoder();\n            case 'quoted-printable':\n                return new libqp.Decoder();\n            default:\n                return new PassThrough();\n        }\n    }\n\n    getEncoder(encoding) {\n        if (!this.headers) {\n            this.parseHeaders();\n        }\n\n        encoding = (encoding || '').toString().toLowerCase().trim();\n\n        if (encoding && encoding !== this.encoding) {\n            this.headers.update('Content-Transfer-Encoding', encoding);\n        } else {\n            encoding = this.encoding;\n        }\n\n        switch (encoding) {\n            case 'base64':\n                return new libbase64.Encoder();\n            case 'quoted-printable':\n                return new libqp.Encoder();\n            default:\n                return new PassThrough();\n        }\n    }\n}\n\nmodule.exports = MimeNode;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mailsplit/lib/mime-node.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mailsplit/lib/node-rewriter.js":
/*!*****************************************************!*\
  !*** ./node_modules/mailsplit/lib/node-rewriter.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n// Helper class to rewrite nodes with specific mime type\n\nconst Transform = (__webpack_require__(/*! stream */ \"stream\").Transform);\nconst FlowedDecoder = __webpack_require__(/*! ./flowed-decoder */ \"(rsc)/./node_modules/mailsplit/lib/flowed-decoder.js\");\n\n/**\n * NodeRewriter Transform stream. Updates content for all nodes with specified mime type\n *\n * @constructor\n * @param {String} mimeType Define the Mime-Type to look for\n * @param {Function} rewriteAction Function to run with the node content\n */\nclass NodeRewriter extends Transform {\n    constructor(filterFunc, rewriteAction) {\n        let options = {\n            readableObjectMode: true,\n            writableObjectMode: true\n        };\n        super(options);\n\n        this.filterFunc = filterFunc;\n        this.rewriteAction = rewriteAction;\n\n        this.decoder = false;\n        this.encoder = false;\n        this.continue = false;\n    }\n\n    _transform(data, encoding, callback) {\n        this.processIncoming(data, callback);\n    }\n\n    _flush(callback) {\n        if (this.decoder) {\n            // emit an empty node just in case there is pending data to end\n            return this.processIncoming(\n                {\n                    type: 'none'\n                },\n                callback\n            );\n        }\n        return callback();\n    }\n\n    processIncoming(data, callback) {\n        if (this.decoder && data.type === 'body') {\n            // data to parse\n            if (!this.decoder.write(data.value)) {\n                return this.decoder.once('drain', callback);\n            } else {\n                return callback();\n            }\n        } else if (this.decoder && data.type !== 'body') {\n            // stop decoding.\n            // we can not process the current data chunk as we need to wait until\n            // the parsed data is completely processed, so we store a reference to the\n            // continue callback\n            this.continue = () => {\n                this.continue = false;\n                this.decoder = false;\n                this.encoder = false;\n                this.processIncoming(data, callback);\n            };\n            return this.decoder.end();\n        } else if (data.type === 'node' && this.filterFunc(data)) {\n            // found matching node, create new handler\n            this.emit('node', this.createDecodePair(data));\n        } else if (this.readable && data.type !== 'none') {\n            // we don't care about this data, just pass it over to the joiner\n            this.push(data);\n        }\n        callback();\n    }\n\n    createDecodePair(node) {\n        this.decoder = node.getDecoder();\n\n        if (['base64', 'quoted-printable'].includes(node.encoding)) {\n            this.encoder = node.getEncoder();\n        } else {\n            this.encoder = node.getEncoder('quoted-printable');\n        }\n\n        let lastByte = false;\n\n        let decoder = this.decoder;\n        let encoder = this.encoder;\n        let firstChunk = true;\n        decoder.$reading = false;\n\n        let readFromEncoder = () => {\n            decoder.$reading = true;\n\n            let data = encoder.read();\n            if (data === null) {\n                decoder.$reading = false;\n                return;\n            }\n\n            if (firstChunk) {\n                firstChunk = false;\n                if (this.readable) {\n                    this.push(node);\n                    if (node.type === 'body') {\n                        lastByte = node.value && node.value.length && node.value[node.value.length - 1];\n                    }\n                }\n            }\n\n            let writeMore = true;\n            if (this.readable) {\n                writeMore = this.push({\n                    node,\n                    type: 'body',\n                    value: data\n                });\n                lastByte = data && data.length && data[data.length - 1];\n            }\n\n            if (writeMore) {\n                return setImmediate(readFromEncoder);\n            } else {\n                encoder.pause();\n                // no idea how to catch drain? use timeout for now as poor man's substitute\n                // this.once('drain', () => encoder.resume());\n                setTimeout(() => {\n                    encoder.resume();\n                    setImmediate(readFromEncoder);\n                }, 100);\n            }\n        };\n\n        encoder.on('readable', () => {\n            if (!decoder.$reading) {\n                return readFromEncoder();\n            }\n        });\n\n        encoder.on('end', () => {\n            if (firstChunk) {\n                firstChunk = false;\n                if (this.readable) {\n                    this.push(node);\n                    if (node.type === 'body') {\n                        lastByte = node.value && node.value.length && node.value[node.value.length - 1];\n                    }\n                }\n            }\n\n            if (lastByte !== 0x0a) {\n                // make sure there is a terminating line break\n                this.push({\n                    node,\n                    type: 'body',\n                    value: Buffer.from([0x0a])\n                });\n            }\n\n            if (this.continue) {\n                return this.continue();\n            }\n        });\n\n        if (/^text\\//.test(node.contentType) && node.flowed) {\n            // text/plain; format=flowed is a special case\n            let flowDecoder = decoder;\n            decoder = new FlowedDecoder({\n                delSp: node.delSp,\n                encoding: node.encoding\n            });\n            flowDecoder.on('error', err => {\n                decoder.emit('error', err);\n            });\n            flowDecoder.pipe(decoder);\n\n            // we don't know what kind of data we are going to get, does it comply with the\n            // requirements of format=flowed, so we just cancel it\n            node.flowed = false;\n            node.delSp = false;\n            node.setContentType();\n        }\n\n        return {\n            node,\n            decoder,\n            encoder\n        };\n    }\n}\n\nmodule.exports = NodeRewriter;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mailsplit/lib/node-rewriter.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mailsplit/lib/node-streamer.js":
/*!*****************************************************!*\
  !*** ./node_modules/mailsplit/lib/node-streamer.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n// Helper class to rewrite nodes with specific mime type\n\nconst Transform = (__webpack_require__(/*! stream */ \"stream\").Transform);\nconst FlowedDecoder = __webpack_require__(/*! ./flowed-decoder */ \"(rsc)/./node_modules/mailsplit/lib/flowed-decoder.js\");\n\n/**\n * NodeRewriter Transform stream. Updates content for all nodes with specified mime type\n *\n * @constructor\n * @param {String} mimeType Define the Mime-Type to look for\n * @param {Function} streamAction Function to run with the node content\n */\nclass NodeStreamer extends Transform {\n    constructor(filterFunc, streamAction) {\n        let options = {\n            readableObjectMode: true,\n            writableObjectMode: true\n        };\n        super(options);\n\n        this.filterFunc = filterFunc;\n        this.streamAction = streamAction;\n\n        this.decoder = false;\n        this.canContinue = false;\n        this.continue = false;\n    }\n\n    _transform(data, encoding, callback) {\n        this.processIncoming(data, callback);\n    }\n\n    _flush(callback) {\n        if (this.decoder) {\n            // emit an empty node just in case there is pending data to end\n            return this.processIncoming(\n                {\n                    type: 'none'\n                },\n                callback\n            );\n        }\n        return callback();\n    }\n\n    processIncoming(data, callback) {\n        if (this.decoder && data.type === 'body') {\n            // data to parse\n            this.push(data);\n            if (!this.decoder.write(data.value)) {\n                return this.decoder.once('drain', callback);\n            } else {\n                return callback();\n            }\n        } else if (this.decoder && data.type !== 'body') {\n            // stop decoding.\n            // we can not process the current data chunk as we need to wait until\n            // the parsed data is completely processed, so we store a reference to the\n            // continue callback\n\n            let doContinue = () => {\n                this.continue = false;\n                this.decoder = false;\n                this.canContinue = false;\n                this.processIncoming(data, callback);\n            };\n\n            if (this.canContinue) {\n                setImmediate(doContinue);\n            } else {\n                this.continue = () => doContinue();\n            }\n\n            return this.decoder.end();\n        } else if (data.type === 'node' && this.filterFunc(data)) {\n            this.push(data);\n            // found matching node, create new handler\n            this.emit('node', this.createDecoder(data));\n        } else if (this.readable && data.type !== 'none') {\n            // we don't care about this data, just pass it over to the joiner\n            this.push(data);\n        }\n        callback();\n    }\n\n    createDecoder(node) {\n        this.decoder = node.getDecoder();\n\n        let decoder = this.decoder;\n        decoder.$reading = false;\n\n        if (/^text\\//.test(node.contentType) && node.flowed) {\n            let flowDecoder = decoder;\n            decoder = new FlowedDecoder({\n                delSp: node.delSp\n            });\n            flowDecoder.on('error', err => {\n                decoder.emit('error', err);\n            });\n            flowDecoder.pipe(decoder);\n        }\n\n        return {\n            node,\n            decoder,\n            done: () => {\n                if (typeof this.continue === 'function') {\n                    // called once input stream is processed\n                    this.continue();\n                } else {\n                    // called before input stream is processed\n                    this.canContinue = true;\n                }\n            }\n        };\n    }\n}\n\nmodule.exports = NodeStreamer;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mailsplit/lib/node-streamer.js\n");

/***/ })

};
;