/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/pino";
exports.ids = ["vendor-chunks/pino"];
exports.modules = {

/***/ "(rsc)/./node_modules/pino/lib/caller.js":
/*!*****************************************!*\
  !*** ./node_modules/pino/lib/caller.js ***!
  \*****************************************/
/***/ ((module) => {

"use strict";
eval("\n\nfunction noOpPrepareStackTrace (_, stack) {\n  return stack\n}\n\nmodule.exports = function getCallers () {\n  const originalPrepare = Error.prepareStackTrace\n  Error.prepareStackTrace = noOpPrepareStackTrace\n  const stack = new Error().stack\n  Error.prepareStackTrace = originalPrepare\n\n  if (!Array.isArray(stack)) {\n    return undefined\n  }\n\n  const entries = stack.slice(2)\n\n  const fileNames = []\n\n  for (const entry of entries) {\n    if (!entry) {\n      continue\n    }\n\n    fileNames.push(entry.getFileName())\n  }\n\n  return fileNames\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcGluby9saWIvY2FsbGVyLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxDb2RlXFxvbmUtbWFpbFxcbm9kZV9tb2R1bGVzXFxwaW5vXFxsaWJcXGNhbGxlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuZnVuY3Rpb24gbm9PcFByZXBhcmVTdGFja1RyYWNlIChfLCBzdGFjaykge1xuICByZXR1cm4gc3RhY2tcbn1cblxubW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiBnZXRDYWxsZXJzICgpIHtcbiAgY29uc3Qgb3JpZ2luYWxQcmVwYXJlID0gRXJyb3IucHJlcGFyZVN0YWNrVHJhY2VcbiAgRXJyb3IucHJlcGFyZVN0YWNrVHJhY2UgPSBub09wUHJlcGFyZVN0YWNrVHJhY2VcbiAgY29uc3Qgc3RhY2sgPSBuZXcgRXJyb3IoKS5zdGFja1xuICBFcnJvci5wcmVwYXJlU3RhY2tUcmFjZSA9IG9yaWdpbmFsUHJlcGFyZVxuXG4gIGlmICghQXJyYXkuaXNBcnJheShzdGFjaykpIHtcbiAgICByZXR1cm4gdW5kZWZpbmVkXG4gIH1cblxuICBjb25zdCBlbnRyaWVzID0gc3RhY2suc2xpY2UoMilcblxuICBjb25zdCBmaWxlTmFtZXMgPSBbXVxuXG4gIGZvciAoY29uc3QgZW50cnkgb2YgZW50cmllcykge1xuICAgIGlmICghZW50cnkpIHtcbiAgICAgIGNvbnRpbnVlXG4gICAgfVxuXG4gICAgZmlsZU5hbWVzLnB1c2goZW50cnkuZ2V0RmlsZU5hbWUoKSlcbiAgfVxuXG4gIHJldHVybiBmaWxlTmFtZXNcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pino/lib/caller.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pino/lib/constants.js":
/*!********************************************!*\
  !*** ./node_modules/pino/lib/constants.js ***!
  \********************************************/
/***/ ((module) => {

eval("/**\n * Represents default log level values\n *\n * @enum {number}\n */\nconst DEFAULT_LEVELS = {\n  trace: 10,\n  debug: 20,\n  info: 30,\n  warn: 40,\n  error: 50,\n  fatal: 60\n}\n\n/**\n * Represents sort order direction: `ascending` or `descending`\n *\n * @enum {string}\n */\nconst SORTING_ORDER = {\n  ASC: 'ASC',\n  DESC: 'DESC'\n}\n\nmodule.exports = {\n  DEFAULT_LEVELS,\n  SORTING_ORDER\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcGluby9saWIvY29uc3RhbnRzLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxDb2RlXFxvbmUtbWFpbFxcbm9kZV9tb2R1bGVzXFxwaW5vXFxsaWJcXGNvbnN0YW50cy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFJlcHJlc2VudHMgZGVmYXVsdCBsb2cgbGV2ZWwgdmFsdWVzXG4gKlxuICogQGVudW0ge251bWJlcn1cbiAqL1xuY29uc3QgREVGQVVMVF9MRVZFTFMgPSB7XG4gIHRyYWNlOiAxMCxcbiAgZGVidWc6IDIwLFxuICBpbmZvOiAzMCxcbiAgd2FybjogNDAsXG4gIGVycm9yOiA1MCxcbiAgZmF0YWw6IDYwXG59XG5cbi8qKlxuICogUmVwcmVzZW50cyBzb3J0IG9yZGVyIGRpcmVjdGlvbjogYGFzY2VuZGluZ2Agb3IgYGRlc2NlbmRpbmdgXG4gKlxuICogQGVudW0ge3N0cmluZ31cbiAqL1xuY29uc3QgU09SVElOR19PUkRFUiA9IHtcbiAgQVNDOiAnQVNDJyxcbiAgREVTQzogJ0RFU0MnXG59XG5cbm1vZHVsZS5leHBvcnRzID0ge1xuICBERUZBVUxUX0xFVkVMUyxcbiAgU09SVElOR19PUkRFUlxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pino/lib/constants.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pino/lib/levels.js":
/*!*****************************************!*\
  !*** ./node_modules/pino/lib/levels.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n/* eslint no-prototype-builtins: 0 */\nconst {\n  lsCacheSym,\n  levelValSym,\n  useOnlyCustomLevelsSym,\n  streamSym,\n  formattersSym,\n  hooksSym,\n  levelCompSym\n} = __webpack_require__(/*! ./symbols */ \"(rsc)/./node_modules/pino/lib/symbols.js\")\nconst { noop, genLog } = __webpack_require__(/*! ./tools */ \"(rsc)/./node_modules/pino/lib/tools.js\")\nconst { DEFAULT_LEVELS, SORTING_ORDER } = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/pino/lib/constants.js\")\n\nconst levelMethods = {\n  fatal: (hook) => {\n    const logFatal = genLog(DEFAULT_LEVELS.fatal, hook)\n    return function (...args) {\n      const stream = this[streamSym]\n      logFatal.call(this, ...args)\n      if (typeof stream.flushSync === 'function') {\n        try {\n          stream.flushSync()\n        } catch (e) {\n          // https://github.com/pinojs/pino/pull/740#discussion_r346788313\n        }\n      }\n    }\n  },\n  error: (hook) => genLog(DEFAULT_LEVELS.error, hook),\n  warn: (hook) => genLog(DEFAULT_LEVELS.warn, hook),\n  info: (hook) => genLog(DEFAULT_LEVELS.info, hook),\n  debug: (hook) => genLog(DEFAULT_LEVELS.debug, hook),\n  trace: (hook) => genLog(DEFAULT_LEVELS.trace, hook)\n}\n\nconst nums = Object.keys(DEFAULT_LEVELS).reduce((o, k) => {\n  o[DEFAULT_LEVELS[k]] = k\n  return o\n}, {})\n\nconst initialLsCache = Object.keys(nums).reduce((o, k) => {\n  o[k] = '{\"level\":' + Number(k)\n  return o\n}, {})\n\nfunction genLsCache (instance) {\n  const formatter = instance[formattersSym].level\n  const { labels } = instance.levels\n  const cache = {}\n  for (const label in labels) {\n    const level = formatter(labels[label], Number(label))\n    cache[label] = JSON.stringify(level).slice(0, -1)\n  }\n  instance[lsCacheSym] = cache\n  return instance\n}\n\nfunction isStandardLevel (level, useOnlyCustomLevels) {\n  if (useOnlyCustomLevels) {\n    return false\n  }\n\n  switch (level) {\n    case 'fatal':\n    case 'error':\n    case 'warn':\n    case 'info':\n    case 'debug':\n    case 'trace':\n      return true\n    default:\n      return false\n  }\n}\n\nfunction setLevel (level) {\n  const { labels, values } = this.levels\n  if (typeof level === 'number') {\n    if (labels[level] === undefined) throw Error('unknown level value' + level)\n    level = labels[level]\n  }\n  if (values[level] === undefined) throw Error('unknown level ' + level)\n  const preLevelVal = this[levelValSym]\n  const levelVal = this[levelValSym] = values[level]\n  const useOnlyCustomLevelsVal = this[useOnlyCustomLevelsSym]\n  const levelComparison = this[levelCompSym]\n  const hook = this[hooksSym].logMethod\n\n  for (const key in values) {\n    if (levelComparison(values[key], levelVal) === false) {\n      this[key] = noop\n      continue\n    }\n    this[key] = isStandardLevel(key, useOnlyCustomLevelsVal) ? levelMethods[key](hook) : genLog(values[key], hook)\n  }\n\n  this.emit(\n    'level-change',\n    level,\n    levelVal,\n    labels[preLevelVal],\n    preLevelVal,\n    this\n  )\n}\n\nfunction getLevel (level) {\n  const { levels, levelVal } = this\n  // protection against potential loss of Pino scope from serializers (edge case with circular refs - https://github.com/pinojs/pino/issues/833)\n  return (levels && levels.labels) ? levels.labels[levelVal] : ''\n}\n\nfunction isLevelEnabled (logLevel) {\n  const { values } = this.levels\n  const logLevelVal = values[logLevel]\n  return logLevelVal !== undefined && this[levelCompSym](logLevelVal, this[levelValSym])\n}\n\n/**\n * Determine if the given `current` level is enabled by comparing it\n * against the current threshold (`expected`).\n *\n * @param {SORTING_ORDER} direction comparison direction \"ASC\" or \"DESC\"\n * @param {number} current current log level number representation\n * @param {number} expected threshold value to compare with\n * @returns {boolean}\n */\nfunction compareLevel (direction, current, expected) {\n  if (direction === SORTING_ORDER.DESC) {\n    return current <= expected\n  }\n\n  return current >= expected\n}\n\n/**\n * Create a level comparison function based on `levelComparison`\n * it could a default function which compares levels either in \"ascending\" or \"descending\" order or custom comparison function\n *\n * @param {SORTING_ORDER | Function} levelComparison sort levels order direction or custom comparison function\n * @returns Function\n */\nfunction genLevelComparison (levelComparison) {\n  if (typeof levelComparison === 'string') {\n    return compareLevel.bind(null, levelComparison)\n  }\n\n  return levelComparison\n}\n\nfunction mappings (customLevels = null, useOnlyCustomLevels = false) {\n  const customNums = customLevels\n    /* eslint-disable */\n    ? Object.keys(customLevels).reduce((o, k) => {\n        o[customLevels[k]] = k\n        return o\n      }, {})\n    : null\n    /* eslint-enable */\n\n  const labels = Object.assign(\n    Object.create(Object.prototype, { Infinity: { value: 'silent' } }),\n    useOnlyCustomLevels ? null : nums,\n    customNums\n  )\n  const values = Object.assign(\n    Object.create(Object.prototype, { silent: { value: Infinity } }),\n    useOnlyCustomLevels ? null : DEFAULT_LEVELS,\n    customLevels\n  )\n  return { labels, values }\n}\n\nfunction assertDefaultLevelFound (defaultLevel, customLevels, useOnlyCustomLevels) {\n  if (typeof defaultLevel === 'number') {\n    const values = [].concat(\n      Object.keys(customLevels || {}).map(key => customLevels[key]),\n      useOnlyCustomLevels ? [] : Object.keys(nums).map(level => +level),\n      Infinity\n    )\n    if (!values.includes(defaultLevel)) {\n      throw Error(`default level:${defaultLevel} must be included in custom levels`)\n    }\n    return\n  }\n\n  const labels = Object.assign(\n    Object.create(Object.prototype, { silent: { value: Infinity } }),\n    useOnlyCustomLevels ? null : DEFAULT_LEVELS,\n    customLevels\n  )\n  if (!(defaultLevel in labels)) {\n    throw Error(`default level:${defaultLevel} must be included in custom levels`)\n  }\n}\n\nfunction assertNoLevelCollisions (levels, customLevels) {\n  const { labels, values } = levels\n  for (const k in customLevels) {\n    if (k in values) {\n      throw Error('levels cannot be overridden')\n    }\n    if (customLevels[k] in labels) {\n      throw Error('pre-existing level values cannot be used for new levels')\n    }\n  }\n}\n\n/**\n * Validates whether `levelComparison` is correct\n *\n * @throws Error\n * @param {SORTING_ORDER | Function} levelComparison - value to validate\n * @returns\n */\nfunction assertLevelComparison (levelComparison) {\n  if (typeof levelComparison === 'function') {\n    return\n  }\n\n  if (typeof levelComparison === 'string' && Object.values(SORTING_ORDER).includes(levelComparison)) {\n    return\n  }\n\n  throw new Error('Levels comparison should be one of \"ASC\", \"DESC\" or \"function\" type')\n}\n\nmodule.exports = {\n  initialLsCache,\n  genLsCache,\n  levelMethods,\n  getLevel,\n  setLevel,\n  isLevelEnabled,\n  mappings,\n  assertNoLevelCollisions,\n  assertDefaultLevelFound,\n  genLevelComparison,\n  assertLevelComparison\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pino/lib/levels.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pino/lib/meta.js":
/*!***************************************!*\
  !*** ./node_modules/pino/lib/meta.js ***!
  \***************************************/
/***/ ((module) => {

"use strict";
eval("\n\nmodule.exports = { version: '9.7.0' }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcGluby9saWIvbWV0YS5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWixtQkFBbUIiLCJzb3VyY2VzIjpbIkM6XFxDb2RlXFxvbmUtbWFpbFxcbm9kZV9tb2R1bGVzXFxwaW5vXFxsaWJcXG1ldGEuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbm1vZHVsZS5leHBvcnRzID0geyB2ZXJzaW9uOiAnOS43LjAnIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pino/lib/meta.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pino/lib/multistream.js":
/*!**********************************************!*\
  !*** ./node_modules/pino/lib/multistream.js ***!
  \**********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nconst metadata = Symbol.for('pino.metadata')\nconst { DEFAULT_LEVELS } = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/pino/lib/constants.js\")\n\nconst DEFAULT_INFO_LEVEL = DEFAULT_LEVELS.info\n\nfunction multistream (streamsArray, opts) {\n  let counter = 0\n  streamsArray = streamsArray || []\n  opts = opts || { dedupe: false }\n\n  const streamLevels = Object.create(DEFAULT_LEVELS)\n  streamLevels.silent = Infinity\n  if (opts.levels && typeof opts.levels === 'object') {\n    Object.keys(opts.levels).forEach(i => {\n      streamLevels[i] = opts.levels[i]\n    })\n  }\n\n  const res = {\n    write,\n    add,\n    emit,\n    flushSync,\n    end,\n    minLevel: 0,\n    streams: [],\n    clone,\n    [metadata]: true,\n    streamLevels\n  }\n\n  if (Array.isArray(streamsArray)) {\n    streamsArray.forEach(add, res)\n  } else {\n    add.call(res, streamsArray)\n  }\n\n  // clean this object up\n  // or it will stay allocated forever\n  // as it is closed on the following closures\n  streamsArray = null\n\n  return res\n\n  // we can exit early because the streams are ordered by level\n  function write (data) {\n    let dest\n    const level = this.lastLevel\n    const { streams } = this\n    // for handling situation when several streams has the same level\n    let recordedLevel = 0\n    let stream\n\n    // if dedupe set to true we send logs to the stream with the highest level\n    // therefore, we have to change sorting order\n    for (let i = initLoopVar(streams.length, opts.dedupe); checkLoopVar(i, streams.length, opts.dedupe); i = adjustLoopVar(i, opts.dedupe)) {\n      dest = streams[i]\n      if (dest.level <= level) {\n        if (recordedLevel !== 0 && recordedLevel !== dest.level) {\n          break\n        }\n        stream = dest.stream\n        if (stream[metadata]) {\n          const { lastTime, lastMsg, lastObj, lastLogger } = this\n          stream.lastLevel = level\n          stream.lastTime = lastTime\n          stream.lastMsg = lastMsg\n          stream.lastObj = lastObj\n          stream.lastLogger = lastLogger\n        }\n        stream.write(data)\n        if (opts.dedupe) {\n          recordedLevel = dest.level\n        }\n      } else if (!opts.dedupe) {\n        break\n      }\n    }\n  }\n\n  function emit (...args) {\n    for (const { stream } of this.streams) {\n      if (typeof stream.emit === 'function') {\n        stream.emit(...args)\n      }\n    }\n  }\n\n  function flushSync () {\n    for (const { stream } of this.streams) {\n      if (typeof stream.flushSync === 'function') {\n        stream.flushSync()\n      }\n    }\n  }\n\n  function add (dest) {\n    if (!dest) {\n      return res\n    }\n\n    // Check that dest implements either StreamEntry or DestinationStream\n    const isStream = typeof dest.write === 'function' || dest.stream\n    const stream_ = dest.write ? dest : dest.stream\n    // This is necessary to provide a meaningful error message, otherwise it throws somewhere inside write()\n    if (!isStream) {\n      throw Error('stream object needs to implement either StreamEntry or DestinationStream interface')\n    }\n\n    const { streams, streamLevels } = this\n\n    let level\n    if (typeof dest.levelVal === 'number') {\n      level = dest.levelVal\n    } else if (typeof dest.level === 'string') {\n      level = streamLevels[dest.level]\n    } else if (typeof dest.level === 'number') {\n      level = dest.level\n    } else {\n      level = DEFAULT_INFO_LEVEL\n    }\n\n    const dest_ = {\n      stream: stream_,\n      level,\n      levelVal: undefined,\n      id: counter++\n    }\n\n    streams.unshift(dest_)\n    streams.sort(compareByLevel)\n\n    this.minLevel = streams[0].level\n\n    return res\n  }\n\n  function end () {\n    for (const { stream } of this.streams) {\n      if (typeof stream.flushSync === 'function') {\n        stream.flushSync()\n      }\n      stream.end()\n    }\n  }\n\n  function clone (level) {\n    const streams = new Array(this.streams.length)\n\n    for (let i = 0; i < streams.length; i++) {\n      streams[i] = {\n        level,\n        stream: this.streams[i].stream\n      }\n    }\n\n    return {\n      write,\n      add,\n      minLevel: level,\n      streams,\n      clone,\n      emit,\n      flushSync,\n      [metadata]: true\n    }\n  }\n}\n\nfunction compareByLevel (a, b) {\n  return a.level - b.level\n}\n\nfunction initLoopVar (length, dedupe) {\n  return dedupe ? length - 1 : 0\n}\n\nfunction adjustLoopVar (i, dedupe) {\n  return dedupe ? i - 1 : i + 1\n}\n\nfunction checkLoopVar (i, length, dedupe) {\n  return dedupe ? i >= 0 : i < length\n}\n\nmodule.exports = multistream\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pino/lib/multistream.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pino/lib/proto.js":
/*!****************************************!*\
  !*** ./node_modules/pino/lib/proto.js ***!
  \****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\n/* eslint no-prototype-builtins: 0 */\n\nconst { EventEmitter } = __webpack_require__(/*! node:events */ \"node:events\")\nconst {\n  lsCacheSym,\n  levelValSym,\n  setLevelSym,\n  getLevelSym,\n  chindingsSym,\n  parsedChindingsSym,\n  mixinSym,\n  asJsonSym,\n  writeSym,\n  mixinMergeStrategySym,\n  timeSym,\n  timeSliceIndexSym,\n  streamSym,\n  serializersSym,\n  formattersSym,\n  errorKeySym,\n  messageKeySym,\n  useOnlyCustomLevelsSym,\n  needsMetadataGsym,\n  redactFmtSym,\n  stringifySym,\n  formatOptsSym,\n  stringifiersSym,\n  msgPrefixSym,\n  hooksSym\n} = __webpack_require__(/*! ./symbols */ \"(rsc)/./node_modules/pino/lib/symbols.js\")\nconst {\n  getLevel,\n  setLevel,\n  isLevelEnabled,\n  mappings,\n  initialLsCache,\n  genLsCache,\n  assertNoLevelCollisions\n} = __webpack_require__(/*! ./levels */ \"(rsc)/./node_modules/pino/lib/levels.js\")\nconst {\n  asChindings,\n  asJson,\n  buildFormatters,\n  stringify\n} = __webpack_require__(/*! ./tools */ \"(rsc)/./node_modules/pino/lib/tools.js\")\nconst {\n  version\n} = __webpack_require__(/*! ./meta */ \"(rsc)/./node_modules/pino/lib/meta.js\")\nconst redaction = __webpack_require__(/*! ./redaction */ \"(rsc)/./node_modules/pino/lib/redaction.js\")\n\n// note: use of class is satirical\n// https://github.com/pinojs/pino/pull/433#pullrequestreview-127703127\nconst constructor = class Pino {}\nconst prototype = {\n  constructor,\n  child,\n  bindings,\n  setBindings,\n  flush,\n  isLevelEnabled,\n  version,\n  get level () { return this[getLevelSym]() },\n  set level (lvl) { this[setLevelSym](lvl) },\n  get levelVal () { return this[levelValSym] },\n  set levelVal (n) { throw Error('levelVal is read-only') },\n  [lsCacheSym]: initialLsCache,\n  [writeSym]: write,\n  [asJsonSym]: asJson,\n  [getLevelSym]: getLevel,\n  [setLevelSym]: setLevel\n}\n\nObject.setPrototypeOf(prototype, EventEmitter.prototype)\n\n// exporting and consuming the prototype object using factory pattern fixes scoping issues with getters when serializing\nmodule.exports = function () {\n  return Object.create(prototype)\n}\n\nconst resetChildingsFormatter = bindings => bindings\nfunction child (bindings, options) {\n  if (!bindings) {\n    throw Error('missing bindings for child Pino')\n  }\n  options = options || {} // default options to empty object\n  const serializers = this[serializersSym]\n  const formatters = this[formattersSym]\n  const instance = Object.create(this)\n\n  if (options.hasOwnProperty('serializers') === true) {\n    instance[serializersSym] = Object.create(null)\n\n    for (const k in serializers) {\n      instance[serializersSym][k] = serializers[k]\n    }\n    const parentSymbols = Object.getOwnPropertySymbols(serializers)\n    /* eslint no-var: off */\n    for (var i = 0; i < parentSymbols.length; i++) {\n      const ks = parentSymbols[i]\n      instance[serializersSym][ks] = serializers[ks]\n    }\n\n    for (const bk in options.serializers) {\n      instance[serializersSym][bk] = options.serializers[bk]\n    }\n    const bindingsSymbols = Object.getOwnPropertySymbols(options.serializers)\n    for (var bi = 0; bi < bindingsSymbols.length; bi++) {\n      const bks = bindingsSymbols[bi]\n      instance[serializersSym][bks] = options.serializers[bks]\n    }\n  } else instance[serializersSym] = serializers\n  if (options.hasOwnProperty('formatters')) {\n    const { level, bindings: chindings, log } = options.formatters\n    instance[formattersSym] = buildFormatters(\n      level || formatters.level,\n      chindings || resetChildingsFormatter,\n      log || formatters.log\n    )\n  } else {\n    instance[formattersSym] = buildFormatters(\n      formatters.level,\n      resetChildingsFormatter,\n      formatters.log\n    )\n  }\n  if (options.hasOwnProperty('customLevels') === true) {\n    assertNoLevelCollisions(this.levels, options.customLevels)\n    instance.levels = mappings(options.customLevels, instance[useOnlyCustomLevelsSym])\n    genLsCache(instance)\n  }\n\n  // redact must place before asChindings and only replace if exist\n  if ((typeof options.redact === 'object' && options.redact !== null) || Array.isArray(options.redact)) {\n    instance.redact = options.redact // replace redact directly\n    const stringifiers = redaction(instance.redact, stringify)\n    const formatOpts = { stringify: stringifiers[redactFmtSym] }\n    instance[stringifySym] = stringify\n    instance[stringifiersSym] = stringifiers\n    instance[formatOptsSym] = formatOpts\n  }\n\n  if (typeof options.msgPrefix === 'string') {\n    instance[msgPrefixSym] = (this[msgPrefixSym] || '') + options.msgPrefix\n  }\n\n  instance[chindingsSym] = asChindings(instance, bindings)\n  const childLevel = options.level || this.level\n  instance[setLevelSym](childLevel)\n  this.onChild(instance)\n  return instance\n}\n\nfunction bindings () {\n  const chindings = this[chindingsSym]\n  const chindingsJson = `{${chindings.substr(1)}}` // at least contains ,\"pid\":7068,\"hostname\":\"myMac\"\n  const bindingsFromJson = JSON.parse(chindingsJson)\n  delete bindingsFromJson.pid\n  delete bindingsFromJson.hostname\n  return bindingsFromJson\n}\n\nfunction setBindings (newBindings) {\n  const chindings = asChindings(this, newBindings)\n  this[chindingsSym] = chindings\n  delete this[parsedChindingsSym]\n}\n\n/**\n * Default strategy for creating `mergeObject` from arguments and the result from `mixin()`.\n * Fields from `mergeObject` have higher priority in this strategy.\n *\n * @param {Object} mergeObject The object a user has supplied to the logging function.\n * @param {Object} mixinObject The result of the `mixin` method.\n * @return {Object}\n */\nfunction defaultMixinMergeStrategy (mergeObject, mixinObject) {\n  return Object.assign(mixinObject, mergeObject)\n}\n\nfunction write (_obj, msg, num) {\n  const t = this[timeSym]()\n  const mixin = this[mixinSym]\n  const errorKey = this[errorKeySym]\n  const messageKey = this[messageKeySym]\n  const mixinMergeStrategy = this[mixinMergeStrategySym] || defaultMixinMergeStrategy\n  let obj\n  const streamWriteHook = this[hooksSym].streamWrite\n\n  if (_obj === undefined || _obj === null) {\n    obj = {}\n  } else if (_obj instanceof Error) {\n    obj = { [errorKey]: _obj }\n    if (msg === undefined) {\n      msg = _obj.message\n    }\n  } else {\n    obj = _obj\n    if (msg === undefined && _obj[messageKey] === undefined && _obj[errorKey]) {\n      msg = _obj[errorKey].message\n    }\n  }\n\n  if (mixin) {\n    obj = mixinMergeStrategy(obj, mixin(obj, num, this))\n  }\n\n  const s = this[asJsonSym](obj, msg, num, t)\n\n  const stream = this[streamSym]\n  if (stream[needsMetadataGsym] === true) {\n    stream.lastLevel = num\n    stream.lastObj = obj\n    stream.lastMsg = msg\n    stream.lastTime = t.slice(this[timeSliceIndexSym])\n    stream.lastLogger = this // for child loggers\n  }\n  stream.write(streamWriteHook ? streamWriteHook(s) : s)\n}\n\nfunction noop () {}\n\nfunction flush (cb) {\n  if (cb != null && typeof cb !== 'function') {\n    throw Error('callback must be a function')\n  }\n\n  const stream = this[streamSym]\n\n  if (typeof stream.flush === 'function') {\n    stream.flush(cb || noop)\n  } else if (cb) cb()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pino/lib/proto.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pino/lib/redaction.js":
/*!********************************************!*\
  !*** ./node_modules/pino/lib/redaction.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nconst fastRedact = __webpack_require__(/*! fast-redact */ \"(rsc)/./node_modules/fast-redact/index.js\")\nconst { redactFmtSym, wildcardFirstSym } = __webpack_require__(/*! ./symbols */ \"(rsc)/./node_modules/pino/lib/symbols.js\")\nconst { rx, validator } = fastRedact\n\nconst validate = validator({\n  ERR_PATHS_MUST_BE_STRINGS: () => 'pino – redacted paths must be strings',\n  ERR_INVALID_PATH: (s) => `pino – redact paths array contains an invalid path (${s})`\n})\n\nconst CENSOR = '[Redacted]'\nconst strict = false // TODO should this be configurable?\n\nfunction redaction (opts, serialize) {\n  const { paths, censor } = handle(opts)\n\n  const shape = paths.reduce((o, str) => {\n    rx.lastIndex = 0\n    const first = rx.exec(str)\n    const next = rx.exec(str)\n\n    // ns is the top-level path segment, brackets + quoting removed.\n    let ns = first[1] !== undefined\n      ? first[1].replace(/^(?:\"|'|`)(.*)(?:\"|'|`)$/, '$1')\n      : first[0]\n\n    if (ns === '*') {\n      ns = wildcardFirstSym\n    }\n\n    // top level key:\n    if (next === null) {\n      o[ns] = null\n      return o\n    }\n\n    // path with at least two segments:\n    // if ns is already redacted at the top level, ignore lower level redactions\n    if (o[ns] === null) {\n      return o\n    }\n\n    const { index } = next\n    const nextPath = `${str.substr(index, str.length - 1)}`\n\n    o[ns] = o[ns] || []\n\n    // shape is a mix of paths beginning with literal values and wildcard\n    // paths [ \"a.b.c\", \"*.b.z\" ] should reduce to a shape of\n    // { \"a\": [ \"b.c\", \"b.z\" ], *: [ \"b.z\" ] }\n    // note: \"b.z\" is in both \"a\" and * arrays because \"a\" matches the wildcard.\n    // (* entry has wildcardFirstSym as key)\n    if (ns !== wildcardFirstSym && o[ns].length === 0) {\n      // first time ns's get all '*' redactions so far\n      o[ns].push(...(o[wildcardFirstSym] || []))\n    }\n\n    if (ns === wildcardFirstSym) {\n      // new * path gets added to all previously registered literal ns's.\n      Object.keys(o).forEach(function (k) {\n        if (o[k]) {\n          o[k].push(nextPath)\n        }\n      })\n    }\n\n    o[ns].push(nextPath)\n    return o\n  }, {})\n\n  // the redactor assigned to the format symbol key\n  // provides top level redaction for instances where\n  // an object is interpolated into the msg string\n  const result = {\n    [redactFmtSym]: fastRedact({ paths, censor, serialize, strict })\n  }\n\n  const topCensor = (...args) => {\n    return typeof censor === 'function' ? serialize(censor(...args)) : serialize(censor)\n  }\n\n  return [...Object.keys(shape), ...Object.getOwnPropertySymbols(shape)].reduce((o, k) => {\n    // top level key:\n    if (shape[k] === null) {\n      o[k] = (value) => topCensor(value, [k])\n    } else {\n      const wrappedCensor = typeof censor === 'function'\n        ? (value, path) => {\n            return censor(value, [k, ...path])\n          }\n        : censor\n      o[k] = fastRedact({\n        paths: shape[k],\n        censor: wrappedCensor,\n        serialize,\n        strict\n      })\n    }\n    return o\n  }, result)\n}\n\nfunction handle (opts) {\n  if (Array.isArray(opts)) {\n    opts = { paths: opts, censor: CENSOR }\n    validate(opts)\n    return opts\n  }\n  let { paths, censor = CENSOR, remove } = opts\n  if (Array.isArray(paths) === false) { throw Error('pino – redact must contain an array of strings') }\n  if (remove === true) censor = undefined\n  validate({ paths, censor })\n\n  return { paths, censor }\n}\n\nmodule.exports = redaction\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcGluby9saWIvcmVkYWN0aW9uLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLG1CQUFtQixtQkFBTyxDQUFDLDhEQUFhO0FBQ3hDLFFBQVEsaUNBQWlDLEVBQUUsbUJBQU8sQ0FBQywyREFBVztBQUM5RCxRQUFRLGdCQUFnQjs7QUFFeEI7QUFDQTtBQUNBLGtGQUFrRixFQUFFO0FBQ3BGLENBQUM7O0FBRUQ7QUFDQTs7QUFFQTtBQUNBLFVBQVUsZ0JBQWdCOztBQUUxQjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLFlBQVksUUFBUTtBQUNwQix3QkFBd0Isa0NBQWtDOztBQUUxRDs7QUFFQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7O0FBRUE7QUFDQTtBQUNBLEdBQUcsSUFBSTs7QUFFUDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyxrQ0FBa0M7QUFDbkU7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7O0FBRUE7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSxRQUFRLGlDQUFpQztBQUN6Qyx3Q0FBd0M7QUFDeEM7QUFDQSxhQUFhLGVBQWU7O0FBRTVCLFdBQVc7QUFDWDs7QUFFQSIsInNvdXJjZXMiOlsiQzpcXENvZGVcXG9uZS1tYWlsXFxub2RlX21vZHVsZXNcXHBpbm9cXGxpYlxccmVkYWN0aW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5jb25zdCBmYXN0UmVkYWN0ID0gcmVxdWlyZSgnZmFzdC1yZWRhY3QnKVxuY29uc3QgeyByZWRhY3RGbXRTeW0sIHdpbGRjYXJkRmlyc3RTeW0gfSA9IHJlcXVpcmUoJy4vc3ltYm9scycpXG5jb25zdCB7IHJ4LCB2YWxpZGF0b3IgfSA9IGZhc3RSZWRhY3RcblxuY29uc3QgdmFsaWRhdGUgPSB2YWxpZGF0b3Ioe1xuICBFUlJfUEFUSFNfTVVTVF9CRV9TVFJJTkdTOiAoKSA9PiAncGlubyDigJMgcmVkYWN0ZWQgcGF0aHMgbXVzdCBiZSBzdHJpbmdzJyxcbiAgRVJSX0lOVkFMSURfUEFUSDogKHMpID0+IGBwaW5vIOKAkyByZWRhY3QgcGF0aHMgYXJyYXkgY29udGFpbnMgYW4gaW52YWxpZCBwYXRoICgke3N9KWBcbn0pXG5cbmNvbnN0IENFTlNPUiA9ICdbUmVkYWN0ZWRdJ1xuY29uc3Qgc3RyaWN0ID0gZmFsc2UgLy8gVE9ETyBzaG91bGQgdGhpcyBiZSBjb25maWd1cmFibGU/XG5cbmZ1bmN0aW9uIHJlZGFjdGlvbiAob3B0cywgc2VyaWFsaXplKSB7XG4gIGNvbnN0IHsgcGF0aHMsIGNlbnNvciB9ID0gaGFuZGxlKG9wdHMpXG5cbiAgY29uc3Qgc2hhcGUgPSBwYXRocy5yZWR1Y2UoKG8sIHN0cikgPT4ge1xuICAgIHJ4Lmxhc3RJbmRleCA9IDBcbiAgICBjb25zdCBmaXJzdCA9IHJ4LmV4ZWMoc3RyKVxuICAgIGNvbnN0IG5leHQgPSByeC5leGVjKHN0cilcblxuICAgIC8vIG5zIGlzIHRoZSB0b3AtbGV2ZWwgcGF0aCBzZWdtZW50LCBicmFja2V0cyArIHF1b3RpbmcgcmVtb3ZlZC5cbiAgICBsZXQgbnMgPSBmaXJzdFsxXSAhPT0gdW5kZWZpbmVkXG4gICAgICA/IGZpcnN0WzFdLnJlcGxhY2UoL14oPzpcInwnfGApKC4qKSg/OlwifCd8YCkkLywgJyQxJylcbiAgICAgIDogZmlyc3RbMF1cblxuICAgIGlmIChucyA9PT0gJyonKSB7XG4gICAgICBucyA9IHdpbGRjYXJkRmlyc3RTeW1cbiAgICB9XG5cbiAgICAvLyB0b3AgbGV2ZWwga2V5OlxuICAgIGlmIChuZXh0ID09PSBudWxsKSB7XG4gICAgICBvW25zXSA9IG51bGxcbiAgICAgIHJldHVybiBvXG4gICAgfVxuXG4gICAgLy8gcGF0aCB3aXRoIGF0IGxlYXN0IHR3byBzZWdtZW50czpcbiAgICAvLyBpZiBucyBpcyBhbHJlYWR5IHJlZGFjdGVkIGF0IHRoZSB0b3AgbGV2ZWwsIGlnbm9yZSBsb3dlciBsZXZlbCByZWRhY3Rpb25zXG4gICAgaWYgKG9bbnNdID09PSBudWxsKSB7XG4gICAgICByZXR1cm4gb1xuICAgIH1cblxuICAgIGNvbnN0IHsgaW5kZXggfSA9IG5leHRcbiAgICBjb25zdCBuZXh0UGF0aCA9IGAke3N0ci5zdWJzdHIoaW5kZXgsIHN0ci5sZW5ndGggLSAxKX1gXG5cbiAgICBvW25zXSA9IG9bbnNdIHx8IFtdXG5cbiAgICAvLyBzaGFwZSBpcyBhIG1peCBvZiBwYXRocyBiZWdpbm5pbmcgd2l0aCBsaXRlcmFsIHZhbHVlcyBhbmQgd2lsZGNhcmRcbiAgICAvLyBwYXRocyBbIFwiYS5iLmNcIiwgXCIqLmIuelwiIF0gc2hvdWxkIHJlZHVjZSB0byBhIHNoYXBlIG9mXG4gICAgLy8geyBcImFcIjogWyBcImIuY1wiLCBcImIuelwiIF0sICo6IFsgXCJiLnpcIiBdIH1cbiAgICAvLyBub3RlOiBcImIuelwiIGlzIGluIGJvdGggXCJhXCIgYW5kICogYXJyYXlzIGJlY2F1c2UgXCJhXCIgbWF0Y2hlcyB0aGUgd2lsZGNhcmQuXG4gICAgLy8gKCogZW50cnkgaGFzIHdpbGRjYXJkRmlyc3RTeW0gYXMga2V5KVxuICAgIGlmIChucyAhPT0gd2lsZGNhcmRGaXJzdFN5bSAmJiBvW25zXS5sZW5ndGggPT09IDApIHtcbiAgICAgIC8vIGZpcnN0IHRpbWUgbnMncyBnZXQgYWxsICcqJyByZWRhY3Rpb25zIHNvIGZhclxuICAgICAgb1tuc10ucHVzaCguLi4ob1t3aWxkY2FyZEZpcnN0U3ltXSB8fCBbXSkpXG4gICAgfVxuXG4gICAgaWYgKG5zID09PSB3aWxkY2FyZEZpcnN0U3ltKSB7XG4gICAgICAvLyBuZXcgKiBwYXRoIGdldHMgYWRkZWQgdG8gYWxsIHByZXZpb3VzbHkgcmVnaXN0ZXJlZCBsaXRlcmFsIG5zJ3MuXG4gICAgICBPYmplY3Qua2V5cyhvKS5mb3JFYWNoKGZ1bmN0aW9uIChrKSB7XG4gICAgICAgIGlmIChvW2tdKSB7XG4gICAgICAgICAgb1trXS5wdXNoKG5leHRQYXRoKVxuICAgICAgICB9XG4gICAgICB9KVxuICAgIH1cblxuICAgIG9bbnNdLnB1c2gobmV4dFBhdGgpXG4gICAgcmV0dXJuIG9cbiAgfSwge30pXG5cbiAgLy8gdGhlIHJlZGFjdG9yIGFzc2lnbmVkIHRvIHRoZSBmb3JtYXQgc3ltYm9sIGtleVxuICAvLyBwcm92aWRlcyB0b3AgbGV2ZWwgcmVkYWN0aW9uIGZvciBpbnN0YW5jZXMgd2hlcmVcbiAgLy8gYW4gb2JqZWN0IGlzIGludGVycG9sYXRlZCBpbnRvIHRoZSBtc2cgc3RyaW5nXG4gIGNvbnN0IHJlc3VsdCA9IHtcbiAgICBbcmVkYWN0Rm10U3ltXTogZmFzdFJlZGFjdCh7IHBhdGhzLCBjZW5zb3IsIHNlcmlhbGl6ZSwgc3RyaWN0IH0pXG4gIH1cblxuICBjb25zdCB0b3BDZW5zb3IgPSAoLi4uYXJncykgPT4ge1xuICAgIHJldHVybiB0eXBlb2YgY2Vuc29yID09PSAnZnVuY3Rpb24nID8gc2VyaWFsaXplKGNlbnNvciguLi5hcmdzKSkgOiBzZXJpYWxpemUoY2Vuc29yKVxuICB9XG5cbiAgcmV0dXJuIFsuLi5PYmplY3Qua2V5cyhzaGFwZSksIC4uLk9iamVjdC5nZXRPd25Qcm9wZXJ0eVN5bWJvbHMoc2hhcGUpXS5yZWR1Y2UoKG8sIGspID0+IHtcbiAgICAvLyB0b3AgbGV2ZWwga2V5OlxuICAgIGlmIChzaGFwZVtrXSA9PT0gbnVsbCkge1xuICAgICAgb1trXSA9ICh2YWx1ZSkgPT4gdG9wQ2Vuc29yKHZhbHVlLCBba10pXG4gICAgfSBlbHNlIHtcbiAgICAgIGNvbnN0IHdyYXBwZWRDZW5zb3IgPSB0eXBlb2YgY2Vuc29yID09PSAnZnVuY3Rpb24nXG4gICAgICAgID8gKHZhbHVlLCBwYXRoKSA9PiB7XG4gICAgICAgICAgICByZXR1cm4gY2Vuc29yKHZhbHVlLCBbaywgLi4ucGF0aF0pXG4gICAgICAgICAgfVxuICAgICAgICA6IGNlbnNvclxuICAgICAgb1trXSA9IGZhc3RSZWRhY3Qoe1xuICAgICAgICBwYXRoczogc2hhcGVba10sXG4gICAgICAgIGNlbnNvcjogd3JhcHBlZENlbnNvcixcbiAgICAgICAgc2VyaWFsaXplLFxuICAgICAgICBzdHJpY3RcbiAgICAgIH0pXG4gICAgfVxuICAgIHJldHVybiBvXG4gIH0sIHJlc3VsdClcbn1cblxuZnVuY3Rpb24gaGFuZGxlIChvcHRzKSB7XG4gIGlmIChBcnJheS5pc0FycmF5KG9wdHMpKSB7XG4gICAgb3B0cyA9IHsgcGF0aHM6IG9wdHMsIGNlbnNvcjogQ0VOU09SIH1cbiAgICB2YWxpZGF0ZShvcHRzKVxuICAgIHJldHVybiBvcHRzXG4gIH1cbiAgbGV0IHsgcGF0aHMsIGNlbnNvciA9IENFTlNPUiwgcmVtb3ZlIH0gPSBvcHRzXG4gIGlmIChBcnJheS5pc0FycmF5KHBhdGhzKSA9PT0gZmFsc2UpIHsgdGhyb3cgRXJyb3IoJ3Bpbm8g4oCTIHJlZGFjdCBtdXN0IGNvbnRhaW4gYW4gYXJyYXkgb2Ygc3RyaW5ncycpIH1cbiAgaWYgKHJlbW92ZSA9PT0gdHJ1ZSkgY2Vuc29yID0gdW5kZWZpbmVkXG4gIHZhbGlkYXRlKHsgcGF0aHMsIGNlbnNvciB9KVxuXG4gIHJldHVybiB7IHBhdGhzLCBjZW5zb3IgfVxufVxuXG5tb2R1bGUuZXhwb3J0cyA9IHJlZGFjdGlvblxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pino/lib/redaction.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pino/lib/symbols.js":
/*!******************************************!*\
  !*** ./node_modules/pino/lib/symbols.js ***!
  \******************************************/
/***/ ((module) => {

"use strict";
eval("\n\nconst setLevelSym = Symbol('pino.setLevel')\nconst getLevelSym = Symbol('pino.getLevel')\nconst levelValSym = Symbol('pino.levelVal')\nconst levelCompSym = Symbol('pino.levelComp')\nconst useLevelLabelsSym = Symbol('pino.useLevelLabels')\nconst useOnlyCustomLevelsSym = Symbol('pino.useOnlyCustomLevels')\nconst mixinSym = Symbol('pino.mixin')\n\nconst lsCacheSym = Symbol('pino.lsCache')\nconst chindingsSym = Symbol('pino.chindings')\n\nconst asJsonSym = Symbol('pino.asJson')\nconst writeSym = Symbol('pino.write')\nconst redactFmtSym = Symbol('pino.redactFmt')\n\nconst timeSym = Symbol('pino.time')\nconst timeSliceIndexSym = Symbol('pino.timeSliceIndex')\nconst streamSym = Symbol('pino.stream')\nconst stringifySym = Symbol('pino.stringify')\nconst stringifySafeSym = Symbol('pino.stringifySafe')\nconst stringifiersSym = Symbol('pino.stringifiers')\nconst endSym = Symbol('pino.end')\nconst formatOptsSym = Symbol('pino.formatOpts')\nconst messageKeySym = Symbol('pino.messageKey')\nconst errorKeySym = Symbol('pino.errorKey')\nconst nestedKeySym = Symbol('pino.nestedKey')\nconst nestedKeyStrSym = Symbol('pino.nestedKeyStr')\nconst mixinMergeStrategySym = Symbol('pino.mixinMergeStrategy')\nconst msgPrefixSym = Symbol('pino.msgPrefix')\n\nconst wildcardFirstSym = Symbol('pino.wildcardFirst')\n\n// public symbols, no need to use the same pino\n// version for these\nconst serializersSym = Symbol.for('pino.serializers')\nconst formattersSym = Symbol.for('pino.formatters')\nconst hooksSym = Symbol.for('pino.hooks')\nconst needsMetadataGsym = Symbol.for('pino.metadata')\n\nmodule.exports = {\n  setLevelSym,\n  getLevelSym,\n  levelValSym,\n  levelCompSym,\n  useLevelLabelsSym,\n  mixinSym,\n  lsCacheSym,\n  chindingsSym,\n  asJsonSym,\n  writeSym,\n  serializersSym,\n  redactFmtSym,\n  timeSym,\n  timeSliceIndexSym,\n  streamSym,\n  stringifySym,\n  stringifySafeSym,\n  stringifiersSym,\n  endSym,\n  formatOptsSym,\n  messageKeySym,\n  errorKeySym,\n  nestedKeySym,\n  wildcardFirstSym,\n  needsMetadataGsym,\n  useOnlyCustomLevelsSym,\n  formattersSym,\n  hooksSym,\n  nestedKeyStrSym,\n  mixinMergeStrategySym,\n  msgPrefixSym\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pino/lib/symbols.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pino/lib/time.js":
/*!***************************************!*\
  !*** ./node_modules/pino/lib/time.js ***!
  \***************************************/
/***/ ((module) => {

"use strict";
eval("\n\nconst nullTime = () => ''\n\nconst epochTime = () => `,\"time\":${Date.now()}`\n\nconst unixTime = () => `,\"time\":${Math.round(Date.now() / 1000.0)}`\n\nconst isoTime = () => `,\"time\":\"${new Date(Date.now()).toISOString()}\"` // using Date.now() for testability\n\nmodule.exports = { nullTime, epochTime, unixTime, isoTime }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcGluby9saWIvdGltZS5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjs7QUFFQSxtQ0FBbUMsV0FBVzs7QUFFOUMsa0NBQWtDLGdDQUFnQzs7QUFFbEUsa0NBQWtDLG1DQUFtQzs7QUFFckUsbUJBQW1CIiwic291cmNlcyI6WyJDOlxcQ29kZVxcb25lLW1haWxcXG5vZGVfbW9kdWxlc1xccGlub1xcbGliXFx0aW1lLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5jb25zdCBudWxsVGltZSA9ICgpID0+ICcnXG5cbmNvbnN0IGVwb2NoVGltZSA9ICgpID0+IGAsXCJ0aW1lXCI6JHtEYXRlLm5vdygpfWBcblxuY29uc3QgdW5peFRpbWUgPSAoKSA9PiBgLFwidGltZVwiOiR7TWF0aC5yb3VuZChEYXRlLm5vdygpIC8gMTAwMC4wKX1gXG5cbmNvbnN0IGlzb1RpbWUgPSAoKSA9PiBgLFwidGltZVwiOlwiJHtuZXcgRGF0ZShEYXRlLm5vdygpKS50b0lTT1N0cmluZygpfVwiYCAvLyB1c2luZyBEYXRlLm5vdygpIGZvciB0ZXN0YWJpbGl0eVxuXG5tb2R1bGUuZXhwb3J0cyA9IHsgbnVsbFRpbWUsIGVwb2NoVGltZSwgdW5peFRpbWUsIGlzb1RpbWUgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pino/lib/time.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pino/lib/tools.js":
/*!****************************************!*\
  !*** ./node_modules/pino/lib/tools.js ***!
  \****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\n/* eslint no-prototype-builtins: 0 */\n\nconst format = __webpack_require__(/*! quick-format-unescaped */ \"(rsc)/./node_modules/quick-format-unescaped/index.js\")\nconst { mapHttpRequest, mapHttpResponse } = __webpack_require__(/*! pino-std-serializers */ \"(rsc)/./node_modules/pino-std-serializers/index.js\")\nconst SonicBoom = __webpack_require__(/*! sonic-boom */ \"(rsc)/./node_modules/sonic-boom/index.js\")\nconst onExit = __webpack_require__(/*! on-exit-leak-free */ \"(rsc)/./node_modules/on-exit-leak-free/index.js\")\nconst {\n  lsCacheSym,\n  chindingsSym,\n  writeSym,\n  serializersSym,\n  formatOptsSym,\n  endSym,\n  stringifiersSym,\n  stringifySym,\n  stringifySafeSym,\n  wildcardFirstSym,\n  nestedKeySym,\n  formattersSym,\n  messageKeySym,\n  errorKeySym,\n  nestedKeyStrSym,\n  msgPrefixSym\n} = __webpack_require__(/*! ./symbols */ \"(rsc)/./node_modules/pino/lib/symbols.js\")\nconst { isMainThread } = __webpack_require__(/*! worker_threads */ \"worker_threads\")\nconst transport = __webpack_require__(/*! ./transport */ \"(rsc)/./node_modules/pino/lib/transport.js\")\n\nfunction noop () {\n}\n\nfunction genLog (level, hook) {\n  if (!hook) return LOG\n\n  return function hookWrappedLog (...args) {\n    hook.call(this, args, LOG, level)\n  }\n\n  function LOG (o, ...n) {\n    if (typeof o === 'object') {\n      let msg = o\n      if (o !== null) {\n        if (o.method && o.headers && o.socket) {\n          o = mapHttpRequest(o)\n        } else if (typeof o.setHeader === 'function') {\n          o = mapHttpResponse(o)\n        }\n      }\n      let formatParams\n      if (msg === null && n.length === 0) {\n        formatParams = [null]\n      } else {\n        msg = n.shift()\n        formatParams = n\n      }\n      // We do not use a coercive check for `msg` as it is\n      // measurably slower than the explicit checks.\n      if (typeof this[msgPrefixSym] === 'string' && msg !== undefined && msg !== null) {\n        msg = this[msgPrefixSym] + msg\n      }\n      this[writeSym](o, format(msg, formatParams, this[formatOptsSym]), level)\n    } else {\n      let msg = o === undefined ? n.shift() : o\n\n      // We do not use a coercive check for `msg` as it is\n      // measurably slower than the explicit checks.\n      if (typeof this[msgPrefixSym] === 'string' && msg !== undefined && msg !== null) {\n        msg = this[msgPrefixSym] + msg\n      }\n      this[writeSym](null, format(msg, n, this[formatOptsSym]), level)\n    }\n  }\n}\n\n// magically escape strings for json\n// relying on their charCodeAt\n// everything below 32 needs JSON.stringify()\n// 34 and 92 happens all the time, so we\n// have a fast case for them\nfunction asString (str) {\n  let result = ''\n  let last = 0\n  let found = false\n  let point = 255\n  const l = str.length\n  if (l > 100) {\n    return JSON.stringify(str)\n  }\n  for (var i = 0; i < l && point >= 32; i++) {\n    point = str.charCodeAt(i)\n    if (point === 34 || point === 92) {\n      result += str.slice(last, i) + '\\\\'\n      last = i\n      found = true\n    }\n  }\n  if (!found) {\n    result = str\n  } else {\n    result += str.slice(last)\n  }\n  return point < 32 ? JSON.stringify(str) : '\"' + result + '\"'\n}\n\nfunction asJson (obj, msg, num, time) {\n  const stringify = this[stringifySym]\n  const stringifySafe = this[stringifySafeSym]\n  const stringifiers = this[stringifiersSym]\n  const end = this[endSym]\n  const chindings = this[chindingsSym]\n  const serializers = this[serializersSym]\n  const formatters = this[formattersSym]\n  const messageKey = this[messageKeySym]\n  const errorKey = this[errorKeySym]\n  let data = this[lsCacheSym][num] + time\n\n  // we need the child bindings added to the output first so instance logged\n  // objects can take precedence when JSON.parse-ing the resulting log line\n  data = data + chindings\n\n  let value\n  if (formatters.log) {\n    obj = formatters.log(obj)\n  }\n  const wildcardStringifier = stringifiers[wildcardFirstSym]\n  let propStr = ''\n  for (const key in obj) {\n    value = obj[key]\n    if (Object.prototype.hasOwnProperty.call(obj, key) && value !== undefined) {\n      if (serializers[key]) {\n        value = serializers[key](value)\n      } else if (key === errorKey && serializers.err) {\n        value = serializers.err(value)\n      }\n\n      const stringifier = stringifiers[key] || wildcardStringifier\n\n      switch (typeof value) {\n        case 'undefined':\n        case 'function':\n          continue\n        case 'number':\n          /* eslint no-fallthrough: \"off\" */\n          if (Number.isFinite(value) === false) {\n            value = null\n          }\n        // this case explicitly falls through to the next one\n        case 'boolean':\n          if (stringifier) value = stringifier(value)\n          break\n        case 'string':\n          value = (stringifier || asString)(value)\n          break\n        default:\n          value = (stringifier || stringify)(value, stringifySafe)\n      }\n      if (value === undefined) continue\n      const strKey = asString(key)\n      propStr += ',' + strKey + ':' + value\n    }\n  }\n\n  let msgStr = ''\n  if (msg !== undefined) {\n    value = serializers[messageKey] ? serializers[messageKey](msg) : msg\n    const stringifier = stringifiers[messageKey] || wildcardStringifier\n\n    switch (typeof value) {\n      case 'function':\n        break\n      case 'number':\n        /* eslint no-fallthrough: \"off\" */\n        if (Number.isFinite(value) === false) {\n          value = null\n        }\n      // this case explicitly falls through to the next one\n      case 'boolean':\n        if (stringifier) value = stringifier(value)\n        msgStr = ',\"' + messageKey + '\":' + value\n        break\n      case 'string':\n        value = (stringifier || asString)(value)\n        msgStr = ',\"' + messageKey + '\":' + value\n        break\n      default:\n        value = (stringifier || stringify)(value, stringifySafe)\n        msgStr = ',\"' + messageKey + '\":' + value\n    }\n  }\n\n  if (this[nestedKeySym] && propStr) {\n    // place all the obj properties under the specified key\n    // the nested key is already formatted from the constructor\n    return data + this[nestedKeyStrSym] + propStr.slice(1) + '}' + msgStr + end\n  } else {\n    return data + propStr + msgStr + end\n  }\n}\n\nfunction asChindings (instance, bindings) {\n  let value\n  let data = instance[chindingsSym]\n  const stringify = instance[stringifySym]\n  const stringifySafe = instance[stringifySafeSym]\n  const stringifiers = instance[stringifiersSym]\n  const wildcardStringifier = stringifiers[wildcardFirstSym]\n  const serializers = instance[serializersSym]\n  const formatter = instance[formattersSym].bindings\n  bindings = formatter(bindings)\n\n  for (const key in bindings) {\n    value = bindings[key]\n    const valid = key !== 'level' &&\n      key !== 'serializers' &&\n      key !== 'formatters' &&\n      key !== 'customLevels' &&\n      bindings.hasOwnProperty(key) &&\n      value !== undefined\n    if (valid === true) {\n      value = serializers[key] ? serializers[key](value) : value\n      value = (stringifiers[key] || wildcardStringifier || stringify)(value, stringifySafe)\n      if (value === undefined) continue\n      data += ',\"' + key + '\":' + value\n    }\n  }\n  return data\n}\n\nfunction hasBeenTampered (stream) {\n  return stream.write !== stream.constructor.prototype.write\n}\n\nfunction buildSafeSonicBoom (opts) {\n  const stream = new SonicBoom(opts)\n  stream.on('error', filterBrokenPipe)\n  // If we are sync: false, we must flush on exit\n  if (!opts.sync && isMainThread) {\n    onExit.register(stream, autoEnd)\n\n    stream.on('close', function () {\n      onExit.unregister(stream)\n    })\n  }\n  return stream\n\n  function filterBrokenPipe (err) {\n    // Impossible to replicate across all operating systems\n    /* istanbul ignore next */\n    if (err.code === 'EPIPE') {\n      // If we get EPIPE, we should stop logging here\n      // however we have no control to the consumer of\n      // SonicBoom, so we just overwrite the write method\n      stream.write = noop\n      stream.end = noop\n      stream.flushSync = noop\n      stream.destroy = noop\n      return\n    }\n    stream.removeListener('error', filterBrokenPipe)\n    stream.emit('error', err)\n  }\n}\n\nfunction autoEnd (stream, eventName) {\n  // This check is needed only on some platforms\n  /* istanbul ignore next */\n  if (stream.destroyed) {\n    return\n  }\n\n  if (eventName === 'beforeExit') {\n    // We still have an event loop, let's use it\n    stream.flush()\n    stream.on('drain', function () {\n      stream.end()\n    })\n  } else {\n    // For some reason istanbul is not detecting this, but it's there\n    /* istanbul ignore next */\n    // We do not have an event loop, so flush synchronously\n    stream.flushSync()\n  }\n}\n\nfunction createArgsNormalizer (defaultOptions) {\n  return function normalizeArgs (instance, caller, opts = {}, stream) {\n    // support stream as a string\n    if (typeof opts === 'string') {\n      stream = buildSafeSonicBoom({ dest: opts })\n      opts = {}\n    } else if (typeof stream === 'string') {\n      if (opts && opts.transport) {\n        throw Error('only one of option.transport or stream can be specified')\n      }\n      stream = buildSafeSonicBoom({ dest: stream })\n    } else if (opts instanceof SonicBoom || opts.writable || opts._writableState) {\n      stream = opts\n      opts = {}\n    } else if (opts.transport) {\n      if (opts.transport instanceof SonicBoom || opts.transport.writable || opts.transport._writableState) {\n        throw Error('option.transport do not allow stream, please pass to option directly. e.g. pino(transport)')\n      }\n      if (opts.transport.targets && opts.transport.targets.length && opts.formatters && typeof opts.formatters.level === 'function') {\n        throw Error('option.transport.targets do not allow custom level formatters')\n      }\n\n      let customLevels\n      if (opts.customLevels) {\n        customLevels = opts.useOnlyCustomLevels ? opts.customLevels : Object.assign({}, opts.levels, opts.customLevels)\n      }\n      stream = transport({ caller, ...opts.transport, levels: customLevels })\n    }\n    opts = Object.assign({}, defaultOptions, opts)\n    opts.serializers = Object.assign({}, defaultOptions.serializers, opts.serializers)\n    opts.formatters = Object.assign({}, defaultOptions.formatters, opts.formatters)\n\n    if (opts.prettyPrint) {\n      throw new Error('prettyPrint option is no longer supported, see the pino-pretty package (https://github.com/pinojs/pino-pretty)')\n    }\n\n    const { enabled, onChild } = opts\n    if (enabled === false) opts.level = 'silent'\n    if (!onChild) opts.onChild = noop\n    if (!stream) {\n      if (!hasBeenTampered(process.stdout)) {\n        // If process.stdout.fd is undefined, it means that we are running\n        // in a worker thread. Let's assume we are logging to file descriptor 1.\n        stream = buildSafeSonicBoom({ fd: process.stdout.fd || 1 })\n      } else {\n        stream = process.stdout\n      }\n    }\n    return { opts, stream }\n  }\n}\n\nfunction stringify (obj, stringifySafeFn) {\n  try {\n    return JSON.stringify(obj)\n  } catch (_) {\n    try {\n      const stringify = stringifySafeFn || this[stringifySafeSym]\n      return stringify(obj)\n    } catch (_) {\n      return '\"[unable to serialize, circular reference is too complex to analyze]\"'\n    }\n  }\n}\n\nfunction buildFormatters (level, bindings, log) {\n  return {\n    level,\n    bindings,\n    log\n  }\n}\n\n/**\n * Convert a string integer file descriptor to a proper native integer\n * file descriptor.\n *\n * @param {string} destination The file descriptor string to attempt to convert.\n *\n * @returns {Number}\n */\nfunction normalizeDestFileDescriptor (destination) {\n  const fd = Number(destination)\n  if (typeof destination === 'string' && Number.isFinite(fd)) {\n    return fd\n  }\n  // destination could be undefined if we are in a worker\n  if (destination === undefined) {\n    // This is stdout in UNIX systems\n    return 1\n  }\n  return destination\n}\n\nmodule.exports = {\n  noop,\n  buildSafeSonicBoom,\n  asChindings,\n  asJson,\n  genLog,\n  createArgsNormalizer,\n  stringify,\n  buildFormatters,\n  normalizeDestFileDescriptor\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pino/lib/tools.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pino/lib/transport.js":
/*!********************************************!*\
  !*** ./node_modules/pino/lib/transport.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nconst { createRequire } = __webpack_require__(/*! module */ \"module\")\nconst getCallers = __webpack_require__(/*! ./caller */ \"(rsc)/./node_modules/pino/lib/caller.js\")\nconst { join, isAbsolute, sep } = __webpack_require__(/*! node:path */ \"node:path\")\nconst sleep = __webpack_require__(/*! atomic-sleep */ \"(rsc)/./node_modules/atomic-sleep/index.js\")\nconst onExit = __webpack_require__(/*! on-exit-leak-free */ \"(rsc)/./node_modules/on-exit-leak-free/index.js\")\nconst ThreadStream = __webpack_require__(/*! thread-stream */ \"(rsc)/./node_modules/thread-stream/index.js\")\n\nfunction setupOnExit (stream) {\n  // This is leak free, it does not leave event handlers\n  onExit.register(stream, autoEnd)\n  onExit.registerBeforeExit(stream, flush)\n\n  stream.on('close', function () {\n    onExit.unregister(stream)\n  })\n}\n\nfunction buildStream (filename, workerData, workerOpts, sync) {\n  const stream = new ThreadStream({\n    filename,\n    workerData,\n    workerOpts,\n    sync\n  })\n\n  stream.on('ready', onReady)\n  stream.on('close', function () {\n    process.removeListener('exit', onExit)\n  })\n\n  process.on('exit', onExit)\n\n  function onReady () {\n    process.removeListener('exit', onExit)\n    stream.unref()\n\n    if (workerOpts.autoEnd !== false) {\n      setupOnExit(stream)\n    }\n  }\n\n  function onExit () {\n    /* istanbul ignore next */\n    if (stream.closed) {\n      return\n    }\n    stream.flushSync()\n    // Apparently there is a very sporadic race condition\n    // that in certain OS would prevent the messages to be flushed\n    // because the thread might not have been created still.\n    // Unfortunately we need to sleep(100) in this case.\n    sleep(100)\n    stream.end()\n  }\n\n  return stream\n}\n\nfunction autoEnd (stream) {\n  stream.ref()\n  stream.flushSync()\n  stream.end()\n  stream.once('close', function () {\n    stream.unref()\n  })\n}\n\nfunction flush (stream) {\n  stream.flushSync()\n}\n\nfunction transport (fullOptions) {\n  const { pipeline, targets, levels, dedupe, worker = {}, caller = getCallers(), sync = false } = fullOptions\n\n  const options = {\n    ...fullOptions.options\n  }\n\n  // Backwards compatibility\n  const callers = typeof caller === 'string' ? [caller] : caller\n\n  // This will be eventually modified by bundlers\n  const bundlerOverrides = '__bundlerPathsOverrides' in globalThis ? globalThis.__bundlerPathsOverrides : {}\n\n  let target = fullOptions.target\n\n  if (target && targets) {\n    throw new Error('only one of target or targets can be specified')\n  }\n\n  if (targets) {\n    target = bundlerOverrides['pino-worker'] || join(__dirname, 'worker.js')\n    options.targets = targets.filter(dest => dest.target).map((dest) => {\n      return {\n        ...dest,\n        target: fixTarget(dest.target)\n      }\n    })\n    options.pipelines = targets.filter(dest => dest.pipeline).map((dest) => {\n      return dest.pipeline.map((t) => {\n        return {\n          ...t,\n          level: dest.level, // duplicate the pipeline `level` property defined in the upper level\n          target: fixTarget(t.target)\n        }\n      })\n    })\n  } else if (pipeline) {\n    target = bundlerOverrides['pino-worker'] || join(__dirname, 'worker.js')\n    options.pipelines = [pipeline.map((dest) => {\n      return {\n        ...dest,\n        target: fixTarget(dest.target)\n      }\n    })]\n  }\n\n  if (levels) {\n    options.levels = levels\n  }\n\n  if (dedupe) {\n    options.dedupe = dedupe\n  }\n\n  options.pinoWillSendConfig = true\n\n  return buildStream(fixTarget(target), options, worker, sync)\n\n  function fixTarget (origin) {\n    origin = bundlerOverrides[origin] || origin\n\n    if (isAbsolute(origin) || origin.indexOf('file://') === 0) {\n      return origin\n    }\n\n    if (origin === 'pino/file') {\n      return join(__dirname, '..', 'file.js')\n    }\n\n    let fixTarget\n\n    for (const filePath of callers) {\n      try {\n        const context = filePath === 'node:repl'\n          ? process.cwd() + sep\n          : filePath\n\n        fixTarget = createRequire(context).resolve(origin)\n        break\n      } catch (err) {\n        // Silent catch\n        continue\n      }\n    }\n\n    if (!fixTarget) {\n      throw new Error(`unable to determine transport target for \"${origin}\"`)\n    }\n\n    return fixTarget\n  }\n}\n\nmodule.exports = transport\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pino/lib/transport.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/pino/pino.js":
/*!***********************************!*\
  !*** ./node_modules/pino/pino.js ***!
  \***********************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nconst os = __webpack_require__(/*! node:os */ \"node:os\")\nconst stdSerializers = __webpack_require__(/*! pino-std-serializers */ \"(rsc)/./node_modules/pino-std-serializers/index.js\")\nconst caller = __webpack_require__(/*! ./lib/caller */ \"(rsc)/./node_modules/pino/lib/caller.js\")\nconst redaction = __webpack_require__(/*! ./lib/redaction */ \"(rsc)/./node_modules/pino/lib/redaction.js\")\nconst time = __webpack_require__(/*! ./lib/time */ \"(rsc)/./node_modules/pino/lib/time.js\")\nconst proto = __webpack_require__(/*! ./lib/proto */ \"(rsc)/./node_modules/pino/lib/proto.js\")\nconst symbols = __webpack_require__(/*! ./lib/symbols */ \"(rsc)/./node_modules/pino/lib/symbols.js\")\nconst { configure } = __webpack_require__(/*! safe-stable-stringify */ \"(rsc)/./node_modules/safe-stable-stringify/index.js\")\nconst { assertDefaultLevelFound, mappings, genLsCache, genLevelComparison, assertLevelComparison } = __webpack_require__(/*! ./lib/levels */ \"(rsc)/./node_modules/pino/lib/levels.js\")\nconst { DEFAULT_LEVELS, SORTING_ORDER } = __webpack_require__(/*! ./lib/constants */ \"(rsc)/./node_modules/pino/lib/constants.js\")\nconst {\n  createArgsNormalizer,\n  asChindings,\n  buildSafeSonicBoom,\n  buildFormatters,\n  stringify,\n  normalizeDestFileDescriptor,\n  noop\n} = __webpack_require__(/*! ./lib/tools */ \"(rsc)/./node_modules/pino/lib/tools.js\")\nconst { version } = __webpack_require__(/*! ./lib/meta */ \"(rsc)/./node_modules/pino/lib/meta.js\")\nconst {\n  chindingsSym,\n  redactFmtSym,\n  serializersSym,\n  timeSym,\n  timeSliceIndexSym,\n  streamSym,\n  stringifySym,\n  stringifySafeSym,\n  stringifiersSym,\n  setLevelSym,\n  endSym,\n  formatOptsSym,\n  messageKeySym,\n  errorKeySym,\n  nestedKeySym,\n  mixinSym,\n  levelCompSym,\n  useOnlyCustomLevelsSym,\n  formattersSym,\n  hooksSym,\n  nestedKeyStrSym,\n  mixinMergeStrategySym,\n  msgPrefixSym\n} = symbols\nconst { epochTime, nullTime } = time\nconst { pid } = process\nconst hostname = os.hostname()\nconst defaultErrorSerializer = stdSerializers.err\nconst defaultOptions = {\n  level: 'info',\n  levelComparison: SORTING_ORDER.ASC,\n  levels: DEFAULT_LEVELS,\n  messageKey: 'msg',\n  errorKey: 'err',\n  nestedKey: null,\n  enabled: true,\n  base: { pid, hostname },\n  serializers: Object.assign(Object.create(null), {\n    err: defaultErrorSerializer\n  }),\n  formatters: Object.assign(Object.create(null), {\n    bindings (bindings) {\n      return bindings\n    },\n    level (label, number) {\n      return { level: number }\n    }\n  }),\n  hooks: {\n    logMethod: undefined,\n    streamWrite: undefined\n  },\n  timestamp: epochTime,\n  name: undefined,\n  redact: null,\n  customLevels: null,\n  useOnlyCustomLevels: false,\n  depthLimit: 5,\n  edgeLimit: 100\n}\n\nconst normalize = createArgsNormalizer(defaultOptions)\n\nconst serializers = Object.assign(Object.create(null), stdSerializers)\n\nfunction pino (...args) {\n  const instance = {}\n  const { opts, stream } = normalize(instance, caller(), ...args)\n\n  if (opts.level && typeof opts.level === 'string' && DEFAULT_LEVELS[opts.level.toLowerCase()] !== undefined) opts.level = opts.level.toLowerCase()\n\n  const {\n    redact,\n    crlf,\n    serializers,\n    timestamp,\n    messageKey,\n    errorKey,\n    nestedKey,\n    base,\n    name,\n    level,\n    customLevels,\n    levelComparison,\n    mixin,\n    mixinMergeStrategy,\n    useOnlyCustomLevels,\n    formatters,\n    hooks,\n    depthLimit,\n    edgeLimit,\n    onChild,\n    msgPrefix\n  } = opts\n\n  const stringifySafe = configure({\n    maximumDepth: depthLimit,\n    maximumBreadth: edgeLimit\n  })\n\n  const allFormatters = buildFormatters(\n    formatters.level,\n    formatters.bindings,\n    formatters.log\n  )\n\n  const stringifyFn = stringify.bind({\n    [stringifySafeSym]: stringifySafe\n  })\n  const stringifiers = redact ? redaction(redact, stringifyFn) : {}\n  const formatOpts = redact\n    ? { stringify: stringifiers[redactFmtSym] }\n    : { stringify: stringifyFn }\n  const end = '}' + (crlf ? '\\r\\n' : '\\n')\n  const coreChindings = asChindings.bind(null, {\n    [chindingsSym]: '',\n    [serializersSym]: serializers,\n    [stringifiersSym]: stringifiers,\n    [stringifySym]: stringify,\n    [stringifySafeSym]: stringifySafe,\n    [formattersSym]: allFormatters\n  })\n\n  let chindings = ''\n  if (base !== null) {\n    if (name === undefined) {\n      chindings = coreChindings(base)\n    } else {\n      chindings = coreChindings(Object.assign({}, base, { name }))\n    }\n  }\n\n  const time = (timestamp instanceof Function)\n    ? timestamp\n    : (timestamp ? epochTime : nullTime)\n  const timeSliceIndex = time().indexOf(':') + 1\n\n  if (useOnlyCustomLevels && !customLevels) throw Error('customLevels is required if useOnlyCustomLevels is set true')\n  if (mixin && typeof mixin !== 'function') throw Error(`Unknown mixin type \"${typeof mixin}\" - expected \"function\"`)\n  if (msgPrefix && typeof msgPrefix !== 'string') throw Error(`Unknown msgPrefix type \"${typeof msgPrefix}\" - expected \"string\"`)\n\n  assertDefaultLevelFound(level, customLevels, useOnlyCustomLevels)\n  const levels = mappings(customLevels, useOnlyCustomLevels)\n\n  if (typeof stream.emit === 'function') {\n    stream.emit('message', { code: 'PINO_CONFIG', config: { levels, messageKey, errorKey } })\n  }\n\n  assertLevelComparison(levelComparison)\n  const levelCompFunc = genLevelComparison(levelComparison)\n\n  Object.assign(instance, {\n    levels,\n    [levelCompSym]: levelCompFunc,\n    [useOnlyCustomLevelsSym]: useOnlyCustomLevels,\n    [streamSym]: stream,\n    [timeSym]: time,\n    [timeSliceIndexSym]: timeSliceIndex,\n    [stringifySym]: stringify,\n    [stringifySafeSym]: stringifySafe,\n    [stringifiersSym]: stringifiers,\n    [endSym]: end,\n    [formatOptsSym]: formatOpts,\n    [messageKeySym]: messageKey,\n    [errorKeySym]: errorKey,\n    [nestedKeySym]: nestedKey,\n    // protect against injection\n    [nestedKeyStrSym]: nestedKey ? `,${JSON.stringify(nestedKey)}:{` : '',\n    [serializersSym]: serializers,\n    [mixinSym]: mixin,\n    [mixinMergeStrategySym]: mixinMergeStrategy,\n    [chindingsSym]: chindings,\n    [formattersSym]: allFormatters,\n    [hooksSym]: hooks,\n    silent: noop,\n    onChild,\n    [msgPrefixSym]: msgPrefix\n  })\n\n  Object.setPrototypeOf(instance, proto())\n\n  genLsCache(instance)\n\n  instance[setLevelSym](level)\n\n  return instance\n}\n\nmodule.exports = pino\n\nmodule.exports.destination = (dest = process.stdout.fd) => {\n  if (typeof dest === 'object') {\n    dest.dest = normalizeDestFileDescriptor(dest.dest || process.stdout.fd)\n    return buildSafeSonicBoom(dest)\n  } else {\n    return buildSafeSonicBoom({ dest: normalizeDestFileDescriptor(dest), minLength: 0 })\n  }\n}\n\nmodule.exports.transport = __webpack_require__(/*! ./lib/transport */ \"(rsc)/./node_modules/pino/lib/transport.js\")\nmodule.exports.multistream = __webpack_require__(/*! ./lib/multistream */ \"(rsc)/./node_modules/pino/lib/multistream.js\")\n\nmodule.exports.levels = mappings()\nmodule.exports.stdSerializers = serializers\nmodule.exports.stdTimeFunctions = Object.assign({}, time)\nmodule.exports.symbols = symbols\nmodule.exports.version = version\n\n// Enables default and name export with TypeScript and Babel\nmodule.exports[\"default\"] = pino\nmodule.exports.pino = pino\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pino/pino.js\n");

/***/ })

};
;