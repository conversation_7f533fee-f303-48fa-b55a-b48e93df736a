/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/layout"],{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"ac4a055b9b66\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxDb2RlXFxvbmUtbWFpbFxcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImFjNGEwNTViOWI2NlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/auth-provider.tsx":
/*!**************************************!*\
  !*** ./components/auth-provider.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var keycloak_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! keycloak-js */ \"(app-pages-browser)/./node_modules/keycloak-js/lib/keycloak.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.createContext)(null);\nconst PUBLIC_URL = process.env.NEXT_PUBLIC_KEYCLOAK_URL;\nconst PUBLIC_REALM = process.env.NEXT_PUBLIC_KEYCLOAK_REALM;\nconst PUBLIC_CLIENT_ID = process.env.NEXT_PUBLIC_KEYCLOAK_CLIENT_ID;\nconst REQUIRED_ROLE = process.env.NEXT_PUBLIC_REQUIRED_ROLE || \"mail-reader\";\nfunction createKeycloak() {\n    if (!PUBLIC_URL || !PUBLIC_REALM || !PUBLIC_CLIENT_ID) return null;\n    const config = {\n        url: PUBLIC_URL,\n        realm: PUBLIC_REALM,\n        clientId: PUBLIC_CLIENT_ID\n    };\n    return new keycloak_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"](config);\n}\nconst DEMO_KEY = \"mail-demo-enabled\";\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [keycloak, setKeycloak] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [authenticated, setAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [initializing, setInitializing] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [idToken, setIdToken] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isDemo, setIsDemo] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const refreshTimer = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const configOk = Boolean(PUBLIC_URL && PUBLIC_REALM && PUBLIC_CLIENT_ID);\n    // Demo bootstrap\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            if ( true && window.localStorage.getItem(DEMO_KEY) === \"1\") {\n                enableDemo();\n                setInitializing(false);\n                return;\n            }\n            const kc = createKeycloak();\n            setKeycloak(kc);\n            if (!kc) {\n                // No Keycloak config present: stay unauthenticated until user chooses demo\n                setInitializing(false);\n                return;\n            }\n            kc.init({\n                onLoad: \"check-sso\",\n                pkceMethod: \"S256\",\n                silentCheckSsoRedirectUri:  true ? \"\".concat(window.location.origin, \"/silent-check-sso.html\") : 0,\n                checkLoginIframe: false\n            }).then({\n                \"AuthProvider.useEffect\": async (auth)=>{\n                    setAuthenticated(auth);\n                    if (auth) {\n                        var _kc_token;\n                        setToken((_kc_token = kc.token) !== null && _kc_token !== void 0 ? _kc_token : null);\n                        var _kc_idToken;\n                        setIdToken((_kc_idToken = kc.idToken) !== null && _kc_idToken !== void 0 ? _kc_idToken : null);\n                        try {\n                            const prof = await kc.loadUserProfile();\n                            setProfile(prof);\n                        } catch (e) {\n                        // ignore\n                        }\n                        scheduleRefresh(kc);\n                    }\n                }\n            }[\"AuthProvider.useEffect\"]).finally({\n                \"AuthProvider.useEffect\": ()=>setInitializing(false)\n            }[\"AuthProvider.useEffect\"]);\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    if (refreshTimer.current) window.clearInterval(refreshTimer.current);\n                }\n            })[\"AuthProvider.useEffect\"];\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const scheduleRefresh = (kc)=>{\n        if (refreshTimer.current) window.clearInterval(refreshTimer.current);\n        refreshTimer.current = window.setInterval(async ()=>{\n            try {\n                const refreshed = await kc.updateToken(30);\n                if (refreshed) {\n                    var _kc_token;\n                    setToken((_kc_token = kc.token) !== null && _kc_token !== void 0 ? _kc_token : null);\n                    var _kc_idToken;\n                    setIdToken((_kc_idToken = kc.idToken) !== null && _kc_idToken !== void 0 ? _kc_idToken : null);\n                }\n            } catch (e) {\n            // token refresh failed\n            }\n        }, 30000);\n    };\n    const login = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"AuthProvider.useCallback[login]\": ()=>{\n            keycloak === null || keycloak === void 0 ? void 0 : keycloak.login();\n        }\n    }[\"AuthProvider.useCallback[login]\"], [\n        keycloak\n    ]);\n    const logout = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"AuthProvider.useCallback[logout]\": ()=>{\n            if (isDemo && \"object\" !== \"undefined\") {\n                window.localStorage.removeItem(DEMO_KEY);\n                setIsDemo(false);\n                setAuthenticated(false);\n                setToken(null);\n                setIdToken(null);\n                setProfile(null);\n                return;\n            }\n            keycloak === null || keycloak === void 0 ? void 0 : keycloak.logout();\n        }\n    }[\"AuthProvider.useCallback[logout]\"], [\n        keycloak,\n        isDemo\n    ]);\n    const roles = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)({\n        \"AuthProvider.useMemo[roles]\": ()=>{\n            var _keycloak_realmAccess, _keycloak_resourceAccess_, _keycloak_resourceAccess;\n            if (isDemo) return new Set([\n                REQUIRED_ROLE\n            ]);\n            var _keycloak_realmAccess_roles;\n            const realmRoles = (_keycloak_realmAccess_roles = keycloak === null || keycloak === void 0 ? void 0 : (_keycloak_realmAccess = keycloak.realmAccess) === null || _keycloak_realmAccess === void 0 ? void 0 : _keycloak_realmAccess.roles) !== null && _keycloak_realmAccess_roles !== void 0 ? _keycloak_realmAccess_roles : [];\n            var _keycloak_resourceAccess__roles;\n            const clientRoles = (_keycloak_resourceAccess__roles = keycloak === null || keycloak === void 0 ? void 0 : (_keycloak_resourceAccess = keycloak.resourceAccess) === null || _keycloak_resourceAccess === void 0 ? void 0 : (_keycloak_resourceAccess_ = _keycloak_resourceAccess[PUBLIC_CLIENT_ID || \"\"]) === null || _keycloak_resourceAccess_ === void 0 ? void 0 : _keycloak_resourceAccess_.roles) !== null && _keycloak_resourceAccess__roles !== void 0 ? _keycloak_resourceAccess__roles : [];\n            return new Set([\n                ...realmRoles,\n                ...clientRoles\n            ]);\n        }\n    }[\"AuthProvider.useMemo[roles]\"], [\n        keycloak,\n        isDemo\n    ]);\n    const hasRequiredRole = roles.has(REQUIRED_ROLE);\n    const getAuthHeader = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"AuthProvider.useCallback[getAuthHeader]\": async ()=>{\n            if (isDemo) return \"Bearer demo\";\n            if (!keycloak) return null;\n            try {\n                await keycloak.updateToken(30);\n                return keycloak.token ? \"Bearer \".concat(keycloak.token) : null;\n            } catch (e) {\n                return token ? \"Bearer \".concat(token) : null;\n            }\n        }\n    }[\"AuthProvider.useCallback[getAuthHeader]\"], [\n        keycloak,\n        token,\n        isDemo\n    ]);\n    function enableDemo() {\n        setIsDemo(true);\n        setAuthenticated(true);\n        setToken(\"demo\");\n        setIdToken(\"demo-id\");\n        setProfile({\n            id: \"demo\",\n            username: \"demo\",\n            email: \"<EMAIL>\",\n            firstName: \"Demo\",\n            lastName: \"User\",\n            attributes: {}\n        });\n        if (true) {\n            window.localStorage.setItem(DEMO_KEY, \"1\");\n        }\n    }\n    const value = {\n        keycloak,\n        token,\n        idToken,\n        profile,\n        authenticated,\n        initializing,\n        hasRequiredRole,\n        requiredRole: REQUIRED_ROLE,\n        configOk,\n        isDemo,\n        login,\n        logout,\n        enableDemo,\n        getAuthHeader\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Code\\\\one-mail\\\\components\\\\auth-provider.tsx\",\n        lineNumber: 187,\n        columnNumber: 10\n    }, undefined);\n};\n_s(AuthProvider, \"z0ta+fnEh1H0sYrxVMdrLS3WtSc=\");\n_c = AuthProvider;\nfunction useAuth() {\n    _s1();\n    const ctx = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(AuthContext);\n    if (!ctx) {\n        throw new Error(\"useAuth must be used within AuthProvider\");\n    }\n    return ctx;\n}\n_s1(useAuth, \"/dMy7t63NXD4eYACoT93CePwGrg=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/auth-provider.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(app-pages-browser)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider(param) {\n    let { children, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Code\\\\one-mail\\\\components\\\\theme-provider.tsx\",\n        lineNumber: 10,\n        columnNumber: 10\n    }, this);\n}\n_c = ThemeProvider;\nvar _c;\n$RefreshReg$(_c, \"ThemeProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvdGhlbWUtcHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUU4QjtBQUlWO0FBRWIsU0FBU0MsY0FBYyxLQUEwQztRQUExQyxFQUFFRSxRQUFRLEVBQUUsR0FBR0MsT0FBMkIsR0FBMUM7SUFDNUIscUJBQU8sOERBQUNGLHNEQUFrQkE7UUFBRSxHQUFHRSxLQUFLO2tCQUFHRDs7Ozs7O0FBQ3pDO0tBRmdCRiIsInNvdXJjZXMiOlsiQzpcXENvZGVcXG9uZS1tYWlsXFxjb21wb25lbnRzXFx0aGVtZS1wcm92aWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHtcbiAgVGhlbWVQcm92aWRlciBhcyBOZXh0VGhlbWVzUHJvdmlkZXIsXG4gIHR5cGUgVGhlbWVQcm92aWRlclByb3BzLFxufSBmcm9tICduZXh0LXRoZW1lcydcblxuZXhwb3J0IGZ1bmN0aW9uIFRoZW1lUHJvdmlkZXIoeyBjaGlsZHJlbiwgLi4ucHJvcHMgfTogVGhlbWVQcm92aWRlclByb3BzKSB7XG4gIHJldHVybiA8TmV4dFRoZW1lc1Byb3ZpZGVyIHsuLi5wcm9wc30+e2NoaWxkcmVufTwvTmV4dFRoZW1lc1Byb3ZpZGVyPlxufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiVGhlbWVQcm92aWRlciIsIk5leHRUaGVtZXNQcm92aWRlciIsImNoaWxkcmVuIiwicHJvcHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/theme-provider.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/keycloak-js/lib/keycloak.js":
/*!**************************************************!*\
  !*** ./node_modules/keycloak-js/lib/keycloak.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/*\n * Copyright 2016 Red Hat, Inc. and/or its affiliates\n * and other contributors as indicated by the <AUTHOR> *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction Keycloak (config) {\n    if (!(this instanceof Keycloak)) {\n        throw new Error(\"The 'Keycloak' constructor must be invoked with 'new'.\")\n    }\n\n    if (typeof config !== 'string' && !isObject(config)) {\n        throw new Error(\"The 'Keycloak' constructor must be provided with a configuration object, or a URL to a JSON configuration file.\");\n    }\n\n    if (isObject(config)) {\n        const requiredProperties = 'oidcProvider' in config\n            ? ['clientId']\n            : ['url', 'realm', 'clientId'];\n\n        for (const property of requiredProperties) {\n            if (!config[property]) {\n                throw new Error(`The configuration object is missing the required '${property}' property.`);\n            }\n        }\n    }\n\n    var kc = this;\n    var adapter;\n    var refreshQueue = [];\n    var callbackStorage;\n\n    var loginIframe = {\n        enable: true,\n        callbackList: [],\n        interval: 5\n    };\n\n    kc.didInitialize = false;\n\n    var useNonce = true;\n    var logInfo = createLogger(console.info);\n    var logWarn = createLogger(console.warn);\n\n    if (!globalThis.isSecureContext) {\n        logWarn(\n            \"[KEYCLOAK] Keycloak JS must be used in a 'secure context' to function properly as it relies on browser APIs that are otherwise not available.\\n\" +\n            \"Continuing to run your application insecurely will lead to unexpected behavior and breakage.\\n\\n\" +\n            \"For more information see: https://developer.mozilla.org/en-US/docs/Web/Security/Secure_Contexts\"\n        );\n    }\n\n    kc.init = function (initOptions = {}) {\n        if (kc.didInitialize) {\n            throw new Error(\"A 'Keycloak' instance can only be initialized once.\");\n        }\n\n        kc.didInitialize = true;\n\n        kc.authenticated = false;\n\n        callbackStorage = createCallbackStorage();\n        var adapters = ['default', 'cordova', 'cordova-native'];\n\n        if (adapters.indexOf(initOptions.adapter) > -1) {\n            adapter = loadAdapter(initOptions.adapter);\n        } else if (typeof initOptions.adapter === \"object\") {\n            adapter = initOptions.adapter;\n        } else {\n            if (window.Cordova || window.cordova) {\n                adapter = loadAdapter('cordova');\n            } else {\n                adapter = loadAdapter();\n            }\n        }\n\n        if (typeof initOptions.useNonce !== 'undefined') {\n            useNonce = initOptions.useNonce;\n        }\n\n        if (typeof initOptions.checkLoginIframe !== 'undefined') {\n            loginIframe.enable = initOptions.checkLoginIframe;\n        }\n\n        if (initOptions.checkLoginIframeInterval) {\n            loginIframe.interval = initOptions.checkLoginIframeInterval;\n        }\n\n        if (initOptions.onLoad === 'login-required') {\n            kc.loginRequired = true;\n        }\n\n        if (initOptions.responseMode) {\n            if (initOptions.responseMode === 'query' || initOptions.responseMode === 'fragment') {\n                kc.responseMode = initOptions.responseMode;\n            } else {\n                throw 'Invalid value for responseMode';\n            }\n        }\n\n        if (initOptions.flow) {\n            switch (initOptions.flow) {\n                case 'standard':\n                    kc.responseType = 'code';\n                    break;\n                case 'implicit':\n                    kc.responseType = 'id_token token';\n                    break;\n                case 'hybrid':\n                    kc.responseType = 'code id_token token';\n                    break;\n                default:\n                    throw 'Invalid value for flow';\n            }\n            kc.flow = initOptions.flow;\n        }\n\n        if (initOptions.timeSkew != null) {\n            kc.timeSkew = initOptions.timeSkew;\n        }\n\n        if(initOptions.redirectUri) {\n            kc.redirectUri = initOptions.redirectUri;\n        }\n\n        if (initOptions.silentCheckSsoRedirectUri) {\n            kc.silentCheckSsoRedirectUri = initOptions.silentCheckSsoRedirectUri;\n        }\n\n        if (typeof initOptions.silentCheckSsoFallback === 'boolean') {\n            kc.silentCheckSsoFallback = initOptions.silentCheckSsoFallback;\n        } else {\n            kc.silentCheckSsoFallback = true;\n        }\n\n        if (typeof initOptions.pkceMethod !== \"undefined\") {\n            if (initOptions.pkceMethod !== \"S256\" && initOptions.pkceMethod !== false) {\n                throw new TypeError(`Invalid value for pkceMethod', expected 'S256' or false but got ${initOptions.pkceMethod}.`);\n            }\n\n            kc.pkceMethod = initOptions.pkceMethod;\n        } else {\n            kc.pkceMethod = \"S256\";\n        }\n\n        if (typeof initOptions.enableLogging === 'boolean') {\n            kc.enableLogging = initOptions.enableLogging;\n        } else {\n            kc.enableLogging = false;\n        }\n\n        if (initOptions.logoutMethod === 'POST') {\n            kc.logoutMethod = 'POST';\n        } else {\n            kc.logoutMethod = 'GET';\n        }\n\n        if (typeof initOptions.scope === 'string') {\n            kc.scope = initOptions.scope;\n        }\n\n        if (typeof initOptions.acrValues === 'string') {\n            kc.acrValues = initOptions.acrValues;\n        }\n\n        if (typeof initOptions.messageReceiveTimeout === 'number' && initOptions.messageReceiveTimeout > 0) {\n            kc.messageReceiveTimeout = initOptions.messageReceiveTimeout;\n        } else {\n            kc.messageReceiveTimeout = 10000;\n        }\n\n        if (!kc.responseMode) {\n            kc.responseMode = 'fragment';\n        }\n        if (!kc.responseType) {\n            kc.responseType = 'code';\n            kc.flow = 'standard';\n        }\n\n        var promise = createPromise();\n\n        var initPromise = createPromise();\n        initPromise.promise.then(function() {\n            kc.onReady && kc.onReady(kc.authenticated);\n            promise.setSuccess(kc.authenticated);\n        }).catch(function(error) {\n            promise.setError(error);\n        });\n\n        var configPromise = loadConfig();\n\n        function onLoad() {\n            var doLogin = function(prompt) {\n                if (!prompt) {\n                    options.prompt = 'none';\n                }\n\n                if (initOptions.locale) {\n                    options.locale = initOptions.locale;\n                }\n                kc.login(options).then(function () {\n                    initPromise.setSuccess();\n                }).catch(function (error) {\n                    initPromise.setError(error);\n                });\n            }\n\n            var checkSsoSilently = async function() {\n                var ifrm = document.createElement(\"iframe\");\n                var src = await kc.createLoginUrl({prompt: 'none', redirectUri: kc.silentCheckSsoRedirectUri});\n                ifrm.setAttribute(\"src\", src);\n                ifrm.setAttribute(\"sandbox\", \"allow-storage-access-by-user-activation allow-scripts allow-same-origin\");\n                ifrm.setAttribute(\"title\", \"keycloak-silent-check-sso\");\n                ifrm.style.display = \"none\";\n                document.body.appendChild(ifrm);\n\n                var messageCallback = function(event) {\n                    if (event.origin !== window.location.origin || ifrm.contentWindow !== event.source) {\n                        return;\n                    }\n\n                    var oauth = parseCallback(event.data);\n                    processCallback(oauth, initPromise);\n\n                    document.body.removeChild(ifrm);\n                    window.removeEventListener(\"message\", messageCallback);\n                };\n\n                window.addEventListener(\"message\", messageCallback);\n            };\n\n            var options = {};\n            switch (initOptions.onLoad) {\n                case 'check-sso':\n                    if (loginIframe.enable) {\n                        setupCheckLoginIframe().then(function() {\n                            checkLoginIframe().then(function (unchanged) {\n                                if (!unchanged) {\n                                    kc.silentCheckSsoRedirectUri ? checkSsoSilently() : doLogin(false);\n                                } else {\n                                    initPromise.setSuccess();\n                                }\n                            }).catch(function (error) {\n                                initPromise.setError(error);\n                            });\n                        });\n                    } else {\n                        kc.silentCheckSsoRedirectUri ? checkSsoSilently() : doLogin(false);\n                    }\n                    break;\n                case 'login-required':\n                    doLogin(true);\n                    break;\n                default:\n                    throw 'Invalid value for onLoad';\n            }\n        }\n\n        function processInit() {\n            var callback = parseCallback(window.location.href);\n\n            if (callback) {\n                window.history.replaceState(window.history.state, null, callback.newUrl);\n            }\n\n            if (callback && callback.valid) {\n                return setupCheckLoginIframe().then(function() {\n                    processCallback(callback, initPromise);\n                }).catch(function (error) {\n                    initPromise.setError(error);\n                });\n            }\n\n            if (initOptions.token && initOptions.refreshToken) {\n                setToken(initOptions.token, initOptions.refreshToken, initOptions.idToken);\n\n                if (loginIframe.enable) {\n                    setupCheckLoginIframe().then(function() {\n                        checkLoginIframe().then(function (unchanged) {\n                            if (unchanged) {\n                                kc.onAuthSuccess && kc.onAuthSuccess();\n                                initPromise.setSuccess();\n                                scheduleCheckIframe();\n                            } else {\n                                initPromise.setSuccess();\n                            }\n                        }).catch(function (error) {\n                            initPromise.setError(error);\n                        });\n                    });\n                } else {\n                    kc.updateToken(-1).then(function() {\n                        kc.onAuthSuccess && kc.onAuthSuccess();\n                        initPromise.setSuccess();\n                    }).catch(function(error) {\n                        kc.onAuthError && kc.onAuthError();\n                        if (initOptions.onLoad) {\n                            onLoad();\n                        } else {\n                            initPromise.setError(error);\n                        }\n                    });\n                }\n            } else if (initOptions.onLoad) {\n                onLoad();\n            } else {\n                initPromise.setSuccess();\n            }\n        }\n\n        configPromise.then(function () {\n            check3pCookiesSupported()\n                .then(processInit)\n                .catch(function (error) {\n                    promise.setError(error);\n                });\n        });\n        configPromise.catch(function (error) {\n            promise.setError(error);\n        });\n\n        return promise.promise;\n    }\n\n    kc.login = function (options) {\n        return adapter.login(options);\n    }\n\n    function generateRandomData(len) {\n        if (typeof crypto === \"undefined\" || typeof crypto.getRandomValues === \"undefined\") {\n            throw new Error(\"Web Crypto API is not available.\");\n        }\n\n        return crypto.getRandomValues(new Uint8Array(len));\n    }\n\n    function generateCodeVerifier(len) {\n        return generateRandomString(len, 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789');\n    }\n\n    function generateRandomString(len, alphabet){\n        var randomData = generateRandomData(len);\n        var chars = new Array(len);\n        for (var i = 0; i < len; i++) {\n            chars[i] = alphabet.charCodeAt(randomData[i] % alphabet.length);\n        }\n        return String.fromCharCode.apply(null, chars);\n    }\n\n    async function generatePkceChallenge(pkceMethod, codeVerifier) {\n        if (pkceMethod !== \"S256\") {\n            throw new TypeError(`Invalid value for 'pkceMethod', expected 'S256' but got '${pkceMethod}'.`);\n        }\n\n        // hash codeVerifier, then encode as url-safe base64 without padding\n        const hashBytes = new Uint8Array(await sha256Digest(codeVerifier));\n        const encodedHash = bytesToBase64(hashBytes)\n            .replace(/\\+/g, '-')\n            .replace(/\\//g, '_')\n            .replace(/\\=/g, '');\n\n        return encodedHash;\n    }\n\n    function buildClaimsParameter(requestedAcr){\n        var claims = {\n            id_token: {\n                acr: requestedAcr\n            }\n        }\n        return JSON.stringify(claims);\n    }\n\n    kc.createLoginUrl = async function(options) {\n        var state = createUUID();\n        var nonce = createUUID();\n\n        var redirectUri = adapter.redirectUri(options);\n\n        var callbackState = {\n            state: state,\n            nonce: nonce,\n            redirectUri: encodeURIComponent(redirectUri),\n            loginOptions: options\n        };\n\n        if (options && options.prompt) {\n            callbackState.prompt = options.prompt;\n        }\n\n        var baseUrl;\n        if (options && options.action == 'register') {\n            baseUrl = kc.endpoints.register();\n        } else {\n            baseUrl = kc.endpoints.authorize();\n        }\n\n        var scope = options && options.scope || kc.scope;\n        if (!scope) {\n            // if scope is not set, default to \"openid\"\n            scope = \"openid\";\n        } else if (scope.indexOf(\"openid\") === -1) {\n            // if openid scope is missing, prefix the given scopes with it\n            scope = \"openid \" + scope;\n        }\n\n        var url = baseUrl\n            + '?client_id=' + encodeURIComponent(kc.clientId)\n            + '&redirect_uri=' + encodeURIComponent(redirectUri)\n            + '&state=' + encodeURIComponent(state)\n            + '&response_mode=' + encodeURIComponent(kc.responseMode)\n            + '&response_type=' + encodeURIComponent(kc.responseType)\n            + '&scope=' + encodeURIComponent(scope);\n        if (useNonce) {\n            url = url + '&nonce=' + encodeURIComponent(nonce);\n        }\n\n        if (options && options.prompt) {\n            url += '&prompt=' + encodeURIComponent(options.prompt);\n        }\n\n        if (options && typeof options.maxAge === 'number') {\n            url += '&max_age=' + encodeURIComponent(options.maxAge);\n        }\n\n        if (options && options.loginHint) {\n            url += '&login_hint=' + encodeURIComponent(options.loginHint);\n        }\n\n        if (options && options.idpHint) {\n            url += '&kc_idp_hint=' + encodeURIComponent(options.idpHint);\n        }\n\n        if (options && options.action && options.action != 'register') {\n            url += '&kc_action=' + encodeURIComponent(options.action);\n        }\n\n        if (options && options.locale) {\n            url += '&ui_locales=' + encodeURIComponent(options.locale);\n        }\n\n        if (options && options.acr) {\n            var claimsParameter = buildClaimsParameter(options.acr);\n            url += '&claims=' + encodeURIComponent(claimsParameter);\n        }\n\n        if ((options && options.acrValues) || kc.acrValues) {\n            url += '&acr_values=' + encodeURIComponent(options.acrValues || kc.acrValues);\n        }\n\n        if (kc.pkceMethod) {\n            try {\n                const codeVerifier = generateCodeVerifier(96);\n                const pkceChallenge = await generatePkceChallenge(kc.pkceMethod, codeVerifier);\n\n                callbackState.pkceCodeVerifier = codeVerifier;\n\n                url += '&code_challenge=' + pkceChallenge;\n                url += '&code_challenge_method=' + kc.pkceMethod;\n            } catch (error) {\n                throw new Error(\"Failed to generate PKCE challenge.\", { cause: error });\n            }\n        }\n\n        callbackStorage.add(callbackState);\n\n        return url;\n    }\n\n    kc.logout = function(options) {\n        return adapter.logout(options);\n    }\n\n    kc.createLogoutUrl = function(options) {\n\n        const logoutMethod = options?.logoutMethod ?? kc.logoutMethod;\n        if (logoutMethod === 'POST') {\n            return kc.endpoints.logout();\n        }\n\n        var url = kc.endpoints.logout()\n            + '?client_id=' + encodeURIComponent(kc.clientId)\n            + '&post_logout_redirect_uri=' + encodeURIComponent(adapter.redirectUri(options, false));\n\n        if (kc.idToken) {\n            url += '&id_token_hint=' + encodeURIComponent(kc.idToken);\n        }\n\n        return url;\n    }\n\n    kc.register = function (options) {\n        return adapter.register(options);\n    }\n\n    kc.createRegisterUrl = async function(options) {\n        if (!options) {\n            options = {};\n        }\n        options.action = 'register';\n        return await kc.createLoginUrl(options);\n    }\n\n    kc.createAccountUrl = function(options) {\n        var realm = getRealmUrl();\n        var url = undefined;\n        if (typeof realm !== 'undefined') {\n            url = realm\n            + '/account'\n            + '?referrer=' + encodeURIComponent(kc.clientId)\n            + '&referrer_uri=' + encodeURIComponent(adapter.redirectUri(options));\n        }\n        return url;\n    }\n\n    kc.accountManagement = function() {\n        return adapter.accountManagement();\n    }\n\n    kc.hasRealmRole = function (role) {\n        var access = kc.realmAccess;\n        return !!access && access.roles.indexOf(role) >= 0;\n    }\n\n    kc.hasResourceRole = function(role, resource) {\n        if (!kc.resourceAccess) {\n            return false;\n        }\n\n        var access = kc.resourceAccess[resource || kc.clientId];\n        return !!access && access.roles.indexOf(role) >= 0;\n    }\n\n    kc.loadUserProfile = function() {\n        var url = getRealmUrl() + '/account';\n        var req = new XMLHttpRequest();\n        req.open('GET', url, true);\n        req.setRequestHeader('Accept', 'application/json');\n        req.setRequestHeader('Authorization', 'bearer ' + kc.token);\n\n        var promise = createPromise();\n\n        req.onreadystatechange = function () {\n            if (req.readyState == 4) {\n                if (req.status == 200) {\n                    kc.profile = JSON.parse(req.responseText);\n                    promise.setSuccess(kc.profile);\n                } else {\n                    promise.setError();\n                }\n            }\n        }\n\n        req.send();\n\n        return promise.promise;\n    }\n\n    kc.loadUserInfo = function() {\n        var url = kc.endpoints.userinfo();\n        var req = new XMLHttpRequest();\n        req.open('GET', url, true);\n        req.setRequestHeader('Accept', 'application/json');\n        req.setRequestHeader('Authorization', 'bearer ' + kc.token);\n\n        var promise = createPromise();\n\n        req.onreadystatechange = function () {\n            if (req.readyState == 4) {\n                if (req.status == 200) {\n                    kc.userInfo = JSON.parse(req.responseText);\n                    promise.setSuccess(kc.userInfo);\n                } else {\n                    promise.setError();\n                }\n            }\n        }\n\n        req.send();\n\n        return promise.promise;\n    }\n\n    kc.isTokenExpired = function(minValidity) {\n        if (!kc.tokenParsed || (!kc.refreshToken && kc.flow != 'implicit' )) {\n            throw 'Not authenticated';\n        }\n\n        if (kc.timeSkew == null) {\n            logInfo('[KEYCLOAK] Unable to determine if token is expired as timeskew is not set');\n            return true;\n        }\n\n        var expiresIn = kc.tokenParsed['exp'] - Math.ceil(new Date().getTime() / 1000) + kc.timeSkew;\n        if (minValidity) {\n            if (isNaN(minValidity)) {\n                throw 'Invalid minValidity';\n            }\n            expiresIn -= minValidity;\n        }\n        return expiresIn < 0;\n    }\n\n    kc.updateToken = function(minValidity) {\n        var promise = createPromise();\n\n        if (!kc.refreshToken) {\n            promise.setError();\n            return promise.promise;\n        }\n\n        minValidity = minValidity || 5;\n\n        var exec = function() {\n            var refreshToken = false;\n            if (minValidity == -1) {\n                refreshToken = true;\n                logInfo('[KEYCLOAK] Refreshing token: forced refresh');\n            } else if (!kc.tokenParsed || kc.isTokenExpired(minValidity)) {\n                refreshToken = true;\n                logInfo('[KEYCLOAK] Refreshing token: token expired');\n            }\n\n            if (!refreshToken) {\n                promise.setSuccess(false);\n            } else {\n                var params = 'grant_type=refresh_token&' + 'refresh_token=' + kc.refreshToken;\n                var url = kc.endpoints.token();\n\n                refreshQueue.push(promise);\n\n                if (refreshQueue.length == 1) {\n                    var req = new XMLHttpRequest();\n                    req.open('POST', url, true);\n                    req.setRequestHeader('Content-type', 'application/x-www-form-urlencoded');\n                    req.withCredentials = true;\n\n                    params += '&client_id=' + encodeURIComponent(kc.clientId);\n\n                    var timeLocal = new Date().getTime();\n\n                    req.onreadystatechange = function () {\n                        if (req.readyState == 4) {\n                            if (req.status == 200) {\n                                logInfo('[KEYCLOAK] Token refreshed');\n\n                                timeLocal = (timeLocal + new Date().getTime()) / 2;\n\n                                var tokenResponse = JSON.parse(req.responseText);\n\n                                setToken(tokenResponse['access_token'], tokenResponse['refresh_token'], tokenResponse['id_token'], timeLocal);\n\n                                kc.onAuthRefreshSuccess && kc.onAuthRefreshSuccess();\n                                for (var p = refreshQueue.pop(); p != null; p = refreshQueue.pop()) {\n                                    p.setSuccess(true);\n                                }\n                            } else {\n                                logWarn('[KEYCLOAK] Failed to refresh token');\n\n                                if (req.status == 400) {\n                                    kc.clearToken();\n                                }\n\n                                kc.onAuthRefreshError && kc.onAuthRefreshError();\n                                for (var p = refreshQueue.pop(); p != null; p = refreshQueue.pop()) {\n                                    p.setError(\"Failed to refresh token: An unexpected HTTP error occurred while attempting to refresh the token.\");\n                                }\n                            }\n                        }\n                    };\n\n                    req.send(params);\n                }\n            }\n        }\n\n        if (loginIframe.enable) {\n            var iframePromise = checkLoginIframe();\n            iframePromise.then(function() {\n                exec();\n            }).catch(function(error) {\n                promise.setError(error);\n            });\n        } else {\n            exec();\n        }\n\n        return promise.promise;\n    }\n\n    kc.clearToken = function() {\n        if (kc.token) {\n            setToken(null, null, null);\n            kc.onAuthLogout && kc.onAuthLogout();\n            if (kc.loginRequired) {\n                kc.login();\n            }\n        }\n    }\n\n    function getRealmUrl() {\n        if (typeof kc.authServerUrl !== 'undefined') {\n            if (kc.authServerUrl.charAt(kc.authServerUrl.length - 1) == '/') {\n                return kc.authServerUrl + 'realms/' + encodeURIComponent(kc.realm);\n            } else {\n                return kc.authServerUrl + '/realms/' + encodeURIComponent(kc.realm);\n            }\n        } else {\n            return undefined;\n        }\n    }\n\n    function getOrigin() {\n        if (!window.location.origin) {\n            return window.location.protocol + \"//\" + window.location.hostname + (window.location.port ? ':' + window.location.port: '');\n        } else {\n            return window.location.origin;\n        }\n    }\n\n    function processCallback(oauth, promise) {\n        var code = oauth.code;\n        var error = oauth.error;\n        var prompt = oauth.prompt;\n\n        var timeLocal = new Date().getTime();\n\n        if (oauth['kc_action_status']) {\n            kc.onActionUpdate && kc.onActionUpdate(oauth['kc_action_status'], oauth['kc_action']);\n        }\n\n        if (error) {\n            if (prompt != 'none') {\n                if (oauth.error_description && oauth.error_description === \"authentication_expired\") {\n                    kc.login(oauth.loginOptions);\n                } else {\n                    var errorData = { error: error, error_description: oauth.error_description };\n                    kc.onAuthError && kc.onAuthError(errorData);\n                    promise && promise.setError(errorData);\n                }\n            } else {\n                promise && promise.setSuccess();\n            }\n            return;\n        } else if ((kc.flow != 'standard') && (oauth.access_token || oauth.id_token)) {\n            authSuccess(oauth.access_token, null, oauth.id_token, true);\n        }\n\n        if ((kc.flow != 'implicit') && code) {\n            var params = 'code=' + code + '&grant_type=authorization_code';\n            var url = kc.endpoints.token();\n\n            var req = new XMLHttpRequest();\n            req.open('POST', url, true);\n            req.setRequestHeader('Content-type', 'application/x-www-form-urlencoded');\n\n            params += '&client_id=' + encodeURIComponent(kc.clientId);\n            params += '&redirect_uri=' + oauth.redirectUri;\n\n            if (oauth.pkceCodeVerifier) {\n                params += '&code_verifier=' + oauth.pkceCodeVerifier;\n            }\n\n            req.withCredentials = true;\n\n            req.onreadystatechange = function() {\n                if (req.readyState == 4) {\n                    if (req.status == 200) {\n\n                        var tokenResponse = JSON.parse(req.responseText);\n                        authSuccess(tokenResponse['access_token'], tokenResponse['refresh_token'], tokenResponse['id_token'], kc.flow === 'standard');\n                        scheduleCheckIframe();\n                    } else {\n                        kc.onAuthError && kc.onAuthError();\n                        promise && promise.setError();\n                    }\n                }\n            };\n\n            req.send(params);\n        }\n\n        function authSuccess(accessToken, refreshToken, idToken, fulfillPromise) {\n            timeLocal = (timeLocal + new Date().getTime()) / 2;\n\n            setToken(accessToken, refreshToken, idToken, timeLocal);\n\n            if (useNonce && (kc.idTokenParsed && kc.idTokenParsed.nonce != oauth.storedNonce)) {\n                logInfo('[KEYCLOAK] Invalid nonce, clearing token');\n                kc.clearToken();\n                promise && promise.setError();\n            } else {\n                if (fulfillPromise) {\n                    kc.onAuthSuccess && kc.onAuthSuccess();\n                    promise && promise.setSuccess();\n                }\n            }\n        }\n\n    }\n\n    function loadConfig() {\n        var promise = createPromise();\n        var configUrl;\n\n        if (typeof config === 'string') {\n            configUrl = config;\n        }\n\n        function setupOidcEndoints(oidcConfiguration) {\n            if (! oidcConfiguration) {\n                kc.endpoints = {\n                    authorize: function() {\n                        return getRealmUrl() + '/protocol/openid-connect/auth';\n                    },\n                    token: function() {\n                        return getRealmUrl() + '/protocol/openid-connect/token';\n                    },\n                    logout: function() {\n                        return getRealmUrl() + '/protocol/openid-connect/logout';\n                    },\n                    checkSessionIframe: function() {\n                        return getRealmUrl() + '/protocol/openid-connect/login-status-iframe.html';\n                    },\n                    thirdPartyCookiesIframe: function() {\n                        return getRealmUrl() + '/protocol/openid-connect/3p-cookies/step1.html';\n                    },\n                    register: function() {\n                        return getRealmUrl() + '/protocol/openid-connect/registrations';\n                    },\n                    userinfo: function() {\n                        return getRealmUrl() + '/protocol/openid-connect/userinfo';\n                    }\n                };\n            } else {\n                kc.endpoints = {\n                    authorize: function() {\n                        return oidcConfiguration.authorization_endpoint;\n                    },\n                    token: function() {\n                        return oidcConfiguration.token_endpoint;\n                    },\n                    logout: function() {\n                        if (!oidcConfiguration.end_session_endpoint) {\n                            throw \"Not supported by the OIDC server\";\n                        }\n                        return oidcConfiguration.end_session_endpoint;\n                    },\n                    checkSessionIframe: function() {\n                        if (!oidcConfiguration.check_session_iframe) {\n                            throw \"Not supported by the OIDC server\";\n                        }\n                        return oidcConfiguration.check_session_iframe;\n                    },\n                    register: function() {\n                        throw 'Redirection to \"Register user\" page not supported in standard OIDC mode';\n                    },\n                    userinfo: function() {\n                        if (!oidcConfiguration.userinfo_endpoint) {\n                            throw \"Not supported by the OIDC server\";\n                        }\n                        return oidcConfiguration.userinfo_endpoint;\n                    }\n                }\n            }\n        }\n\n        if (configUrl) {\n            var req = new XMLHttpRequest();\n            req.open('GET', configUrl, true);\n            req.setRequestHeader('Accept', 'application/json');\n\n            req.onreadystatechange = function () {\n                if (req.readyState == 4) {\n                    if (req.status == 200 || fileLoaded(req)) {\n                        var config = JSON.parse(req.responseText);\n\n                        kc.authServerUrl = config['auth-server-url'];\n                        kc.realm = config['realm'];\n                        kc.clientId = config['resource'];\n                        setupOidcEndoints(null);\n                        promise.setSuccess();\n                    } else {\n                        promise.setError();\n                    }\n                }\n            };\n\n            req.send();\n        } else {\n            kc.clientId = config.clientId;\n\n            var oidcProvider = config['oidcProvider'];\n            if (!oidcProvider) {\n                kc.authServerUrl = config.url;\n                kc.realm = config.realm;\n                setupOidcEndoints(null);\n                promise.setSuccess();\n            } else {\n                if (typeof oidcProvider === 'string') {\n                    var oidcProviderConfigUrl;\n                    if (oidcProvider.charAt(oidcProvider.length - 1) == '/') {\n                        oidcProviderConfigUrl = oidcProvider + '.well-known/openid-configuration';\n                    } else {\n                        oidcProviderConfigUrl = oidcProvider + '/.well-known/openid-configuration';\n                    }\n                    var req = new XMLHttpRequest();\n                    req.open('GET', oidcProviderConfigUrl, true);\n                    req.setRequestHeader('Accept', 'application/json');\n\n                    req.onreadystatechange = function () {\n                        if (req.readyState == 4) {\n                            if (req.status == 200 || fileLoaded(req)) {\n                                var oidcProviderConfig = JSON.parse(req.responseText);\n                                setupOidcEndoints(oidcProviderConfig);\n                                promise.setSuccess();\n                            } else {\n                                promise.setError();\n                            }\n                        }\n                    };\n\n                    req.send();\n                } else {\n                    setupOidcEndoints(oidcProvider);\n                    promise.setSuccess();\n                }\n            }\n        }\n\n        return promise.promise;\n    }\n\n    function fileLoaded(xhr) {\n        return xhr.status == 0 && xhr.responseText && xhr.responseURL.startsWith('file:');\n    }\n\n    function setToken(token, refreshToken, idToken, timeLocal) {\n        if (kc.tokenTimeoutHandle) {\n            clearTimeout(kc.tokenTimeoutHandle);\n            kc.tokenTimeoutHandle = null;\n        }\n\n        if (refreshToken) {\n            kc.refreshToken = refreshToken;\n            kc.refreshTokenParsed = decodeToken(refreshToken);\n        } else {\n            delete kc.refreshToken;\n            delete kc.refreshTokenParsed;\n        }\n\n        if (idToken) {\n            kc.idToken = idToken;\n            kc.idTokenParsed = decodeToken(idToken);\n        } else {\n            delete kc.idToken;\n            delete kc.idTokenParsed;\n        }\n\n        if (token) {\n            kc.token = token;\n            kc.tokenParsed = decodeToken(token);\n            kc.sessionId = kc.tokenParsed.sid;\n            kc.authenticated = true;\n            kc.subject = kc.tokenParsed.sub;\n            kc.realmAccess = kc.tokenParsed.realm_access;\n            kc.resourceAccess = kc.tokenParsed.resource_access;\n\n            if (timeLocal) {\n                kc.timeSkew = Math.floor(timeLocal / 1000) - kc.tokenParsed.iat;\n            }\n\n            if (kc.timeSkew != null) {\n                logInfo('[KEYCLOAK] Estimated time difference between browser and server is ' + kc.timeSkew + ' seconds');\n\n                if (kc.onTokenExpired) {\n                    var expiresIn = (kc.tokenParsed['exp'] - (new Date().getTime() / 1000) + kc.timeSkew) * 1000;\n                    logInfo('[KEYCLOAK] Token expires in ' + Math.round(expiresIn / 1000) + ' s');\n                    if (expiresIn <= 0) {\n                        kc.onTokenExpired();\n                    } else {\n                        kc.tokenTimeoutHandle = setTimeout(kc.onTokenExpired, expiresIn);\n                    }\n                }\n            }\n        } else {\n            delete kc.token;\n            delete kc.tokenParsed;\n            delete kc.subject;\n            delete kc.realmAccess;\n            delete kc.resourceAccess;\n\n            kc.authenticated = false;\n        }\n    }\n\n    function createUUID() {\n        if (typeof crypto === \"undefined\" || typeof crypto.randomUUID === \"undefined\") {\n            throw new Error(\"Web Crypto API is not available.\");\n        }\n\n        return crypto.randomUUID();\n    }\n\n    function parseCallback(url) {\n        var oauth = parseCallbackUrl(url);\n        if (!oauth) {\n            return;\n        }\n\n        var oauthState = callbackStorage.get(oauth.state);\n\n        if (oauthState) {\n            oauth.valid = true;\n            oauth.redirectUri = oauthState.redirectUri;\n            oauth.storedNonce = oauthState.nonce;\n            oauth.prompt = oauthState.prompt;\n            oauth.pkceCodeVerifier = oauthState.pkceCodeVerifier;\n            oauth.loginOptions = oauthState.loginOptions;\n        }\n\n        return oauth;\n    }\n\n    function parseCallbackUrl(url) {\n        var supportedParams;\n        switch (kc.flow) {\n            case 'standard':\n                supportedParams = ['code', 'state', 'session_state', 'kc_action_status', 'kc_action', 'iss'];\n                break;\n            case 'implicit':\n                supportedParams = ['access_token', 'token_type', 'id_token', 'state', 'session_state', 'expires_in', 'kc_action_status', 'kc_action', 'iss'];\n                break;\n            case 'hybrid':\n                supportedParams = ['access_token', 'token_type', 'id_token', 'code', 'state', 'session_state', 'expires_in', 'kc_action_status', 'kc_action', 'iss'];\n                break;\n        }\n\n        supportedParams.push('error');\n        supportedParams.push('error_description');\n        supportedParams.push('error_uri');\n\n        var queryIndex = url.indexOf('?');\n        var fragmentIndex = url.indexOf('#');\n\n        var newUrl;\n        var parsed;\n\n        if (kc.responseMode === 'query' && queryIndex !== -1) {\n            newUrl = url.substring(0, queryIndex);\n            parsed = parseCallbackParams(url.substring(queryIndex + 1, fragmentIndex !== -1 ? fragmentIndex : url.length), supportedParams);\n            if (parsed.paramsString !== '') {\n                newUrl += '?' + parsed.paramsString;\n            }\n            if (fragmentIndex !== -1) {\n                newUrl += url.substring(fragmentIndex);\n            }\n        } else if (kc.responseMode === 'fragment' && fragmentIndex !== -1) {\n            newUrl = url.substring(0, fragmentIndex);\n            parsed = parseCallbackParams(url.substring(fragmentIndex + 1), supportedParams);\n            if (parsed.paramsString !== '') {\n                newUrl += '#' + parsed.paramsString;\n            }\n        }\n\n        if (parsed && parsed.oauthParams) {\n            if (kc.flow === 'standard' || kc.flow === 'hybrid') {\n                if ((parsed.oauthParams.code || parsed.oauthParams.error) && parsed.oauthParams.state) {\n                    parsed.oauthParams.newUrl = newUrl;\n                    return parsed.oauthParams;\n                }\n            } else if (kc.flow === 'implicit') {\n                if ((parsed.oauthParams.access_token || parsed.oauthParams.error) && parsed.oauthParams.state) {\n                    parsed.oauthParams.newUrl = newUrl;\n                    return parsed.oauthParams;\n                }\n            }\n        }\n    }\n\n    function parseCallbackParams(paramsString, supportedParams) {\n        var p = paramsString.split('&');\n        var result = {\n            paramsString: '',\n            oauthParams: {}\n        }\n        for (var i = 0; i < p.length; i++) {\n            var split = p[i].indexOf(\"=\");\n            var key = p[i].slice(0, split);\n            if (supportedParams.indexOf(key) !== -1) {\n                result.oauthParams[key] = p[i].slice(split + 1);\n            } else {\n                if (result.paramsString !== '') {\n                    result.paramsString += '&';\n                }\n                result.paramsString += p[i];\n            }\n        }\n        return result;\n    }\n\n    function createPromise() {\n        // Need to create a native Promise which also preserves the\n        // interface of the custom promise type previously used by the API\n        var p = {\n            setSuccess: function(result) {\n                p.resolve(result);\n            },\n\n            setError: function(result) {\n                p.reject(result);\n            }\n        };\n        p.promise = new Promise(function(resolve, reject) {\n            p.resolve = resolve;\n            p.reject = reject;\n        });\n\n        return p;\n    }\n\n    // Function to extend existing native Promise with timeout\n    function applyTimeoutToPromise(promise, timeout, errorMessage) {\n        var timeoutHandle = null;\n        var timeoutPromise = new Promise(function (resolve, reject) {\n            timeoutHandle = setTimeout(function () {\n                reject({ \"error\": errorMessage || \"Promise is not settled within timeout of \" + timeout + \"ms\" });\n            }, timeout);\n        });\n\n        return Promise.race([promise, timeoutPromise]).finally(function () {\n            clearTimeout(timeoutHandle);\n        });\n    }\n\n    function setupCheckLoginIframe() {\n        var promise = createPromise();\n\n        if (!loginIframe.enable) {\n            promise.setSuccess();\n            return promise.promise;\n        }\n\n        if (loginIframe.iframe) {\n            promise.setSuccess();\n            return promise.promise;\n        }\n\n        var iframe = document.createElement('iframe');\n        loginIframe.iframe = iframe;\n\n        iframe.onload = function() {\n            var authUrl = kc.endpoints.authorize();\n            if (authUrl.charAt(0) === '/') {\n                loginIframe.iframeOrigin = getOrigin();\n            } else {\n                loginIframe.iframeOrigin = authUrl.substring(0, authUrl.indexOf('/', 8));\n            }\n            promise.setSuccess();\n        }\n\n        var src = kc.endpoints.checkSessionIframe();\n        iframe.setAttribute('src', src );\n        iframe.setAttribute('sandbox', 'allow-storage-access-by-user-activation allow-scripts allow-same-origin');\n        iframe.setAttribute('title', 'keycloak-session-iframe' );\n        iframe.style.display = 'none';\n        document.body.appendChild(iframe);\n\n        var messageCallback = function(event) {\n            if ((event.origin !== loginIframe.iframeOrigin) || (loginIframe.iframe.contentWindow !== event.source)) {\n                return;\n            }\n\n            if (!(event.data == 'unchanged' || event.data == 'changed' || event.data == 'error')) {\n                return;\n            }\n\n\n            if (event.data != 'unchanged') {\n                kc.clearToken();\n            }\n\n            var callbacks = loginIframe.callbackList.splice(0, loginIframe.callbackList.length);\n\n            for (var i = callbacks.length - 1; i >= 0; --i) {\n                var promise = callbacks[i];\n                if (event.data == 'error') {\n                    promise.setError();\n                } else {\n                    promise.setSuccess(event.data == 'unchanged');\n                }\n            }\n        };\n\n        window.addEventListener('message', messageCallback, false);\n\n        return promise.promise;\n    }\n\n    function scheduleCheckIframe() {\n        if (loginIframe.enable) {\n            if (kc.token) {\n                setTimeout(function() {\n                    checkLoginIframe().then(function(unchanged) {\n                        if (unchanged) {\n                            scheduleCheckIframe();\n                        }\n                    });\n                }, loginIframe.interval * 1000);\n            }\n        }\n    }\n\n    function checkLoginIframe() {\n        var promise = createPromise();\n\n        if (loginIframe.iframe && loginIframe.iframeOrigin ) {\n            var msg = kc.clientId + ' ' + (kc.sessionId ? kc.sessionId : '');\n            loginIframe.callbackList.push(promise);\n            var origin = loginIframe.iframeOrigin;\n            if (loginIframe.callbackList.length == 1) {\n                loginIframe.iframe.contentWindow.postMessage(msg, origin);\n            }\n        } else {\n            promise.setSuccess();\n        }\n\n        return promise.promise;\n    }\n\n    function check3pCookiesSupported() {\n        var promise = createPromise();\n\n        if ((loginIframe.enable || kc.silentCheckSsoRedirectUri) && typeof kc.endpoints.thirdPartyCookiesIframe === 'function') {\n            var iframe = document.createElement('iframe');\n            iframe.setAttribute('src', kc.endpoints.thirdPartyCookiesIframe());\n            iframe.setAttribute('sandbox', 'allow-storage-access-by-user-activation allow-scripts allow-same-origin');\n            iframe.setAttribute('title', 'keycloak-3p-check-iframe' );\n            iframe.style.display = 'none';\n            document.body.appendChild(iframe);\n\n            var messageCallback = function(event) {\n                if (iframe.contentWindow !== event.source) {\n                    return;\n                }\n\n                if (event.data !== \"supported\" && event.data !== \"unsupported\") {\n                    return;\n                } else if (event.data === \"unsupported\") {\n                    logWarn(\n                        \"[KEYCLOAK] Your browser is blocking access to 3rd-party cookies, this means:\\n\\n\" +\n                        \" - It is not possible to retrieve tokens without redirecting to the Keycloak server (a.k.a. no support for silent authentication).\\n\" +\n                        \" - It is not possible to automatically detect changes to the session status (such as the user logging out in another tab).\\n\\n\" +\n                        \"For more information see: https://www.keycloak.org/securing-apps/javascript-adapter#_modern_browsers\"\n                    );\n\n                    loginIframe.enable = false;\n                    if (kc.silentCheckSsoFallback) {\n                        kc.silentCheckSsoRedirectUri = false;\n                    }\n                }\n\n                document.body.removeChild(iframe);\n                window.removeEventListener(\"message\", messageCallback);\n                promise.setSuccess();\n            };\n\n            window.addEventListener('message', messageCallback, false);\n        } else {\n            promise.setSuccess();\n        }\n\n        return applyTimeoutToPromise(promise.promise, kc.messageReceiveTimeout, \"Timeout when waiting for 3rd party check iframe message.\");\n    }\n\n    function loadAdapter(type) {\n        if (!type || type == 'default') {\n            return {\n                login: async function(options) {\n                    window.location.assign(await kc.createLoginUrl(options));\n                    return createPromise().promise;\n                },\n\n                logout: async function(options) {\n\n                    const logoutMethod = options?.logoutMethod ?? kc.logoutMethod;\n                    if (logoutMethod === \"GET\") {\n                        window.location.replace(kc.createLogoutUrl(options));\n                        return;\n                    }\n\n                    // Create form to send POST request.\n                    const form = document.createElement(\"form\");\n\n                    form.setAttribute(\"method\", \"POST\");\n                    form.setAttribute(\"action\", kc.createLogoutUrl(options));\n                    form.style.display = \"none\";\n\n                    // Add data to form as hidden input fields.\n                    const data = {\n                        id_token_hint: kc.idToken,\n                        client_id: kc.clientId,\n                        post_logout_redirect_uri: adapter.redirectUri(options, false)\n                    };\n\n                    for (const [name, value] of Object.entries(data)) {\n                        const input = document.createElement(\"input\");\n\n                        input.setAttribute(\"type\", \"hidden\");\n                        input.setAttribute(\"name\", name);\n                        input.setAttribute(\"value\", value);\n\n                        form.appendChild(input);\n                    }\n\n                    // Append form to page and submit it to perform logout and redirect.\n                    document.body.appendChild(form);\n                    form.submit();\n                },\n\n                register: async function(options) {\n                    window.location.assign(await kc.createRegisterUrl(options));\n                    return createPromise().promise;\n                },\n\n                accountManagement : function() {\n                    var accountUrl = kc.createAccountUrl();\n                    if (typeof accountUrl !== 'undefined') {\n                        window.location.href = accountUrl;\n                    } else {\n                        throw \"Not supported by the OIDC server\";\n                    }\n                    return createPromise().promise;\n                },\n\n                redirectUri: function(options, encodeHash) {\n                    if (arguments.length == 1) {\n                        encodeHash = true;\n                    }\n\n                    if (options && options.redirectUri) {\n                        return options.redirectUri;\n                    } else if (kc.redirectUri) {\n                        return kc.redirectUri;\n                    } else {\n                        return location.href;\n                    }\n                }\n            };\n        }\n\n        if (type == 'cordova') {\n            loginIframe.enable = false;\n            var cordovaOpenWindowWrapper = function(loginUrl, target, options) {\n                if (window.cordova && window.cordova.InAppBrowser) {\n                    // Use inappbrowser for IOS and Android if available\n                    return window.cordova.InAppBrowser.open(loginUrl, target, options);\n                } else {\n                    return window.open(loginUrl, target, options);\n                }\n            };\n\n            var shallowCloneCordovaOptions = function (userOptions) {\n                if (userOptions && userOptions.cordovaOptions) {\n                    return Object.keys(userOptions.cordovaOptions).reduce(function (options, optionName) {\n                        options[optionName] = userOptions.cordovaOptions[optionName];\n                        return options;\n                    }, {});\n                } else {\n                    return {};\n                }\n            };\n\n            var formatCordovaOptions = function (cordovaOptions) {\n                return Object.keys(cordovaOptions).reduce(function (options, optionName) {\n                    options.push(optionName+\"=\"+cordovaOptions[optionName]);\n                    return options;\n                }, []).join(\",\");\n            };\n\n            var createCordovaOptions = function (userOptions) {\n                var cordovaOptions = shallowCloneCordovaOptions(userOptions);\n                cordovaOptions.location = 'no';\n                if (userOptions && userOptions.prompt == 'none') {\n                    cordovaOptions.hidden = 'yes';\n                }\n                return formatCordovaOptions(cordovaOptions);\n            };\n\n            var getCordovaRedirectUri = function() {\n                return kc.redirectUri || 'http://localhost';\n            }\n\n            return {\n                login: async function(options) {\n                    var promise = createPromise();\n\n                    var cordovaOptions = createCordovaOptions(options);\n                    var loginUrl = await kc.createLoginUrl(options);\n                    var ref = cordovaOpenWindowWrapper(loginUrl, '_blank', cordovaOptions);\n                    var completed = false;\n\n                    var closed = false;\n                    var closeBrowser = function() {\n                        closed = true;\n                        ref.close();\n                    };\n\n                    ref.addEventListener('loadstart', function(event) {\n                        if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n                            var callback = parseCallback(event.url);\n                            processCallback(callback, promise);\n                            closeBrowser();\n                            completed = true;\n                        }\n                    });\n\n                    ref.addEventListener('loaderror', function(event) {\n                        if (!completed) {\n                            if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n                                var callback = parseCallback(event.url);\n                                processCallback(callback, promise);\n                                closeBrowser();\n                                completed = true;\n                            } else {\n                                promise.setError();\n                                closeBrowser();\n                            }\n                        }\n                    });\n\n                    ref.addEventListener('exit', function(event) {\n                        if (!closed) {\n                            promise.setError({\n                                reason: \"closed_by_user\"\n                            });\n                        }\n                    });\n\n                    return promise.promise;\n                },\n\n                logout: function(options) {\n                    var promise = createPromise();\n\n                    var logoutUrl = kc.createLogoutUrl(options);\n                    var ref = cordovaOpenWindowWrapper(logoutUrl, '_blank', 'location=no,hidden=yes,clearcache=yes');\n\n                    var error;\n\n                    ref.addEventListener('loadstart', function(event) {\n                        if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n                            ref.close();\n                        }\n                    });\n\n                    ref.addEventListener('loaderror', function(event) {\n                        if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n                            ref.close();\n                        } else {\n                            error = true;\n                            ref.close();\n                        }\n                    });\n\n                    ref.addEventListener('exit', function(event) {\n                        if (error) {\n                            promise.setError();\n                        } else {\n                            kc.clearToken();\n                            promise.setSuccess();\n                        }\n                    });\n\n                    return promise.promise;\n                },\n\n                register : async function(options) {\n                    var promise = createPromise();\n                    var registerUrl = await kc.createRegisterUrl();\n                    var cordovaOptions = createCordovaOptions(options);\n                    var ref = cordovaOpenWindowWrapper(registerUrl, '_blank', cordovaOptions);\n                    ref.addEventListener('loadstart', function(event) {\n                        if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n                            ref.close();\n                            var oauth = parseCallback(event.url);\n                            processCallback(oauth, promise);\n                        }\n                    });\n                    return promise.promise;\n                },\n\n                accountManagement : function() {\n                    var accountUrl = kc.createAccountUrl();\n                    if (typeof accountUrl !== 'undefined') {\n                        var ref = cordovaOpenWindowWrapper(accountUrl, '_blank', 'location=no');\n                        ref.addEventListener('loadstart', function(event) {\n                            if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n                                ref.close();\n                            }\n                        });\n                    } else {\n                        throw \"Not supported by the OIDC server\";\n                    }\n                },\n\n                redirectUri: function(options) {\n                    return getCordovaRedirectUri();\n                }\n            }\n        }\n\n        if (type == 'cordova-native') {\n            loginIframe.enable = false;\n\n            return {\n                login: async function(options) {\n                    var promise = createPromise();\n                    var loginUrl = await kc.createLoginUrl(options);\n\n                    universalLinks.subscribe('keycloak', function(event) {\n                        universalLinks.unsubscribe('keycloak');\n                        window.cordova.plugins.browsertab.close();\n                        var oauth = parseCallback(event.url);\n                        processCallback(oauth, promise);\n                    });\n\n                    window.cordova.plugins.browsertab.openUrl(loginUrl);\n                    return promise.promise;\n                },\n\n                logout: function(options) {\n                    var promise = createPromise();\n                    var logoutUrl = kc.createLogoutUrl(options);\n\n                    universalLinks.subscribe('keycloak', function(event) {\n                        universalLinks.unsubscribe('keycloak');\n                        window.cordova.plugins.browsertab.close();\n                        kc.clearToken();\n                        promise.setSuccess();\n                    });\n\n                    window.cordova.plugins.browsertab.openUrl(logoutUrl);\n                    return promise.promise;\n                },\n\n                register : async function(options) {\n                    var promise = createPromise();\n                    var registerUrl = await kc.createRegisterUrl(options);\n                    universalLinks.subscribe('keycloak' , function(event) {\n                        universalLinks.unsubscribe('keycloak');\n                        window.cordova.plugins.browsertab.close();\n                        var oauth = parseCallback(event.url);\n                        processCallback(oauth, promise);\n                    });\n                    window.cordova.plugins.browsertab.openUrl(registerUrl);\n                    return promise.promise;\n\n                },\n\n                accountManagement : function() {\n                    var accountUrl = kc.createAccountUrl();\n                    if (typeof accountUrl !== 'undefined') {\n                        window.cordova.plugins.browsertab.openUrl(accountUrl);\n                    } else {\n                        throw \"Not supported by the OIDC server\";\n                    }\n                },\n\n                redirectUri: function(options) {\n                    if (options && options.redirectUri) {\n                        return options.redirectUri;\n                    } else if (kc.redirectUri) {\n                        return kc.redirectUri;\n                    } else {\n                        return \"http://localhost\";\n                    }\n                }\n            }\n        }\n\n        throw 'invalid adapter type: ' + type;\n    }\n\n    const STORAGE_KEY_PREFIX = 'kc-callback-';\n\n    var LocalStorage = function() {\n        if (!(this instanceof LocalStorage)) {\n            return new LocalStorage();\n        }\n\n        localStorage.setItem('kc-test', 'test');\n        localStorage.removeItem('kc-test');\n\n        var cs = this;\n\n        /**\n         * Clears all values from local storage that are no longer valid.\n         */\n        function clearInvalidValues() {\n            const currentTime = Date.now();\n\n            for (const [key, value] of getStoredEntries()) {\n                // Attempt to parse the expiry time from the value.\n                const expiry = parseExpiry(value);\n\n                // Discard the value if it is malformed or expired.\n                if (expiry === null || expiry < currentTime) {\n                    localStorage.removeItem(key);\n                }\n            }\n        }\n\n        /**\n         * Clears all known values from local storage.\n         */\n        function clearAllValues() {\n            for (const [key] of getStoredEntries()) {\n                localStorage.removeItem(key);\n            }\n        }\n\n        /**\n         * Gets all entries stored in local storage that are known to be managed by this class.\n         * @returns {Array<[string, unknown]>} An array of key-value pairs.\n         */\n        function getStoredEntries() {\n            return Object.entries(localStorage).filter(([key]) => key.startsWith(STORAGE_KEY_PREFIX));\n        }\n\n        /**\n         * Parses the expiry time from a value stored in local storage.\n         * @param {unknown} value\n         * @returns {number | null} The expiry time in milliseconds, or `null` if the value is malformed.\n         */\n        function parseExpiry(value) {\n            let parsedValue;\n\n            // Attempt to parse the value as JSON.\n            try {\n                parsedValue = JSON.parse(value);\n            } catch (error) {\n                return null;\n            }\n\n            // Attempt to extract the 'expires' property.\n            if (isObject(parsedValue) && 'expires' in parsedValue && typeof parsedValue.expires === 'number') {\n                return parsedValue.expires;\n            }\n\n            return null;\n        }\n\n        cs.get = function(state) {\n            if (!state) {\n                return;\n            }\n\n            var key = STORAGE_KEY_PREFIX + state;\n            var value = localStorage.getItem(key);\n            if (value) {\n                localStorage.removeItem(key);\n                value = JSON.parse(value);\n            }\n\n            clearInvalidValues();\n            return value;\n        };\n\n        cs.add = function(state) {\n            clearInvalidValues();\n\n            const key = STORAGE_KEY_PREFIX + state.state;\n            const value = JSON.stringify({\n                ...state,\n                // Set the expiry time to 1 hour from now.\n                expires: Date.now() + (60 * 60 * 1000)\n            });\n\n            try {\n                localStorage.setItem(key, value);\n            } catch (error) {\n                // If the storage is full, clear all known values and try again.\n                clearAllValues();\n                localStorage.setItem(key, value);\n            }\n        };\n    };\n\n    var CookieStorage = function() {\n        if (!(this instanceof CookieStorage)) {\n            return new CookieStorage();\n        }\n\n        var cs = this;\n\n        cs.get = function(state) {\n            if (!state) {\n                return;\n            }\n\n            var value = getCookie(STORAGE_KEY_PREFIX + state);\n            setCookie(STORAGE_KEY_PREFIX + state, '', cookieExpiration(-100));\n            if (value) {\n                return JSON.parse(value);\n            }\n        };\n\n        cs.add = function(state) {\n            setCookie(STORAGE_KEY_PREFIX + state.state, JSON.stringify(state), cookieExpiration(60));\n        };\n\n        cs.removeItem = function(key) {\n            setCookie(key, '', cookieExpiration(-100));\n        };\n\n        var cookieExpiration = function (minutes) {\n            var exp = new Date();\n            exp.setTime(exp.getTime() + (minutes*60*1000));\n            return exp;\n        };\n\n        var getCookie = function (key) {\n            var name = key + '=';\n            var ca = document.cookie.split(';');\n            for (var i = 0; i < ca.length; i++) {\n                var c = ca[i];\n                while (c.charAt(0) == ' ') {\n                    c = c.substring(1);\n                }\n                if (c.indexOf(name) == 0) {\n                    return c.substring(name.length, c.length);\n                }\n            }\n            return '';\n        };\n\n        var setCookie = function (key, value, expirationDate) {\n            var cookie = key + '=' + value + '; '\n                + 'expires=' + expirationDate.toUTCString() + '; ';\n            document.cookie = cookie;\n        }\n    };\n\n    function createCallbackStorage() {\n        try {\n            return new LocalStorage();\n        } catch (err) {\n        }\n\n        return new CookieStorage();\n    }\n\n    function createLogger(fn) {\n        return function() {\n            if (kc.enableLogging) {\n                fn.apply(console, Array.prototype.slice.call(arguments));\n            }\n        };\n    }\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Keycloak);\n\n/**\n * @param {ArrayBuffer} bytes\n * @see https://developer.mozilla.org/en-US/docs/Glossary/Base64#the_unicode_problem\n */\nfunction bytesToBase64(bytes) {\n    const binString = String.fromCodePoint(...bytes);\n    return btoa(binString);\n}\n\n/**\n * @param {string} message\n * @see https://developer.mozilla.org/en-US/docs/Web/API/SubtleCrypto/digest#basic_example\n */\nasync function sha256Digest(message) {\n    const encoder = new TextEncoder();\n    const data = encoder.encode(message);\n\n    if (typeof crypto === \"undefined\" || typeof crypto.subtle === \"undefined\") {\n        throw new Error(\"Web Crypto API is not available.\");\n    }\n\n    return await crypto.subtle.digest(\"SHA-256\", data);\n}\n\n/**\n * @param {string} token\n */\nfunction decodeToken(token) {\n    const [header, payload] = token.split(\".\");\n\n    if (typeof payload !== \"string\") {\n        throw new Error(\"Unable to decode token, payload not found.\");\n    }\n\n    let decoded;\n\n    try {\n        decoded = base64UrlDecode(payload);\n    } catch (error) {\n        throw new Error(\"Unable to decode token, payload is not a valid Base64URL value.\", { cause: error });\n    }\n\n    try {\n        return JSON.parse(decoded);\n    } catch (error) {\n        throw new Error(\"Unable to decode token, payload is not a valid JSON value.\", { cause: error });\n    }\n}\n\n/**\n * @param {string} input\n */\nfunction base64UrlDecode(input) {\n    let output = input\n        .replaceAll(\"-\", \"+\")\n        .replaceAll(\"_\", \"/\");\n\n    switch (output.length % 4) {\n        case 0:\n            break;\n        case 2:\n            output += \"==\";\n            break;\n        case 3:\n            output += \"=\";\n            break;\n        default:\n            throw new Error(\"Input is not of the correct length.\");\n    }\n\n    try {\n        return b64DecodeUnicode(output);\n    } catch (error) {\n        return atob(output);\n    }\n}\n\n/**\n * @param {string} input\n */\nfunction b64DecodeUnicode(input) {\n    return decodeURIComponent(atob(input).replace(/(.)/g, (m, p) => {\n        let code = p.charCodeAt(0).toString(16).toUpperCase();\n\n        if (code.length < 2) {\n            code = \"0\" + code;\n        }\n\n        return \"%\" + code;\n    }));\n}\n\n/**\n * Check if the input is an object that can be operated on.\n * @param {unknown} input\n */\nfunction isObject(input) {\n    return typeof input === 'object' && input !== null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/keycloak-js/lib/keycloak.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next-themes/dist/index.mjs":
/*!*************************************************!*\
  !*** ./node_modules/next-themes/dist/index.mjs ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ J),\n/* harmony export */   useTheme: () => (/* binding */ z)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme auto */ var _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\nvar M = (e, i, s, u, m, a, l, h)=>{\n    let d = document.documentElement, w = [\n        \"light\",\n        \"dark\"\n    ];\n    function p(n) {\n        (Array.isArray(e) ? e : [\n            e\n        ]).forEach((y)=>{\n            let k = y === \"class\", S = k && a ? m.map((f)=>a[f] || f) : m;\n            k ? (d.classList.remove(...S), d.classList.add(a && a[n] ? a[n] : n)) : d.setAttribute(y, n);\n        }), R(n);\n    }\n    function R(n) {\n        h && w.includes(n) && (d.style.colorScheme = n);\n    }\n    function c() {\n        return window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? \"dark\" : \"light\";\n    }\n    if (u) p(u);\n    else try {\n        let n = localStorage.getItem(i) || s, y = l && n === \"system\" ? c() : n;\n        p(y);\n    } catch (n) {}\n};\n_c = M;\nvar b = [\n    \"light\",\n    \"dark\"\n], I = \"(prefers-color-scheme: dark)\", O = \"object\" == \"undefined\", x = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(void 0), U = {\n    setTheme: (e)=>{},\n    themes: []\n}, z = ()=>{\n    _s();\n    var e;\n    return (e = react__WEBPACK_IMPORTED_MODULE_0__.useContext(x)) != null ? e : U;\n}, J = (e)=>{\n    _s1();\n    return react__WEBPACK_IMPORTED_MODULE_0__.useContext(x) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, e.children) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(V, {\n        ...e\n    });\n}, N = [\n    \"light\",\n    \"dark\"\n], V = (param)=>{\n    let { forcedTheme: e, disableTransitionOnChange: i = !1, enableSystem: s = !0, enableColorScheme: u = !0, storageKey: m = \"theme\", themes: a = N, defaultTheme: l = s ? \"system\" : \"light\", attribute: h = \"data-theme\", value: d, children: w, nonce: p, scriptProps: R } = param;\n    _s2();\n    let [c, n] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        \"V.useState\": ()=>H(m, l)\n    }[\"V.useState\"]), [T, y] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        \"V.useState\": ()=>c === \"system\" ? E() : c\n    }[\"V.useState\"]), k = d ? Object.values(d) : a, S = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"V.useCallback[S]\": (o)=>{\n            let r = o;\n            if (!r) return;\n            o === \"system\" && s && (r = E());\n            let v = d ? d[r] : r, C = i ? W(p) : null, P = document.documentElement, L = {\n                \"V.useCallback[S].L\": (g)=>{\n                    g === \"class\" ? (P.classList.remove(...k), v && P.classList.add(v)) : g.startsWith(\"data-\") && (v ? P.setAttribute(g, v) : P.removeAttribute(g));\n                }\n            }[\"V.useCallback[S].L\"];\n            if (Array.isArray(h) ? h.forEach(L) : L(h), u) {\n                let g = b.includes(l) ? l : null, D = b.includes(r) ? r : g;\n                P.style.colorScheme = D;\n            }\n            C == null || C();\n        }\n    }[\"V.useCallback[S]\"], [\n        p\n    ]), f = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"V.useCallback[f]\": (o)=>{\n            let r = typeof o == \"function\" ? o(c) : o;\n            n(r);\n            try {\n                localStorage.setItem(m, r);\n            } catch (v) {}\n        }\n    }[\"V.useCallback[f]\"], [\n        c\n    ]), A = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"V.useCallback[A]\": (o)=>{\n            let r = E(o);\n            y(r), c === \"system\" && s && !e && S(\"system\");\n        }\n    }[\"V.useCallback[A]\"], [\n        c,\n        e\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"V.useEffect\": ()=>{\n            let o = window.matchMedia(I);\n            return o.addListener(A), A(o), ({\n                \"V.useEffect\": ()=>o.removeListener(A)\n            })[\"V.useEffect\"];\n        }\n    }[\"V.useEffect\"], [\n        A\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"V.useEffect\": ()=>{\n            let o = {\n                \"V.useEffect.o\": (r)=>{\n                    r.key === m && (r.newValue ? n(r.newValue) : f(l));\n                }\n            }[\"V.useEffect.o\"];\n            return window.addEventListener(\"storage\", o), ({\n                \"V.useEffect\": ()=>window.removeEventListener(\"storage\", o)\n            })[\"V.useEffect\"];\n        }\n    }[\"V.useEffect\"], [\n        f\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"V.useEffect\": ()=>{\n            S(e != null ? e : c);\n        }\n    }[\"V.useEffect\"], [\n        e,\n        c\n    ]);\n    let Q = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"V.useMemo[Q]\": ()=>({\n                theme: c,\n                setTheme: f,\n                forcedTheme: e,\n                resolvedTheme: c === \"system\" ? T : c,\n                themes: s ? [\n                    ...a,\n                    \"system\"\n                ] : a,\n                systemTheme: s ? T : void 0\n            })\n    }[\"V.useMemo[Q]\"], [\n        c,\n        f,\n        e,\n        T,\n        s,\n        a\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(x.Provider, {\n        value: Q\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(_, {\n        forcedTheme: e,\n        storageKey: m,\n        attribute: h,\n        enableSystem: s,\n        enableColorScheme: u,\n        defaultTheme: l,\n        value: d,\n        themes: a,\n        nonce: p,\n        scriptProps: R\n    }), w);\n}, _ = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.memo((param)=>{\n    let { forcedTheme: e, storageKey: i, attribute: s, enableSystem: u, enableColorScheme: m, defaultTheme: a, value: l, themes: h, nonce: d, scriptProps: w } = param;\n    let p = JSON.stringify([\n        s,\n        i,\n        a,\n        e,\n        h,\n        l,\n        u,\n        m\n    ]).slice(1, -1);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"script\", {\n        ...w,\n        suppressHydrationWarning: !0,\n        nonce:  false ? 0 : \"\",\n        dangerouslySetInnerHTML: {\n            __html: \"(\".concat(M.toString(), \")(\").concat(p, \")\")\n        }\n    });\n}), H = (e, i)=>{\n    if (O) return;\n    let s;\n    try {\n        s = localStorage.getItem(e) || void 0;\n    } catch (u) {}\n    return s || i;\n}, W = (e)=>{\n    let i = document.createElement(\"style\");\n    return e && i.setAttribute(\"nonce\", e), i.appendChild(document.createTextNode(\"*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}\")), document.head.appendChild(i), ()=>{\n        window.getComputedStyle(document.body), setTimeout(()=>{\n            document.head.removeChild(i);\n        }, 1);\n    };\n}, E = (e)=>(e || (e = window.matchMedia(I)), e.matches ? \"dark\" : \"light\");\n_s(z, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\n_s1(J, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\n_s2(V, \"UCkmxL+2pKwquH5a3QithkhUKcE=\");\n\nvar _c;\n$RefreshReg$(_c, \"M\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next-themes/dist/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CCode%5C%5Cone-mail%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCode%5C%5Cone-mail%5C%5Ccomponents%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCode%5C%5Cone-mail%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCode%5C%5Cone-mail%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CCode%5C%5Cone-mail%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCode%5C%5Cone-mail%5C%5Ccomponents%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCode%5C%5Cone-mail%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCode%5C%5Cone-mail%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/globals.css */ \"(app-pages-browser)/./app/globals.css\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/auth-provider.tsx */ \"(app-pages-browser)/./components/auth-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(app-pages-browser)/./components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CCode%5C%5Cone-mail%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCode%5C%5Cone-mail%5C%5Ccomponents%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCode%5C%5Cone-mail%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCode%5C%5Cone-mail%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js ***!
  \*********************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("var __dirname = \"/\";\n(()=>{\"use strict\";var e={529:(e,r,t)=>{var n=t(191);var i=Object.create(null);var a=typeof document===\"undefined\";var o=Array.prototype.forEach;function debounce(e,r){var t=0;return function(){var n=this;var i=arguments;var a=function functionCall(){return e.apply(n,i)};clearTimeout(t);t=setTimeout(a,r)}}function noop(){}function getCurrentScriptUrl(e){var r=i[e];if(!r){if(document.currentScript){r=document.currentScript.src}else{var t=document.getElementsByTagName(\"script\");var a=t[t.length-1];if(a){r=a.src}}i[e]=r}return function(e){if(!r){return null}var t=r.split(/([^\\\\/]+)\\.js$/);var i=t&&t[1];if(!i){return[r.replace(\".js\",\".css\")]}if(!e){return[r.replace(\".js\",\".css\")]}return e.split(\",\").map((function(e){var t=new RegExp(\"\".concat(i,\"\\\\.js$\"),\"g\");return n(r.replace(t,\"\".concat(e.replace(/{fileName}/g,i),\".css\")))}))}}function updateCss(e,r){if(!r){if(!e.href){return}r=e.href.split(\"?\")[0]}if(!isUrlRequest(r)){return}if(e.isLoaded===false){return}if(!r||!(r.indexOf(\".css\")>-1)){return}e.visited=true;var t=e.cloneNode();t.isLoaded=false;t.addEventListener(\"load\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.addEventListener(\"error\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.href=\"\".concat(r,\"?\").concat(Date.now());if(e.nextSibling){e.parentNode.insertBefore(t,e.nextSibling)}else{e.parentNode.appendChild(t)}}function getReloadUrl(e,r){var t;e=n(e,{stripWWW:false});r.some((function(n){if(e.indexOf(r)>-1){t=n}}));return t}function reloadStyle(e){if(!e){return false}var r=document.querySelectorAll(\"link\");var t=false;o.call(r,(function(r){if(!r.href){return}var n=getReloadUrl(r.href,e);if(!isUrlRequest(n)){return}if(r.visited===true){return}if(n){updateCss(r,n);t=true}}));return t}function reloadAll(){var e=document.querySelectorAll(\"link\");o.call(e,(function(e){if(e.visited===true){return}updateCss(e)}))}function isUrlRequest(e){if(!/^[a-zA-Z][a-zA-Z\\d+\\-.]*:/.test(e)){return false}return true}e.exports=function(e,r){if(a){console.log(\"no window.document found, will not HMR CSS\");return noop}var t=getCurrentScriptUrl(e);function update(){var e=t(r.filename);var n=reloadStyle(e);if(r.locals){console.log(\"[HMR] Detected local css modules. Reload all css\");reloadAll();return}if(n){console.log(\"[HMR] css reload %s\",e.join(\" \"))}else{console.log(\"[HMR] Reload all css\");reloadAll()}}return debounce(update,50)}},191:e=>{function normalizeUrl(e){return e.reduce((function(e,r){switch(r){case\"..\":e.pop();break;case\".\":break;default:e.push(r)}return e}),[]).join(\"/\")}e.exports=function(e){e=e.trim();if(/^data:/i.test(e)){return e}var r=e.indexOf(\"//\")!==-1?e.split(\"//\")[0]+\"//\":\"\";var t=e.replace(new RegExp(r,\"i\"),\"\").split(\"/\");var n=t[0].toLowerCase().replace(/\\.$/,\"\");t[0]=\"\";var i=normalizeUrl(t);return r+n+i}}};var r={};function __nccwpck_require__(t){var n=r[t];if(n!==undefined){return n.exports}var i=r[t]={exports:{}};var a=true;try{e[t](i,i.exports,__nccwpck_require__);a=false}finally{if(a)delete r[t]}return i.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var t=__nccwpck_require__(529);module.exports=t})();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PORTAL_TYPE:\n          return \"Portal\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          },\n      specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        Error(\"react-stack-top-frame\"),\n        createTask(getTaskName(type))\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkM6XFxDb2RlXFxvbmUtbWFpbFxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxjb21waWxlZFxccmVhY3RcXGpzeC1kZXYtcnVudGltZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24uanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLmRldmVsb3BtZW50LmpzJyk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":
/*!*********************************************************************************************************************************************************!*\
  !*** ./node_modules/next/font/google/target.css?{"path":"app\\layout.tsx","import":"Inter","arguments":[{"subsets":["latin"]}],"variableName":"inter"} ***!
  \*********************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"style\":{\"fontFamily\":\"'Inter', 'Inter Fallback'\",\"fontStyle\":\"normal\"},\"className\":\"__className_e8ce0c\"};\n    if(true) {\n      // 1754814754644\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2ZvbnQvZ29vZ2xlL3RhcmdldC5jc3M/e1wicGF0aFwiOlwiYXBwXFxcXGxheW91dC50c3hcIixcImltcG9ydFwiOlwiSW50ZXJcIixcImFyZ3VtZW50c1wiOlt7XCJzdWJzZXRzXCI6W1wibGF0aW5cIl19XSxcInZhcmlhYmxlTmFtZVwiOlwiaW50ZXJcIn0iLCJtYXBwaW5ncyI6IkFBQUE7QUFDQSxrQkFBa0IsU0FBUyw4REFBOEQ7QUFDekYsT0FBTyxJQUFVO0FBQ2pCO0FBQ0Esc0JBQXNCLG1CQUFPLENBQUMsd01BQXNHLGNBQWMsc0RBQXNEO0FBQ3hNLE1BQU0sVUFBVTtBQUNoQjtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxDb2RlXFxvbmUtbWFpbFxcbm9kZV9tb2R1bGVzXFxuZXh0XFxmb250XFxnb29nbGVcXHRhcmdldC5jc3M/e1wicGF0aFwiOlwiYXBwXFxsYXlvdXQudHN4XCIsXCJpbXBvcnRcIjpcIkludGVyXCIsXCJhcmd1bWVudHNcIjpbe1wic3Vic2V0c1wiOltcImxhdGluXCJdfV0sXCJ2YXJpYWJsZU5hbWVcIjpcImludGVyXCJ9fGFwcC1wYWdlcy1icm93c2VyIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIGV4dHJhY3RlZCBieSBtaW5pLWNzcy1leHRyYWN0LXBsdWdpblxubW9kdWxlLmV4cG9ydHMgPSB7XCJzdHlsZVwiOntcImZvbnRGYW1pbHlcIjpcIidJbnRlcicsICdJbnRlciBGYWxsYmFjaydcIixcImZvbnRTdHlsZVwiOlwibm9ybWFsXCJ9LFwiY2xhc3NOYW1lXCI6XCJfX2NsYXNzTmFtZV9lOGNlMGNcIn07XG4gICAgaWYobW9kdWxlLmhvdCkge1xuICAgICAgLy8gMTc1NDgxNDc1NDY0NFxuICAgICAgdmFyIGNzc1JlbG9hZCA9IHJlcXVpcmUoXCJDOi9Db2RlL29uZS1tYWlsL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvbWluaS1jc3MtZXh0cmFjdC1wbHVnaW4vaG1yL2hvdE1vZHVsZVJlcGxhY2VtZW50LmpzXCIpKG1vZHVsZS5pZCwge1wicHVibGljUGF0aFwiOlwiL19uZXh0L1wiLFwiZXNNb2R1bGVcIjpmYWxzZSxcImxvY2Fsc1wiOnRydWV9KTtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShjc3NSZWxvYWQpO1xuICAgICAgXG4gICAgfVxuICAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CCode%5C%5Cone-mail%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCode%5C%5Cone-mail%5C%5Ccomponents%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCode%5C%5Cone-mail%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CCode%5C%5Cone-mail%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);