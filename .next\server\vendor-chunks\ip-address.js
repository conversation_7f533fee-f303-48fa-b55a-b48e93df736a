"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/ip-address";
exports.ids = ["vendor-chunks/ip-address"];
exports.modules = {

/***/ "(rsc)/./node_modules/ip-address/dist/address-error.js":
/*!*******************************************************!*\
  !*** ./node_modules/ip-address/dist/address-error.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AddressError = void 0;\nclass AddressError extends Error {\n    constructor(message, parseMessage) {\n        super(message);\n        this.name = 'AddressError';\n        if (parseMessage !== null) {\n            this.parseMessage = parseMessage;\n        }\n    }\n}\nexports.AddressError = AddressError;\n//# sourceMappingURL=address-error.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvaXAtYWRkcmVzcy9kaXN0L2FkZHJlc3MtZXJyb3IuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsb0JBQW9CO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQjtBQUNwQiIsInNvdXJjZXMiOlsiQzpcXENvZGVcXG9uZS1tYWlsXFxub2RlX21vZHVsZXNcXGlwLWFkZHJlc3NcXGRpc3RcXGFkZHJlc3MtZXJyb3IuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLkFkZHJlc3NFcnJvciA9IHZvaWQgMDtcbmNsYXNzIEFkZHJlc3NFcnJvciBleHRlbmRzIEVycm9yIHtcbiAgICBjb25zdHJ1Y3RvcihtZXNzYWdlLCBwYXJzZU1lc3NhZ2UpIHtcbiAgICAgICAgc3VwZXIobWVzc2FnZSk7XG4gICAgICAgIHRoaXMubmFtZSA9ICdBZGRyZXNzRXJyb3InO1xuICAgICAgICBpZiAocGFyc2VNZXNzYWdlICE9PSBudWxsKSB7XG4gICAgICAgICAgICB0aGlzLnBhcnNlTWVzc2FnZSA9IHBhcnNlTWVzc2FnZTtcbiAgICAgICAgfVxuICAgIH1cbn1cbmV4cG9ydHMuQWRkcmVzc0Vycm9yID0gQWRkcmVzc0Vycm9yO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YWRkcmVzcy1lcnJvci5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/ip-address/dist/address-error.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/ip-address/dist/common.js":
/*!************************************************!*\
  !*** ./node_modules/ip-address/dist/common.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isCorrect = exports.isInSubnet = void 0;\nfunction isInSubnet(address) {\n    if (this.subnetMask < address.subnetMask) {\n        return false;\n    }\n    if (this.mask(address.subnetMask) === address.mask()) {\n        return true;\n    }\n    return false;\n}\nexports.isInSubnet = isInSubnet;\nfunction isCorrect(defaultBits) {\n    return function () {\n        if (this.addressMinusSuffix !== this.correctForm()) {\n            return false;\n        }\n        if (this.subnetMask === defaultBits && !this.parsedSubnet) {\n            return true;\n        }\n        return this.parsedSubnet === String(this.subnetMask);\n    };\n}\nexports.isCorrect = isCorrect;\n//# sourceMappingURL=common.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvaXAtYWRkcmVzcy9kaXN0L2NvbW1vbi5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxpQkFBaUIsR0FBRyxrQkFBa0I7QUFDdEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakIiLCJzb3VyY2VzIjpbIkM6XFxDb2RlXFxvbmUtbWFpbFxcbm9kZV9tb2R1bGVzXFxpcC1hZGRyZXNzXFxkaXN0XFxjb21tb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLmlzQ29ycmVjdCA9IGV4cG9ydHMuaXNJblN1Ym5ldCA9IHZvaWQgMDtcbmZ1bmN0aW9uIGlzSW5TdWJuZXQoYWRkcmVzcykge1xuICAgIGlmICh0aGlzLnN1Ym5ldE1hc2sgPCBhZGRyZXNzLnN1Ym5ldE1hc2spIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBpZiAodGhpcy5tYXNrKGFkZHJlc3Muc3VibmV0TWFzaykgPT09IGFkZHJlc3MubWFzaygpKSB7XG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cbiAgICByZXR1cm4gZmFsc2U7XG59XG5leHBvcnRzLmlzSW5TdWJuZXQgPSBpc0luU3VibmV0O1xuZnVuY3Rpb24gaXNDb3JyZWN0KGRlZmF1bHRCaXRzKSB7XG4gICAgcmV0dXJuIGZ1bmN0aW9uICgpIHtcbiAgICAgICAgaWYgKHRoaXMuYWRkcmVzc01pbnVzU3VmZml4ICE9PSB0aGlzLmNvcnJlY3RGb3JtKCkpIHtcbiAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodGhpcy5zdWJuZXRNYXNrID09PSBkZWZhdWx0Qml0cyAmJiAhdGhpcy5wYXJzZWRTdWJuZXQpIHtcbiAgICAgICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB0aGlzLnBhcnNlZFN1Ym5ldCA9PT0gU3RyaW5nKHRoaXMuc3VibmV0TWFzayk7XG4gICAgfTtcbn1cbmV4cG9ydHMuaXNDb3JyZWN0ID0gaXNDb3JyZWN0O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y29tbW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/ip-address/dist/common.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/ip-address/dist/ip-address.js":
/*!****************************************************!*\
  !*** ./node_modules/ip-address/dist/ip-address.js ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.v6 = exports.AddressError = exports.Address6 = exports.Address4 = void 0;\nconst ipv4_1 = __webpack_require__(/*! ./ipv4 */ \"(rsc)/./node_modules/ip-address/dist/ipv4.js\");\nObject.defineProperty(exports, \"Address4\", ({ enumerable: true, get: function () { return ipv4_1.Address4; } }));\nconst ipv6_1 = __webpack_require__(/*! ./ipv6 */ \"(rsc)/./node_modules/ip-address/dist/ipv6.js\");\nObject.defineProperty(exports, \"Address6\", ({ enumerable: true, get: function () { return ipv6_1.Address6; } }));\nconst address_error_1 = __webpack_require__(/*! ./address-error */ \"(rsc)/./node_modules/ip-address/dist/address-error.js\");\nObject.defineProperty(exports, \"AddressError\", ({ enumerable: true, get: function () { return address_error_1.AddressError; } }));\nconst helpers = __importStar(__webpack_require__(/*! ./v6/helpers */ \"(rsc)/./node_modules/ip-address/dist/v6/helpers.js\"));\nexports.v6 = { helpers };\n//# sourceMappingURL=ip-address.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/ip-address/dist/ip-address.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/ip-address/dist/ipv4.js":
/*!**********************************************!*\
  !*** ./node_modules/ip-address/dist/ipv4.js ***!
  \**********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/* eslint-disable no-param-reassign */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Address4 = void 0;\nconst common = __importStar(__webpack_require__(/*! ./common */ \"(rsc)/./node_modules/ip-address/dist/common.js\"));\nconst constants = __importStar(__webpack_require__(/*! ./v4/constants */ \"(rsc)/./node_modules/ip-address/dist/v4/constants.js\"));\nconst address_error_1 = __webpack_require__(/*! ./address-error */ \"(rsc)/./node_modules/ip-address/dist/address-error.js\");\nconst jsbn_1 = __webpack_require__(/*! jsbn */ \"(rsc)/./node_modules/jsbn/index.js\");\nconst sprintf_js_1 = __webpack_require__(/*! sprintf-js */ \"(rsc)/./node_modules/sprintf-js/src/sprintf.js\");\n/**\n * Represents an IPv4 address\n * @class Address4\n * @param {string} address - An IPv4 address string\n */\nclass Address4 {\n    constructor(address) {\n        this.groups = constants.GROUPS;\n        this.parsedAddress = [];\n        this.parsedSubnet = '';\n        this.subnet = '/32';\n        this.subnetMask = 32;\n        this.v4 = true;\n        /**\n         * Returns true if the address is correct, false otherwise\n         * @memberof Address4\n         * @instance\n         * @returns {Boolean}\n         */\n        this.isCorrect = common.isCorrect(constants.BITS);\n        /**\n         * Returns true if the given address is in the subnet of the current address\n         * @memberof Address4\n         * @instance\n         * @returns {boolean}\n         */\n        this.isInSubnet = common.isInSubnet;\n        this.address = address;\n        const subnet = constants.RE_SUBNET_STRING.exec(address);\n        if (subnet) {\n            this.parsedSubnet = subnet[0].replace('/', '');\n            this.subnetMask = parseInt(this.parsedSubnet, 10);\n            this.subnet = `/${this.subnetMask}`;\n            if (this.subnetMask < 0 || this.subnetMask > constants.BITS) {\n                throw new address_error_1.AddressError('Invalid subnet mask.');\n            }\n            address = address.replace(constants.RE_SUBNET_STRING, '');\n        }\n        this.addressMinusSuffix = address;\n        this.parsedAddress = this.parse(address);\n    }\n    static isValid(address) {\n        try {\n            // eslint-disable-next-line no-new\n            new Address4(address);\n            return true;\n        }\n        catch (e) {\n            return false;\n        }\n    }\n    /*\n     * Parses a v4 address\n     */\n    parse(address) {\n        const groups = address.split('.');\n        if (!address.match(constants.RE_ADDRESS)) {\n            throw new address_error_1.AddressError('Invalid IPv4 address.');\n        }\n        return groups;\n    }\n    /**\n     * Returns the correct form of an address\n     * @memberof Address4\n     * @instance\n     * @returns {String}\n     */\n    correctForm() {\n        return this.parsedAddress.map((part) => parseInt(part, 10)).join('.');\n    }\n    /**\n     * Converts a hex string to an IPv4 address object\n     * @memberof Address4\n     * @static\n     * @param {string} hex - a hex string to convert\n     * @returns {Address4}\n     */\n    static fromHex(hex) {\n        const padded = hex.replace(/:/g, '').padStart(8, '0');\n        const groups = [];\n        let i;\n        for (i = 0; i < 8; i += 2) {\n            const h = padded.slice(i, i + 2);\n            groups.push(parseInt(h, 16));\n        }\n        return new Address4(groups.join('.'));\n    }\n    /**\n     * Converts an integer into a IPv4 address object\n     * @memberof Address4\n     * @static\n     * @param {integer} integer - a number to convert\n     * @returns {Address4}\n     */\n    static fromInteger(integer) {\n        return Address4.fromHex(integer.toString(16));\n    }\n    /**\n     * Return an address from in-addr.arpa form\n     * @memberof Address4\n     * @static\n     * @param {string} arpaFormAddress - an 'in-addr.arpa' form ipv4 address\n     * @returns {Adress4}\n     * @example\n     * var address = Address4.fromArpa(**********.in-addr.arpa.)\n     * address.correctForm(); // '**********'\n     */\n    static fromArpa(arpaFormAddress) {\n        // remove ending \".in-addr.arpa.\" or just \".\"\n        const leader = arpaFormAddress.replace(/(\\.in-addr\\.arpa)?\\.$/, '');\n        const address = leader.split('.').reverse().join('.');\n        return new Address4(address);\n    }\n    /**\n     * Converts an IPv4 address object to a hex string\n     * @memberof Address4\n     * @instance\n     * @returns {String}\n     */\n    toHex() {\n        return this.parsedAddress.map((part) => (0, sprintf_js_1.sprintf)('%02x', parseInt(part, 10))).join(':');\n    }\n    /**\n     * Converts an IPv4 address object to an array of bytes\n     * @memberof Address4\n     * @instance\n     * @returns {Array}\n     */\n    toArray() {\n        return this.parsedAddress.map((part) => parseInt(part, 10));\n    }\n    /**\n     * Converts an IPv4 address object to an IPv6 address group\n     * @memberof Address4\n     * @instance\n     * @returns {String}\n     */\n    toGroup6() {\n        const output = [];\n        let i;\n        for (i = 0; i < constants.GROUPS; i += 2) {\n            const hex = (0, sprintf_js_1.sprintf)('%02x%02x', parseInt(this.parsedAddress[i], 10), parseInt(this.parsedAddress[i + 1], 10));\n            output.push((0, sprintf_js_1.sprintf)('%x', parseInt(hex, 16)));\n        }\n        return output.join(':');\n    }\n    /**\n     * Returns the address as a BigInteger\n     * @memberof Address4\n     * @instance\n     * @returns {BigInteger}\n     */\n    bigInteger() {\n        return new jsbn_1.BigInteger(this.parsedAddress.map((n) => (0, sprintf_js_1.sprintf)('%02x', parseInt(n, 10))).join(''), 16);\n    }\n    /**\n     * Helper function getting start address.\n     * @memberof Address4\n     * @instance\n     * @returns {BigInteger}\n     */\n    _startAddress() {\n        return new jsbn_1.BigInteger(this.mask() + '0'.repeat(constants.BITS - this.subnetMask), 2);\n    }\n    /**\n     * The first address in the range given by this address' subnet.\n     * Often referred to as the Network Address.\n     * @memberof Address4\n     * @instance\n     * @returns {Address4}\n     */\n    startAddress() {\n        return Address4.fromBigInteger(this._startAddress());\n    }\n    /**\n     * The first host address in the range given by this address's subnet ie\n     * the first address after the Network Address\n     * @memberof Address4\n     * @instance\n     * @returns {Address4}\n     */\n    startAddressExclusive() {\n        const adjust = new jsbn_1.BigInteger('1');\n        return Address4.fromBigInteger(this._startAddress().add(adjust));\n    }\n    /**\n     * Helper function getting end address.\n     * @memberof Address4\n     * @instance\n     * @returns {BigInteger}\n     */\n    _endAddress() {\n        return new jsbn_1.BigInteger(this.mask() + '1'.repeat(constants.BITS - this.subnetMask), 2);\n    }\n    /**\n     * The last address in the range given by this address' subnet\n     * Often referred to as the Broadcast\n     * @memberof Address4\n     * @instance\n     * @returns {Address4}\n     */\n    endAddress() {\n        return Address4.fromBigInteger(this._endAddress());\n    }\n    /**\n     * The last host address in the range given by this address's subnet ie\n     * the last address prior to the Broadcast Address\n     * @memberof Address4\n     * @instance\n     * @returns {Address4}\n     */\n    endAddressExclusive() {\n        const adjust = new jsbn_1.BigInteger('1');\n        return Address4.fromBigInteger(this._endAddress().subtract(adjust));\n    }\n    /**\n     * Converts a BigInteger to a v4 address object\n     * @memberof Address4\n     * @static\n     * @param {BigInteger} bigInteger - a BigInteger to convert\n     * @returns {Address4}\n     */\n    static fromBigInteger(bigInteger) {\n        return Address4.fromInteger(parseInt(bigInteger.toString(), 10));\n    }\n    /**\n     * Returns the first n bits of the address, defaulting to the\n     * subnet mask\n     * @memberof Address4\n     * @instance\n     * @returns {String}\n     */\n    mask(mask) {\n        if (mask === undefined) {\n            mask = this.subnetMask;\n        }\n        return this.getBitsBase2(0, mask);\n    }\n    /**\n     * Returns the bits in the given range as a base-2 string\n     * @memberof Address4\n     * @instance\n     * @returns {string}\n     */\n    getBitsBase2(start, end) {\n        return this.binaryZeroPad().slice(start, end);\n    }\n    /**\n     * Return the reversed ip6.arpa form of the address\n     * @memberof Address4\n     * @param {Object} options\n     * @param {boolean} options.omitSuffix - omit the \"in-addr.arpa\" suffix\n     * @instance\n     * @returns {String}\n     */\n    reverseForm(options) {\n        if (!options) {\n            options = {};\n        }\n        const reversed = this.correctForm().split('.').reverse().join('.');\n        if (options.omitSuffix) {\n            return reversed;\n        }\n        return (0, sprintf_js_1.sprintf)('%s.in-addr.arpa.', reversed);\n    }\n    /**\n     * Returns true if the given address is a multicast address\n     * @memberof Address4\n     * @instance\n     * @returns {boolean}\n     */\n    isMulticast() {\n        return this.isInSubnet(new Address4('*********/4'));\n    }\n    /**\n     * Returns a zero-padded base-2 string representation of the address\n     * @memberof Address4\n     * @instance\n     * @returns {string}\n     */\n    binaryZeroPad() {\n        return this.bigInteger().toString(2).padStart(constants.BITS, '0');\n    }\n    /**\n     * Groups an IPv4 address for inclusion at the end of an IPv6 address\n     * @returns {String}\n     */\n    groupForV6() {\n        const segments = this.parsedAddress;\n        return this.address.replace(constants.RE_ADDRESS, (0, sprintf_js_1.sprintf)('<span class=\"hover-group group-v4 group-6\">%s</span>.<span class=\"hover-group group-v4 group-7\">%s</span>', segments.slice(0, 2).join('.'), segments.slice(2, 4).join('.')));\n    }\n}\nexports.Address4 = Address4;\n//# sourceMappingURL=ipv4.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/ip-address/dist/ipv4.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/ip-address/dist/ipv6.js":
/*!**********************************************!*\
  !*** ./node_modules/ip-address/dist/ipv6.js ***!
  \**********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/* eslint-disable prefer-destructuring */\n/* eslint-disable no-param-reassign */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Address6 = void 0;\nconst common = __importStar(__webpack_require__(/*! ./common */ \"(rsc)/./node_modules/ip-address/dist/common.js\"));\nconst constants4 = __importStar(__webpack_require__(/*! ./v4/constants */ \"(rsc)/./node_modules/ip-address/dist/v4/constants.js\"));\nconst constants6 = __importStar(__webpack_require__(/*! ./v6/constants */ \"(rsc)/./node_modules/ip-address/dist/v6/constants.js\"));\nconst helpers = __importStar(__webpack_require__(/*! ./v6/helpers */ \"(rsc)/./node_modules/ip-address/dist/v6/helpers.js\"));\nconst ipv4_1 = __webpack_require__(/*! ./ipv4 */ \"(rsc)/./node_modules/ip-address/dist/ipv4.js\");\nconst regular_expressions_1 = __webpack_require__(/*! ./v6/regular-expressions */ \"(rsc)/./node_modules/ip-address/dist/v6/regular-expressions.js\");\nconst address_error_1 = __webpack_require__(/*! ./address-error */ \"(rsc)/./node_modules/ip-address/dist/address-error.js\");\nconst jsbn_1 = __webpack_require__(/*! jsbn */ \"(rsc)/./node_modules/jsbn/index.js\");\nconst sprintf_js_1 = __webpack_require__(/*! sprintf-js */ \"(rsc)/./node_modules/sprintf-js/src/sprintf.js\");\nfunction assert(condition) {\n    if (!condition) {\n        throw new Error('Assertion failed.');\n    }\n}\nfunction addCommas(number) {\n    const r = /(\\d+)(\\d{3})/;\n    while (r.test(number)) {\n        number = number.replace(r, '$1,$2');\n    }\n    return number;\n}\nfunction spanLeadingZeroes4(n) {\n    n = n.replace(/^(0{1,})([1-9]+)$/, '<span class=\"parse-error\">$1</span>$2');\n    n = n.replace(/^(0{1,})(0)$/, '<span class=\"parse-error\">$1</span>$2');\n    return n;\n}\n/*\n * A helper function to compact an array\n */\nfunction compact(address, slice) {\n    const s1 = [];\n    const s2 = [];\n    let i;\n    for (i = 0; i < address.length; i++) {\n        if (i < slice[0]) {\n            s1.push(address[i]);\n        }\n        else if (i > slice[1]) {\n            s2.push(address[i]);\n        }\n    }\n    return s1.concat(['compact']).concat(s2);\n}\nfunction paddedHex(octet) {\n    return (0, sprintf_js_1.sprintf)('%04x', parseInt(octet, 16));\n}\nfunction unsignByte(b) {\n    // eslint-disable-next-line no-bitwise\n    return b & 0xff;\n}\n/**\n * Represents an IPv6 address\n * @class Address6\n * @param {string} address - An IPv6 address string\n * @param {number} [groups=8] - How many octets to parse\n * @example\n * var address = new Address6('2001::/32');\n */\nclass Address6 {\n    constructor(address, optionalGroups) {\n        this.addressMinusSuffix = '';\n        this.parsedSubnet = '';\n        this.subnet = '/128';\n        this.subnetMask = 128;\n        this.v4 = false;\n        this.zone = '';\n        // #region Attributes\n        /**\n         * Returns true if the given address is in the subnet of the current address\n         * @memberof Address6\n         * @instance\n         * @returns {boolean}\n         */\n        this.isInSubnet = common.isInSubnet;\n        /**\n         * Returns true if the address is correct, false otherwise\n         * @memberof Address6\n         * @instance\n         * @returns {boolean}\n         */\n        this.isCorrect = common.isCorrect(constants6.BITS);\n        if (optionalGroups === undefined) {\n            this.groups = constants6.GROUPS;\n        }\n        else {\n            this.groups = optionalGroups;\n        }\n        this.address = address;\n        const subnet = constants6.RE_SUBNET_STRING.exec(address);\n        if (subnet) {\n            this.parsedSubnet = subnet[0].replace('/', '');\n            this.subnetMask = parseInt(this.parsedSubnet, 10);\n            this.subnet = `/${this.subnetMask}`;\n            if (Number.isNaN(this.subnetMask) ||\n                this.subnetMask < 0 ||\n                this.subnetMask > constants6.BITS) {\n                throw new address_error_1.AddressError('Invalid subnet mask.');\n            }\n            address = address.replace(constants6.RE_SUBNET_STRING, '');\n        }\n        else if (/\\//.test(address)) {\n            throw new address_error_1.AddressError('Invalid subnet mask.');\n        }\n        const zone = constants6.RE_ZONE_STRING.exec(address);\n        if (zone) {\n            this.zone = zone[0];\n            address = address.replace(constants6.RE_ZONE_STRING, '');\n        }\n        this.addressMinusSuffix = address;\n        this.parsedAddress = this.parse(this.addressMinusSuffix);\n    }\n    static isValid(address) {\n        try {\n            // eslint-disable-next-line no-new\n            new Address6(address);\n            return true;\n        }\n        catch (e) {\n            return false;\n        }\n    }\n    /**\n     * Convert a BigInteger to a v6 address object\n     * @memberof Address6\n     * @static\n     * @param {BigInteger} bigInteger - a BigInteger to convert\n     * @returns {Address6}\n     * @example\n     * var bigInteger = new BigInteger('1000000000000');\n     * var address = Address6.fromBigInteger(bigInteger);\n     * address.correctForm(); // '::e8:d4a5:1000'\n     */\n    static fromBigInteger(bigInteger) {\n        const hex = bigInteger.toString(16).padStart(32, '0');\n        const groups = [];\n        let i;\n        for (i = 0; i < constants6.GROUPS; i++) {\n            groups.push(hex.slice(i * 4, (i + 1) * 4));\n        }\n        return new Address6(groups.join(':'));\n    }\n    /**\n     * Convert a URL (with optional port number) to an address object\n     * @memberof Address6\n     * @static\n     * @param {string} url - a URL with optional port number\n     * @example\n     * var addressAndPort = Address6.fromURL('http://[ffff::]:8080/foo/');\n     * addressAndPort.address.correctForm(); // 'ffff::'\n     * addressAndPort.port; // 8080\n     */\n    static fromURL(url) {\n        let host;\n        let port = null;\n        let result;\n        // If we have brackets parse them and find a port\n        if (url.indexOf('[') !== -1 && url.indexOf(']:') !== -1) {\n            result = constants6.RE_URL_WITH_PORT.exec(url);\n            if (result === null) {\n                return {\n                    error: 'failed to parse address with port',\n                    address: null,\n                    port: null,\n                };\n            }\n            host = result[1];\n            port = result[2];\n            // If there's a URL extract the address\n        }\n        else if (url.indexOf('/') !== -1) {\n            // Remove the protocol prefix\n            url = url.replace(/^[a-z0-9]+:\\/\\//, '');\n            // Parse the address\n            result = constants6.RE_URL.exec(url);\n            if (result === null) {\n                return {\n                    error: 'failed to parse address from URL',\n                    address: null,\n                    port: null,\n                };\n            }\n            host = result[1];\n            // Otherwise just assign the URL to the host and let the library parse it\n        }\n        else {\n            host = url;\n        }\n        // If there's a port convert it to an integer\n        if (port) {\n            port = parseInt(port, 10);\n            // squelch out of range ports\n            if (port < 0 || port > 65536) {\n                port = null;\n            }\n        }\n        else {\n            // Standardize `undefined` to `null`\n            port = null;\n        }\n        return {\n            address: new Address6(host),\n            port,\n        };\n    }\n    /**\n     * Create an IPv6-mapped address given an IPv4 address\n     * @memberof Address6\n     * @static\n     * @param {string} address - An IPv4 address string\n     * @returns {Address6}\n     * @example\n     * var address = Address6.fromAddress4('***********');\n     * address.correctForm(); // '::ffff:c0a8:1'\n     * address.to4in6(); // '::ffff:***********'\n     */\n    static fromAddress4(address) {\n        const address4 = new ipv4_1.Address4(address);\n        const mask6 = constants6.BITS - (constants4.BITS - address4.subnetMask);\n        return new Address6(`::ffff:${address4.correctForm()}/${mask6}`);\n    }\n    /**\n     * Return an address from ip6.arpa form\n     * @memberof Address6\n     * @static\n     * @param {string} arpaFormAddress - an 'ip6.arpa' form address\n     * @returns {Adress6}\n     * @example\n     * var address = Address6.fromArpa(e.f.f.f.3.c.2.6.f.f.f.e.6.6.8.e.*******.9.4.e.c.0.0.0.0.*******.ip6.arpa.)\n     * address.correctForm(); // '2001:0:ce49:7601:e866:efff:62c3:fffe'\n     */\n    static fromArpa(arpaFormAddress) {\n        // remove ending \".ip6.arpa.\" or just \".\"\n        let address = arpaFormAddress.replace(/(\\.ip6\\.arpa)?\\.$/, '');\n        const semicolonAmount = 7;\n        // correct ip6.arpa form with ending removed will be 63 characters\n        if (address.length !== 63) {\n            throw new address_error_1.AddressError(\"Invalid 'ip6.arpa' form.\");\n        }\n        const parts = address.split('.').reverse();\n        for (let i = semicolonAmount; i > 0; i--) {\n            const insertIndex = i * 4;\n            parts.splice(insertIndex, 0, ':');\n        }\n        address = parts.join('');\n        return new Address6(address);\n    }\n    /**\n     * Return the Microsoft UNC transcription of the address\n     * @memberof Address6\n     * @instance\n     * @returns {String} the Microsoft UNC transcription of the address\n     */\n    microsoftTranscription() {\n        return (0, sprintf_js_1.sprintf)('%s.ipv6-literal.net', this.correctForm().replace(/:/g, '-'));\n    }\n    /**\n     * Return the first n bits of the address, defaulting to the subnet mask\n     * @memberof Address6\n     * @instance\n     * @param {number} [mask=subnet] - the number of bits to mask\n     * @returns {String} the first n bits of the address as a string\n     */\n    mask(mask = this.subnetMask) {\n        return this.getBitsBase2(0, mask);\n    }\n    /**\n     * Return the number of possible subnets of a given size in the address\n     * @memberof Address6\n     * @instance\n     * @param {number} [size=128] - the subnet size\n     * @returns {String}\n     */\n    // TODO: probably useful to have a numeric version of this too\n    possibleSubnets(subnetSize = 128) {\n        const availableBits = constants6.BITS - this.subnetMask;\n        const subnetBits = Math.abs(subnetSize - constants6.BITS);\n        const subnetPowers = availableBits - subnetBits;\n        if (subnetPowers < 0) {\n            return '0';\n        }\n        return addCommas(new jsbn_1.BigInteger('2', 10).pow(subnetPowers).toString(10));\n    }\n    /**\n     * Helper function getting start address.\n     * @memberof Address6\n     * @instance\n     * @returns {BigInteger}\n     */\n    _startAddress() {\n        return new jsbn_1.BigInteger(this.mask() + '0'.repeat(constants6.BITS - this.subnetMask), 2);\n    }\n    /**\n     * The first address in the range given by this address' subnet\n     * Often referred to as the Network Address.\n     * @memberof Address6\n     * @instance\n     * @returns {Address6}\n     */\n    startAddress() {\n        return Address6.fromBigInteger(this._startAddress());\n    }\n    /**\n     * The first host address in the range given by this address's subnet ie\n     * the first address after the Network Address\n     * @memberof Address6\n     * @instance\n     * @returns {Address6}\n     */\n    startAddressExclusive() {\n        const adjust = new jsbn_1.BigInteger('1');\n        return Address6.fromBigInteger(this._startAddress().add(adjust));\n    }\n    /**\n     * Helper function getting end address.\n     * @memberof Address6\n     * @instance\n     * @returns {BigInteger}\n     */\n    _endAddress() {\n        return new jsbn_1.BigInteger(this.mask() + '1'.repeat(constants6.BITS - this.subnetMask), 2);\n    }\n    /**\n     * The last address in the range given by this address' subnet\n     * Often referred to as the Broadcast\n     * @memberof Address6\n     * @instance\n     * @returns {Address6}\n     */\n    endAddress() {\n        return Address6.fromBigInteger(this._endAddress());\n    }\n    /**\n     * The last host address in the range given by this address's subnet ie\n     * the last address prior to the Broadcast Address\n     * @memberof Address6\n     * @instance\n     * @returns {Address6}\n     */\n    endAddressExclusive() {\n        const adjust = new jsbn_1.BigInteger('1');\n        return Address6.fromBigInteger(this._endAddress().subtract(adjust));\n    }\n    /**\n     * Return the scope of the address\n     * @memberof Address6\n     * @instance\n     * @returns {String}\n     */\n    getScope() {\n        let scope = constants6.SCOPES[this.getBits(12, 16).intValue()];\n        if (this.getType() === 'Global unicast' && scope !== 'Link local') {\n            scope = 'Global';\n        }\n        return scope || 'Unknown';\n    }\n    /**\n     * Return the type of the address\n     * @memberof Address6\n     * @instance\n     * @returns {String}\n     */\n    getType() {\n        for (const subnet of Object.keys(constants6.TYPES)) {\n            if (this.isInSubnet(new Address6(subnet))) {\n                return constants6.TYPES[subnet];\n            }\n        }\n        return 'Global unicast';\n    }\n    /**\n     * Return the bits in the given range as a BigInteger\n     * @memberof Address6\n     * @instance\n     * @returns {BigInteger}\n     */\n    getBits(start, end) {\n        return new jsbn_1.BigInteger(this.getBitsBase2(start, end), 2);\n    }\n    /**\n     * Return the bits in the given range as a base-2 string\n     * @memberof Address6\n     * @instance\n     * @returns {String}\n     */\n    getBitsBase2(start, end) {\n        return this.binaryZeroPad().slice(start, end);\n    }\n    /**\n     * Return the bits in the given range as a base-16 string\n     * @memberof Address6\n     * @instance\n     * @returns {String}\n     */\n    getBitsBase16(start, end) {\n        const length = end - start;\n        if (length % 4 !== 0) {\n            throw new Error('Length of bits to retrieve must be divisible by four');\n        }\n        return this.getBits(start, end)\n            .toString(16)\n            .padStart(length / 4, '0');\n    }\n    /**\n     * Return the bits that are set past the subnet mask length\n     * @memberof Address6\n     * @instance\n     * @returns {String}\n     */\n    getBitsPastSubnet() {\n        return this.getBitsBase2(this.subnetMask, constants6.BITS);\n    }\n    /**\n     * Return the reversed ip6.arpa form of the address\n     * @memberof Address6\n     * @param {Object} options\n     * @param {boolean} options.omitSuffix - omit the \"ip6.arpa\" suffix\n     * @instance\n     * @returns {String}\n     */\n    reverseForm(options) {\n        if (!options) {\n            options = {};\n        }\n        const characters = Math.floor(this.subnetMask / 4);\n        const reversed = this.canonicalForm()\n            .replace(/:/g, '')\n            .split('')\n            .slice(0, characters)\n            .reverse()\n            .join('.');\n        if (characters > 0) {\n            if (options.omitSuffix) {\n                return reversed;\n            }\n            return (0, sprintf_js_1.sprintf)('%s.ip6.arpa.', reversed);\n        }\n        if (options.omitSuffix) {\n            return '';\n        }\n        return 'ip6.arpa.';\n    }\n    /**\n     * Return the correct form of the address\n     * @memberof Address6\n     * @instance\n     * @returns {String}\n     */\n    correctForm() {\n        let i;\n        let groups = [];\n        let zeroCounter = 0;\n        const zeroes = [];\n        for (i = 0; i < this.parsedAddress.length; i++) {\n            const value = parseInt(this.parsedAddress[i], 16);\n            if (value === 0) {\n                zeroCounter++;\n            }\n            if (value !== 0 && zeroCounter > 0) {\n                if (zeroCounter > 1) {\n                    zeroes.push([i - zeroCounter, i - 1]);\n                }\n                zeroCounter = 0;\n            }\n        }\n        // Do we end with a string of zeroes?\n        if (zeroCounter > 1) {\n            zeroes.push([this.parsedAddress.length - zeroCounter, this.parsedAddress.length - 1]);\n        }\n        const zeroLengths = zeroes.map((n) => n[1] - n[0] + 1);\n        if (zeroes.length > 0) {\n            const index = zeroLengths.indexOf(Math.max(...zeroLengths));\n            groups = compact(this.parsedAddress, zeroes[index]);\n        }\n        else {\n            groups = this.parsedAddress;\n        }\n        for (i = 0; i < groups.length; i++) {\n            if (groups[i] !== 'compact') {\n                groups[i] = parseInt(groups[i], 16).toString(16);\n            }\n        }\n        let correct = groups.join(':');\n        correct = correct.replace(/^compact$/, '::');\n        correct = correct.replace(/^compact|compact$/, ':');\n        correct = correct.replace(/compact/, '');\n        return correct;\n    }\n    /**\n     * Return a zero-padded base-2 string representation of the address\n     * @memberof Address6\n     * @instance\n     * @returns {String}\n     * @example\n     * var address = new Address6('2001:4860:4001:803::1011');\n     * address.binaryZeroPad();\n     * // '0010000000000001010010000110000001000000000000010000100000000011\n     * //  0000000000000000000000000000000000000000000000000001000000010001'\n     */\n    binaryZeroPad() {\n        return this.bigInteger().toString(2).padStart(constants6.BITS, '0');\n    }\n    // TODO: Improve the semantics of this helper function\n    parse4in6(address) {\n        const groups = address.split(':');\n        const lastGroup = groups.slice(-1)[0];\n        const address4 = lastGroup.match(constants4.RE_ADDRESS);\n        if (address4) {\n            this.parsedAddress4 = address4[0];\n            this.address4 = new ipv4_1.Address4(this.parsedAddress4);\n            for (let i = 0; i < this.address4.groups; i++) {\n                if (/^0[0-9]+/.test(this.address4.parsedAddress[i])) {\n                    throw new address_error_1.AddressError(\"IPv4 addresses can't have leading zeroes.\", address.replace(constants4.RE_ADDRESS, this.address4.parsedAddress.map(spanLeadingZeroes4).join('.')));\n                }\n            }\n            this.v4 = true;\n            groups[groups.length - 1] = this.address4.toGroup6();\n            address = groups.join(':');\n        }\n        return address;\n    }\n    // TODO: Make private?\n    parse(address) {\n        address = this.parse4in6(address);\n        const badCharacters = address.match(constants6.RE_BAD_CHARACTERS);\n        if (badCharacters) {\n            throw new address_error_1.AddressError((0, sprintf_js_1.sprintf)('Bad character%s detected in address: %s', badCharacters.length > 1 ? 's' : '', badCharacters.join('')), address.replace(constants6.RE_BAD_CHARACTERS, '<span class=\"parse-error\">$1</span>'));\n        }\n        const badAddress = address.match(constants6.RE_BAD_ADDRESS);\n        if (badAddress) {\n            throw new address_error_1.AddressError((0, sprintf_js_1.sprintf)('Address failed regex: %s', badAddress.join('')), address.replace(constants6.RE_BAD_ADDRESS, '<span class=\"parse-error\">$1</span>'));\n        }\n        let groups = [];\n        const halves = address.split('::');\n        if (halves.length === 2) {\n            let first = halves[0].split(':');\n            let last = halves[1].split(':');\n            if (first.length === 1 && first[0] === '') {\n                first = [];\n            }\n            if (last.length === 1 && last[0] === '') {\n                last = [];\n            }\n            const remaining = this.groups - (first.length + last.length);\n            if (!remaining) {\n                throw new address_error_1.AddressError('Error parsing groups');\n            }\n            this.elidedGroups = remaining;\n            this.elisionBegin = first.length;\n            this.elisionEnd = first.length + this.elidedGroups;\n            groups = groups.concat(first);\n            for (let i = 0; i < remaining; i++) {\n                groups.push('0');\n            }\n            groups = groups.concat(last);\n        }\n        else if (halves.length === 1) {\n            groups = address.split(':');\n            this.elidedGroups = 0;\n        }\n        else {\n            throw new address_error_1.AddressError('Too many :: groups found');\n        }\n        groups = groups.map((group) => (0, sprintf_js_1.sprintf)('%x', parseInt(group, 16)));\n        if (groups.length !== this.groups) {\n            throw new address_error_1.AddressError('Incorrect number of groups found');\n        }\n        return groups;\n    }\n    /**\n     * Return the canonical form of the address\n     * @memberof Address6\n     * @instance\n     * @returns {String}\n     */\n    canonicalForm() {\n        return this.parsedAddress.map(paddedHex).join(':');\n    }\n    /**\n     * Return the decimal form of the address\n     * @memberof Address6\n     * @instance\n     * @returns {String}\n     */\n    decimal() {\n        return this.parsedAddress.map((n) => (0, sprintf_js_1.sprintf)('%05d', parseInt(n, 16))).join(':');\n    }\n    /**\n     * Return the address as a BigInteger\n     * @memberof Address6\n     * @instance\n     * @returns {BigInteger}\n     */\n    bigInteger() {\n        return new jsbn_1.BigInteger(this.parsedAddress.map(paddedHex).join(''), 16);\n    }\n    /**\n     * Return the last two groups of this address as an IPv4 address string\n     * @memberof Address6\n     * @instance\n     * @returns {Address4}\n     * @example\n     * var address = new Address6('2001:4860:4001::1825:bf11');\n     * address.to4().correctForm(); // '************'\n     */\n    to4() {\n        const binary = this.binaryZeroPad().split('');\n        return ipv4_1.Address4.fromHex(new jsbn_1.BigInteger(binary.slice(96, 128).join(''), 2).toString(16));\n    }\n    /**\n     * Return the v4-in-v6 form of the address\n     * @memberof Address6\n     * @instance\n     * @returns {String}\n     */\n    to4in6() {\n        const address4 = this.to4();\n        const address6 = new Address6(this.parsedAddress.slice(0, 6).join(':'), 6);\n        const correct = address6.correctForm();\n        let infix = '';\n        if (!/:$/.test(correct)) {\n            infix = ':';\n        }\n        return correct + infix + address4.address;\n    }\n    /**\n     * Return an object containing the Teredo properties of the address\n     * @memberof Address6\n     * @instance\n     * @returns {Object}\n     */\n    inspectTeredo() {\n        /*\n        - Bits 0 to 31 are set to the Teredo prefix (normally 2001:0000::/32).\n        - Bits 32 to 63 embed the primary IPv4 address of the Teredo server that\n          is used.\n        - Bits 64 to 79 can be used to define some flags. Currently only the\n          higher order bit is used; it is set to 1 if the Teredo client is\n          located behind a cone NAT, 0 otherwise. For Microsoft's Windows Vista\n          and Windows Server 2008 implementations, more bits are used. In those\n          implementations, the format for these 16 bits is \"CRAAAAUG AAAAAAAA\",\n          where \"C\" remains the \"Cone\" flag. The \"R\" bit is reserved for future\n          use. The \"U\" bit is for the Universal/Local flag (set to 0). The \"G\" bit\n          is Individual/Group flag (set to 0). The A bits are set to a 12-bit\n          randomly generated number chosen by the Teredo client to introduce\n          additional protection for the Teredo node against IPv6-based scanning\n          attacks.\n        - Bits 80 to 95 contains the obfuscated UDP port number. This is the\n          port number that is mapped by the NAT to the Teredo client with all\n          bits inverted.\n        - Bits 96 to 127 contains the obfuscated IPv4 address. This is the\n          public IPv4 address of the NAT with all bits inverted.\n        */\n        const prefix = this.getBitsBase16(0, 32);\n        const udpPort = this.getBits(80, 96).xor(new jsbn_1.BigInteger('ffff', 16)).toString();\n        const server4 = ipv4_1.Address4.fromHex(this.getBitsBase16(32, 64));\n        const client4 = ipv4_1.Address4.fromHex(this.getBits(96, 128).xor(new jsbn_1.BigInteger('ffffffff', 16)).toString(16));\n        const flags = this.getBits(64, 80);\n        const flagsBase2 = this.getBitsBase2(64, 80);\n        const coneNat = flags.testBit(15);\n        const reserved = flags.testBit(14);\n        const groupIndividual = flags.testBit(8);\n        const universalLocal = flags.testBit(9);\n        const nonce = new jsbn_1.BigInteger(flagsBase2.slice(2, 6) + flagsBase2.slice(8, 16), 2).toString(10);\n        return {\n            prefix: (0, sprintf_js_1.sprintf)('%s:%s', prefix.slice(0, 4), prefix.slice(4, 8)),\n            server4: server4.address,\n            client4: client4.address,\n            flags: flagsBase2,\n            coneNat,\n            microsoft: {\n                reserved,\n                universalLocal,\n                groupIndividual,\n                nonce,\n            },\n            udpPort,\n        };\n    }\n    /**\n     * Return an object containing the 6to4 properties of the address\n     * @memberof Address6\n     * @instance\n     * @returns {Object}\n     */\n    inspect6to4() {\n        /*\n        - Bits 0 to 15 are set to the 6to4 prefix (2002::/16).\n        - Bits 16 to 48 embed the IPv4 address of the 6to4 gateway that is used.\n        */\n        const prefix = this.getBitsBase16(0, 16);\n        const gateway = ipv4_1.Address4.fromHex(this.getBitsBase16(16, 48));\n        return {\n            prefix: (0, sprintf_js_1.sprintf)('%s', prefix.slice(0, 4)),\n            gateway: gateway.address,\n        };\n    }\n    /**\n     * Return a v6 6to4 address from a v6 v4inv6 address\n     * @memberof Address6\n     * @instance\n     * @returns {Address6}\n     */\n    to6to4() {\n        if (!this.is4()) {\n            return null;\n        }\n        const addr6to4 = [\n            '2002',\n            this.getBitsBase16(96, 112),\n            this.getBitsBase16(112, 128),\n            '',\n            '/16',\n        ].join(':');\n        return new Address6(addr6to4);\n    }\n    /**\n     * Return a byte array\n     * @memberof Address6\n     * @instance\n     * @returns {Array}\n     */\n    toByteArray() {\n        const byteArray = this.bigInteger().toByteArray();\n        // work around issue where `toByteArray` returns a leading 0 element\n        if (byteArray.length === 17 && byteArray[0] === 0) {\n            return byteArray.slice(1);\n        }\n        return byteArray;\n    }\n    /**\n     * Return an unsigned byte array\n     * @memberof Address6\n     * @instance\n     * @returns {Array}\n     */\n    toUnsignedByteArray() {\n        return this.toByteArray().map(unsignByte);\n    }\n    /**\n     * Convert a byte array to an Address6 object\n     * @memberof Address6\n     * @static\n     * @returns {Address6}\n     */\n    static fromByteArray(bytes) {\n        return this.fromUnsignedByteArray(bytes.map(unsignByte));\n    }\n    /**\n     * Convert an unsigned byte array to an Address6 object\n     * @memberof Address6\n     * @static\n     * @returns {Address6}\n     */\n    static fromUnsignedByteArray(bytes) {\n        const BYTE_MAX = new jsbn_1.BigInteger('256', 10);\n        let result = new jsbn_1.BigInteger('0', 10);\n        let multiplier = new jsbn_1.BigInteger('1', 10);\n        for (let i = bytes.length - 1; i >= 0; i--) {\n            result = result.add(multiplier.multiply(new jsbn_1.BigInteger(bytes[i].toString(10), 10)));\n            multiplier = multiplier.multiply(BYTE_MAX);\n        }\n        return Address6.fromBigInteger(result);\n    }\n    /**\n     * Returns true if the address is in the canonical form, false otherwise\n     * @memberof Address6\n     * @instance\n     * @returns {boolean}\n     */\n    isCanonical() {\n        return this.addressMinusSuffix === this.canonicalForm();\n    }\n    /**\n     * Returns true if the address is a link local address, false otherwise\n     * @memberof Address6\n     * @instance\n     * @returns {boolean}\n     */\n    isLinkLocal() {\n        // Zeroes are required, i.e. we can't check isInSubnet with 'fe80::/10'\n        if (this.getBitsBase2(0, 64) ===\n            '1111111010000000000000000000000000000000000000000000000000000000') {\n            return true;\n        }\n        return false;\n    }\n    /**\n     * Returns true if the address is a multicast address, false otherwise\n     * @memberof Address6\n     * @instance\n     * @returns {boolean}\n     */\n    isMulticast() {\n        return this.getType() === 'Multicast';\n    }\n    /**\n     * Returns true if the address is a v4-in-v6 address, false otherwise\n     * @memberof Address6\n     * @instance\n     * @returns {boolean}\n     */\n    is4() {\n        return this.v4;\n    }\n    /**\n     * Returns true if the address is a Teredo address, false otherwise\n     * @memberof Address6\n     * @instance\n     * @returns {boolean}\n     */\n    isTeredo() {\n        return this.isInSubnet(new Address6('2001::/32'));\n    }\n    /**\n     * Returns true if the address is a 6to4 address, false otherwise\n     * @memberof Address6\n     * @instance\n     * @returns {boolean}\n     */\n    is6to4() {\n        return this.isInSubnet(new Address6('2002::/16'));\n    }\n    /**\n     * Returns true if the address is a loopback address, false otherwise\n     * @memberof Address6\n     * @instance\n     * @returns {boolean}\n     */\n    isLoopback() {\n        return this.getType() === 'Loopback';\n    }\n    // #endregion\n    // #region HTML\n    /**\n     * @returns {String} the address in link form with a default port of 80\n     */\n    href(optionalPort) {\n        if (optionalPort === undefined) {\n            optionalPort = '';\n        }\n        else {\n            optionalPort = (0, sprintf_js_1.sprintf)(':%s', optionalPort);\n        }\n        return (0, sprintf_js_1.sprintf)('http://[%s]%s/', this.correctForm(), optionalPort);\n    }\n    /**\n     * @returns {String} a link suitable for conveying the address via a URL hash\n     */\n    link(options) {\n        if (!options) {\n            options = {};\n        }\n        if (options.className === undefined) {\n            options.className = '';\n        }\n        if (options.prefix === undefined) {\n            options.prefix = '/#address=';\n        }\n        if (options.v4 === undefined) {\n            options.v4 = false;\n        }\n        let formFunction = this.correctForm;\n        if (options.v4) {\n            formFunction = this.to4in6;\n        }\n        if (options.className) {\n            return (0, sprintf_js_1.sprintf)('<a href=\"%1$s%2$s\" class=\"%3$s\">%2$s</a>', options.prefix, formFunction.call(this), options.className);\n        }\n        return (0, sprintf_js_1.sprintf)('<a href=\"%1$s%2$s\">%2$s</a>', options.prefix, formFunction.call(this));\n    }\n    /**\n     * Groups an address\n     * @returns {String}\n     */\n    group() {\n        if (this.elidedGroups === 0) {\n            // The simple case\n            return helpers.simpleGroup(this.address).join(':');\n        }\n        assert(typeof this.elidedGroups === 'number');\n        assert(typeof this.elisionBegin === 'number');\n        // The elided case\n        const output = [];\n        const [left, right] = this.address.split('::');\n        if (left.length) {\n            output.push(...helpers.simpleGroup(left));\n        }\n        else {\n            output.push('');\n        }\n        const classes = ['hover-group'];\n        for (let i = this.elisionBegin; i < this.elisionBegin + this.elidedGroups; i++) {\n            classes.push((0, sprintf_js_1.sprintf)('group-%d', i));\n        }\n        output.push((0, sprintf_js_1.sprintf)('<span class=\"%s\"></span>', classes.join(' ')));\n        if (right.length) {\n            output.push(...helpers.simpleGroup(right, this.elisionEnd));\n        }\n        else {\n            output.push('');\n        }\n        if (this.is4()) {\n            assert(this.address4 instanceof ipv4_1.Address4);\n            output.pop();\n            output.push(this.address4.groupForV6());\n        }\n        return output.join(':');\n    }\n    // #endregion\n    // #region Regular expressions\n    /**\n     * Generate a regular expression string that can be used to find or validate\n     * all variations of this address\n     * @memberof Address6\n     * @instance\n     * @param {boolean} substringSearch\n     * @returns {string}\n     */\n    regularExpressionString(substringSearch = false) {\n        let output = [];\n        // TODO: revisit why this is necessary\n        const address6 = new Address6(this.correctForm());\n        if (address6.elidedGroups === 0) {\n            // The simple case\n            output.push((0, regular_expressions_1.simpleRegularExpression)(address6.parsedAddress));\n        }\n        else if (address6.elidedGroups === constants6.GROUPS) {\n            // A completely elided address\n            output.push((0, regular_expressions_1.possibleElisions)(constants6.GROUPS));\n        }\n        else {\n            // A partially elided address\n            const halves = address6.address.split('::');\n            if (halves[0].length) {\n                output.push((0, regular_expressions_1.simpleRegularExpression)(halves[0].split(':')));\n            }\n            assert(typeof address6.elidedGroups === 'number');\n            output.push((0, regular_expressions_1.possibleElisions)(address6.elidedGroups, halves[0].length !== 0, halves[1].length !== 0));\n            if (halves[1].length) {\n                output.push((0, regular_expressions_1.simpleRegularExpression)(halves[1].split(':')));\n            }\n            output = [output.join(':')];\n        }\n        if (!substringSearch) {\n            output = [\n                '(?=^|',\n                regular_expressions_1.ADDRESS_BOUNDARY,\n                '|[^\\\\w\\\\:])(',\n                ...output,\n                ')(?=[^\\\\w\\\\:]|',\n                regular_expressions_1.ADDRESS_BOUNDARY,\n                '|$)',\n            ];\n        }\n        return output.join('');\n    }\n    /**\n     * Generate a regular expression that can be used to find or validate all\n     * variations of this address.\n     * @memberof Address6\n     * @instance\n     * @param {boolean} substringSearch\n     * @returns {RegExp}\n     */\n    regularExpression(substringSearch = false) {\n        return new RegExp(this.regularExpressionString(substringSearch), 'i');\n    }\n}\nexports.Address6 = Address6;\n//# sourceMappingURL=ipv6.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/ip-address/dist/ipv6.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/ip-address/dist/v4/constants.js":
/*!******************************************************!*\
  !*** ./node_modules/ip-address/dist/v4/constants.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.RE_SUBNET_STRING = exports.RE_ADDRESS = exports.GROUPS = exports.BITS = void 0;\nexports.BITS = 32;\nexports.GROUPS = 4;\nexports.RE_ADDRESS = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/g;\nexports.RE_SUBNET_STRING = /\\/\\d{1,2}$/;\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvaXAtYWRkcmVzcy9kaXN0L3Y0L2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCx3QkFBd0IsR0FBRyxrQkFBa0IsR0FBRyxjQUFjLEdBQUcsWUFBWTtBQUM3RSxZQUFZO0FBQ1osY0FBYztBQUNkLGtCQUFrQjtBQUNsQix3QkFBd0IsU0FBUyxJQUFJO0FBQ3JDIiwic291cmNlcyI6WyJDOlxcQ29kZVxcb25lLW1haWxcXG5vZGVfbW9kdWxlc1xcaXAtYWRkcmVzc1xcZGlzdFxcdjRcXGNvbnN0YW50cy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuUkVfU1VCTkVUX1NUUklORyA9IGV4cG9ydHMuUkVfQUREUkVTUyA9IGV4cG9ydHMuR1JPVVBTID0gZXhwb3J0cy5CSVRTID0gdm9pZCAwO1xuZXhwb3J0cy5CSVRTID0gMzI7XG5leHBvcnRzLkdST1VQUyA9IDQ7XG5leHBvcnRzLlJFX0FERFJFU1MgPSAvXigyNVswLTVdfDJbMC00XVswLTldfFswMV0/WzAtOV1bMC05XT8pXFwuKDI1WzAtNV18MlswLTRdWzAtOV18WzAxXT9bMC05XVswLTldPylcXC4oMjVbMC01XXwyWzAtNF1bMC05XXxbMDFdP1swLTldWzAtOV0/KVxcLigyNVswLTVdfDJbMC00XVswLTldfFswMV0/WzAtOV1bMC05XT8pJC9nO1xuZXhwb3J0cy5SRV9TVUJORVRfU1RSSU5HID0gL1xcL1xcZHsxLDJ9JC87XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jb25zdGFudHMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/ip-address/dist/v4/constants.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/ip-address/dist/v6/constants.js":
/*!******************************************************!*\
  !*** ./node_modules/ip-address/dist/v6/constants.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.RE_URL_WITH_PORT = exports.RE_URL = exports.RE_ZONE_STRING = exports.RE_SUBNET_STRING = exports.RE_BAD_ADDRESS = exports.RE_BAD_CHARACTERS = exports.TYPES = exports.SCOPES = exports.GROUPS = exports.BITS = void 0;\nexports.BITS = 128;\nexports.GROUPS = 8;\n/**\n * Represents IPv6 address scopes\n * @memberof Address6\n * @static\n */\nexports.SCOPES = {\n    0: 'Reserved',\n    1: 'Interface local',\n    2: 'Link local',\n    4: 'Admin local',\n    5: 'Site local',\n    8: 'Organization local',\n    14: 'Global',\n    15: 'Reserved',\n};\n/**\n * Represents IPv6 address types\n * @memberof Address6\n * @static\n */\nexports.TYPES = {\n    'ff01::1/128': 'Multicast (All nodes on this interface)',\n    'ff01::2/128': 'Multicast (All routers on this interface)',\n    'ff02::1/128': 'Multicast (All nodes on this link)',\n    'ff02::2/128': 'Multicast (All routers on this link)',\n    'ff05::2/128': 'Multicast (All routers in this site)',\n    'ff02::5/128': 'Multicast (OSPFv3 AllSPF routers)',\n    'ff02::6/128': 'Multicast (OSPFv3 AllDR routers)',\n    'ff02::9/128': 'Multicast (RIP routers)',\n    'ff02::a/128': 'Multicast (EIGRP routers)',\n    'ff02::d/128': 'Multicast (PIM routers)',\n    'ff02::16/128': 'Multicast (MLDv2 reports)',\n    'ff01::fb/128': 'Multicast (mDNSv6)',\n    'ff02::fb/128': 'Multicast (mDNSv6)',\n    'ff05::fb/128': 'Multicast (mDNSv6)',\n    'ff02::1:2/128': 'Multicast (All DHCP servers and relay agents on this link)',\n    'ff05::1:2/128': 'Multicast (All DHCP servers and relay agents in this site)',\n    'ff02::1:3/128': 'Multicast (All DHCP servers on this link)',\n    'ff05::1:3/128': 'Multicast (All DHCP servers in this site)',\n    '::/128': 'Unspecified',\n    '::1/128': 'Loopback',\n    'ff00::/8': 'Multicast',\n    'fe80::/10': 'Link-local unicast',\n};\n/**\n * A regular expression that matches bad characters in an IPv6 address\n * @memberof Address6\n * @static\n */\nexports.RE_BAD_CHARACTERS = /([^0-9a-f:/%])/gi;\n/**\n * A regular expression that matches an incorrect IPv6 address\n * @memberof Address6\n * @static\n */\nexports.RE_BAD_ADDRESS = /([0-9a-f]{5,}|:{3,}|[^:]:$|^:[^:]|\\/$)/gi;\n/**\n * A regular expression that matches an IPv6 subnet\n * @memberof Address6\n * @static\n */\nexports.RE_SUBNET_STRING = /\\/\\d{1,3}(?=%|$)/;\n/**\n * A regular expression that matches an IPv6 zone\n * @memberof Address6\n * @static\n */\nexports.RE_ZONE_STRING = /%.*$/;\nexports.RE_URL = new RegExp(/^\\[{0,1}([0-9a-f:]+)\\]{0,1}/);\nexports.RE_URL_WITH_PORT = new RegExp(/\\[([0-9a-f:]+)\\]:([0-9]{1,5})/);\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/ip-address/dist/v6/constants.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/ip-address/dist/v6/helpers.js":
/*!****************************************************!*\
  !*** ./node_modules/ip-address/dist/v6/helpers.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.simpleGroup = exports.spanLeadingZeroes = exports.spanAll = exports.spanAllZeroes = void 0;\nconst sprintf_js_1 = __webpack_require__(/*! sprintf-js */ \"(rsc)/./node_modules/sprintf-js/src/sprintf.js\");\n/**\n * @returns {String} the string with all zeroes contained in a <span>\n */\nfunction spanAllZeroes(s) {\n    return s.replace(/(0+)/g, '<span class=\"zero\">$1</span>');\n}\nexports.spanAllZeroes = spanAllZeroes;\n/**\n * @returns {String} the string with each character contained in a <span>\n */\nfunction spanAll(s, offset = 0) {\n    const letters = s.split('');\n    return letters\n        .map((n, i) => (0, sprintf_js_1.sprintf)('<span class=\"digit value-%s position-%d\">%s</span>', n, i + offset, spanAllZeroes(n)) // XXX Use #base-2 .value-0 instead?\n    )\n        .join('');\n}\nexports.spanAll = spanAll;\nfunction spanLeadingZeroesSimple(group) {\n    return group.replace(/^(0+)/, '<span class=\"zero\">$1</span>');\n}\n/**\n * @returns {String} the string with leading zeroes contained in a <span>\n */\nfunction spanLeadingZeroes(address) {\n    const groups = address.split(':');\n    return groups.map((g) => spanLeadingZeroesSimple(g)).join(':');\n}\nexports.spanLeadingZeroes = spanLeadingZeroes;\n/**\n * Groups an address\n * @returns {String} a grouped address\n */\nfunction simpleGroup(addressString, offset = 0) {\n    const groups = addressString.split(':');\n    return groups.map((g, i) => {\n        if (/group-v4/.test(g)) {\n            return g;\n        }\n        return (0, sprintf_js_1.sprintf)('<span class=\"hover-group group-%d\">%s</span>', i + offset, spanLeadingZeroesSimple(g));\n    });\n}\nexports.simpleGroup = simpleGroup;\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvaXAtYWRkcmVzcy9kaXN0L3Y2L2hlbHBlcnMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsbUJBQW1CLEdBQUcseUJBQXlCLEdBQUcsZUFBZSxHQUFHLHFCQUFxQjtBQUN6RixxQkFBcUIsbUJBQU8sQ0FBQyxrRUFBWTtBQUN6QztBQUNBLGFBQWEsUUFBUTtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBLGFBQWEsUUFBUTtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYSxRQUFRO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUI7QUFDekI7QUFDQTtBQUNBLGFBQWEsUUFBUTtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EsbUJBQW1CO0FBQ25CIiwic291cmNlcyI6WyJDOlxcQ29kZVxcb25lLW1haWxcXG5vZGVfbW9kdWxlc1xcaXAtYWRkcmVzc1xcZGlzdFxcdjZcXGhlbHBlcnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLnNpbXBsZUdyb3VwID0gZXhwb3J0cy5zcGFuTGVhZGluZ1plcm9lcyA9IGV4cG9ydHMuc3BhbkFsbCA9IGV4cG9ydHMuc3BhbkFsbFplcm9lcyA9IHZvaWQgMDtcbmNvbnN0IHNwcmludGZfanNfMSA9IHJlcXVpcmUoXCJzcHJpbnRmLWpzXCIpO1xuLyoqXG4gKiBAcmV0dXJucyB7U3RyaW5nfSB0aGUgc3RyaW5nIHdpdGggYWxsIHplcm9lcyBjb250YWluZWQgaW4gYSA8c3Bhbj5cbiAqL1xuZnVuY3Rpb24gc3BhbkFsbFplcm9lcyhzKSB7XG4gICAgcmV0dXJuIHMucmVwbGFjZSgvKDArKS9nLCAnPHNwYW4gY2xhc3M9XCJ6ZXJvXCI+JDE8L3NwYW4+Jyk7XG59XG5leHBvcnRzLnNwYW5BbGxaZXJvZXMgPSBzcGFuQWxsWmVyb2VzO1xuLyoqXG4gKiBAcmV0dXJucyB7U3RyaW5nfSB0aGUgc3RyaW5nIHdpdGggZWFjaCBjaGFyYWN0ZXIgY29udGFpbmVkIGluIGEgPHNwYW4+XG4gKi9cbmZ1bmN0aW9uIHNwYW5BbGwocywgb2Zmc2V0ID0gMCkge1xuICAgIGNvbnN0IGxldHRlcnMgPSBzLnNwbGl0KCcnKTtcbiAgICByZXR1cm4gbGV0dGVyc1xuICAgICAgICAubWFwKChuLCBpKSA9PiAoMCwgc3ByaW50Zl9qc18xLnNwcmludGYpKCc8c3BhbiBjbGFzcz1cImRpZ2l0IHZhbHVlLSVzIHBvc2l0aW9uLSVkXCI+JXM8L3NwYW4+JywgbiwgaSArIG9mZnNldCwgc3BhbkFsbFplcm9lcyhuKSkgLy8gWFhYIFVzZSAjYmFzZS0yIC52YWx1ZS0wIGluc3RlYWQ/XG4gICAgKVxuICAgICAgICAuam9pbignJyk7XG59XG5leHBvcnRzLnNwYW5BbGwgPSBzcGFuQWxsO1xuZnVuY3Rpb24gc3BhbkxlYWRpbmdaZXJvZXNTaW1wbGUoZ3JvdXApIHtcbiAgICByZXR1cm4gZ3JvdXAucmVwbGFjZSgvXigwKykvLCAnPHNwYW4gY2xhc3M9XCJ6ZXJvXCI+JDE8L3NwYW4+Jyk7XG59XG4vKipcbiAqIEByZXR1cm5zIHtTdHJpbmd9IHRoZSBzdHJpbmcgd2l0aCBsZWFkaW5nIHplcm9lcyBjb250YWluZWQgaW4gYSA8c3Bhbj5cbiAqL1xuZnVuY3Rpb24gc3BhbkxlYWRpbmdaZXJvZXMoYWRkcmVzcykge1xuICAgIGNvbnN0IGdyb3VwcyA9IGFkZHJlc3Muc3BsaXQoJzonKTtcbiAgICByZXR1cm4gZ3JvdXBzLm1hcCgoZykgPT4gc3BhbkxlYWRpbmdaZXJvZXNTaW1wbGUoZykpLmpvaW4oJzonKTtcbn1cbmV4cG9ydHMuc3BhbkxlYWRpbmdaZXJvZXMgPSBzcGFuTGVhZGluZ1plcm9lcztcbi8qKlxuICogR3JvdXBzIGFuIGFkZHJlc3NcbiAqIEByZXR1cm5zIHtTdHJpbmd9IGEgZ3JvdXBlZCBhZGRyZXNzXG4gKi9cbmZ1bmN0aW9uIHNpbXBsZUdyb3VwKGFkZHJlc3NTdHJpbmcsIG9mZnNldCA9IDApIHtcbiAgICBjb25zdCBncm91cHMgPSBhZGRyZXNzU3RyaW5nLnNwbGl0KCc6Jyk7XG4gICAgcmV0dXJuIGdyb3Vwcy5tYXAoKGcsIGkpID0+IHtcbiAgICAgICAgaWYgKC9ncm91cC12NC8udGVzdChnKSkge1xuICAgICAgICAgICAgcmV0dXJuIGc7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuICgwLCBzcHJpbnRmX2pzXzEuc3ByaW50ZikoJzxzcGFuIGNsYXNzPVwiaG92ZXItZ3JvdXAgZ3JvdXAtJWRcIj4lczwvc3Bhbj4nLCBpICsgb2Zmc2V0LCBzcGFuTGVhZGluZ1plcm9lc1NpbXBsZShnKSk7XG4gICAgfSk7XG59XG5leHBvcnRzLnNpbXBsZUdyb3VwID0gc2ltcGxlR3JvdXA7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1oZWxwZXJzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/ip-address/dist/v6/helpers.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/ip-address/dist/v6/regular-expressions.js":
/*!****************************************************************!*\
  !*** ./node_modules/ip-address/dist/v6/regular-expressions.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.possibleElisions = exports.simpleRegularExpression = exports.ADDRESS_BOUNDARY = exports.padGroup = exports.groupPossibilities = void 0;\nconst v6 = __importStar(__webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/ip-address/dist/v6/constants.js\"));\nconst sprintf_js_1 = __webpack_require__(/*! sprintf-js */ \"(rsc)/./node_modules/sprintf-js/src/sprintf.js\");\nfunction groupPossibilities(possibilities) {\n    return (0, sprintf_js_1.sprintf)('(%s)', possibilities.join('|'));\n}\nexports.groupPossibilities = groupPossibilities;\nfunction padGroup(group) {\n    if (group.length < 4) {\n        return (0, sprintf_js_1.sprintf)('0{0,%d}%s', 4 - group.length, group);\n    }\n    return group;\n}\nexports.padGroup = padGroup;\nexports.ADDRESS_BOUNDARY = '[^A-Fa-f0-9:]';\nfunction simpleRegularExpression(groups) {\n    const zeroIndexes = [];\n    groups.forEach((group, i) => {\n        const groupInteger = parseInt(group, 16);\n        if (groupInteger === 0) {\n            zeroIndexes.push(i);\n        }\n    });\n    // You can technically elide a single 0, this creates the regular expressions\n    // to match that eventuality\n    const possibilities = zeroIndexes.map((zeroIndex) => groups\n        .map((group, i) => {\n        if (i === zeroIndex) {\n            const elision = i === 0 || i === v6.GROUPS - 1 ? ':' : '';\n            return groupPossibilities([padGroup(group), elision]);\n        }\n        return padGroup(group);\n    })\n        .join(':'));\n    // The simplest case\n    possibilities.push(groups.map(padGroup).join(':'));\n    return groupPossibilities(possibilities);\n}\nexports.simpleRegularExpression = simpleRegularExpression;\nfunction possibleElisions(elidedGroups, moreLeft, moreRight) {\n    const left = moreLeft ? '' : ':';\n    const right = moreRight ? '' : ':';\n    const possibilities = [];\n    // 1. elision of everything (::)\n    if (!moreLeft && !moreRight) {\n        possibilities.push('::');\n    }\n    // 2. complete elision of the middle\n    if (moreLeft && moreRight) {\n        possibilities.push('');\n    }\n    if ((moreRight && !moreLeft) || (!moreRight && moreLeft)) {\n        // 3. complete elision of one side\n        possibilities.push(':');\n    }\n    // 4. elision from the left side\n    possibilities.push((0, sprintf_js_1.sprintf)('%s(:0{1,4}){1,%d}', left, elidedGroups - 1));\n    // 5. elision from the right side\n    possibilities.push((0, sprintf_js_1.sprintf)('(0{1,4}:){1,%d}%s', elidedGroups - 1, right));\n    // 6. no elision\n    possibilities.push((0, sprintf_js_1.sprintf)('(0{1,4}:){%d}0{1,4}', elidedGroups - 1));\n    // 7. elision (including sloppy elision) from the middle\n    for (let groups = 1; groups < elidedGroups - 1; groups++) {\n        for (let position = 1; position < elidedGroups - groups; position++) {\n            possibilities.push((0, sprintf_js_1.sprintf)('(0{1,4}:){%d}:(0{1,4}:){%d}0{1,4}', position, elidedGroups - position - groups - 1));\n        }\n    }\n    return groupPossibilities(possibilities);\n}\nexports.possibleElisions = possibleElisions;\n//# sourceMappingURL=regular-expressions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/ip-address/dist/v6/regular-expressions.js\n");

/***/ })

};
;