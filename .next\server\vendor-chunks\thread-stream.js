"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/thread-stream";
exports.ids = ["vendor-chunks/thread-stream"];
exports.modules = {

/***/ "(rsc)/./node_modules/thread-stream/index.js":
/*!*********************************************!*\
  !*** ./node_modules/thread-stream/index.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst { version } = __webpack_require__(/*! ./package.json */ \"(rsc)/./node_modules/thread-stream/package.json\")\nconst { EventEmitter } = __webpack_require__(/*! events */ \"events\")\nconst { Worker } = __webpack_require__(/*! worker_threads */ \"worker_threads\")\nconst { join } = __webpack_require__(/*! path */ \"path\")\nconst { pathToFileURL } = __webpack_require__(/*! url */ \"url\")\nconst { wait } = __webpack_require__(/*! ./lib/wait */ \"(rsc)/./node_modules/thread-stream/lib/wait.js\")\nconst {\n  WRITE_INDEX,\n  READ_INDEX\n} = __webpack_require__(/*! ./lib/indexes */ \"(rsc)/./node_modules/thread-stream/lib/indexes.js\")\nconst buffer = __webpack_require__(/*! buffer */ \"buffer\")\nconst assert = __webpack_require__(/*! assert */ \"assert\")\n\nconst kImpl = Symbol('kImpl')\n\n// V8 limit for string size\nconst MAX_STRING = buffer.constants.MAX_STRING_LENGTH\n\nclass FakeWeakRef {\n  constructor (value) {\n    this._value = value\n  }\n\n  deref () {\n    return this._value\n  }\n}\n\nclass FakeFinalizationRegistry {\n  register () {}\n\n  unregister () {}\n}\n\n// Currently using FinalizationRegistry with code coverage breaks the world\n// Ref: https://github.com/nodejs/node/issues/49344\nconst FinalizationRegistry = process.env.NODE_V8_COVERAGE ? FakeFinalizationRegistry : global.FinalizationRegistry || FakeFinalizationRegistry\nconst WeakRef = process.env.NODE_V8_COVERAGE ? FakeWeakRef : global.WeakRef || FakeWeakRef\n\nconst registry = new FinalizationRegistry((worker) => {\n  if (worker.exited) {\n    return\n  }\n  worker.terminate()\n})\n\nfunction createWorker (stream, opts) {\n  const { filename, workerData } = opts\n\n  const bundlerOverrides = '__bundlerPathsOverrides' in globalThis ? globalThis.__bundlerPathsOverrides : {}\n  const toExecute = bundlerOverrides['thread-stream-worker'] || join(__dirname, 'lib', 'worker.js')\n\n  const worker = new Worker(toExecute, {\n    ...opts.workerOpts,\n    trackUnmanagedFds: false,\n    workerData: {\n      filename: filename.indexOf('file://') === 0\n        ? filename\n        : pathToFileURL(filename).href,\n      dataBuf: stream[kImpl].dataBuf,\n      stateBuf: stream[kImpl].stateBuf,\n      workerData: {\n        $context: {\n          threadStreamVersion: version\n        },\n        ...workerData\n      }\n    }\n  })\n\n  // We keep a strong reference for now,\n  // we need to start writing first\n  worker.stream = new FakeWeakRef(stream)\n\n  worker.on('message', onWorkerMessage)\n  worker.on('exit', onWorkerExit)\n  registry.register(stream, worker)\n\n  return worker\n}\n\nfunction drain (stream) {\n  assert(!stream[kImpl].sync)\n  if (stream[kImpl].needDrain) {\n    stream[kImpl].needDrain = false\n    stream.emit('drain')\n  }\n}\n\nfunction nextFlush (stream) {\n  const writeIndex = Atomics.load(stream[kImpl].state, WRITE_INDEX)\n  let leftover = stream[kImpl].data.length - writeIndex\n\n  if (leftover > 0) {\n    if (stream[kImpl].buf.length === 0) {\n      stream[kImpl].flushing = false\n\n      if (stream[kImpl].ending) {\n        end(stream)\n      } else if (stream[kImpl].needDrain) {\n        process.nextTick(drain, stream)\n      }\n\n      return\n    }\n\n    let toWrite = stream[kImpl].buf.slice(0, leftover)\n    let toWriteBytes = Buffer.byteLength(toWrite)\n    if (toWriteBytes <= leftover) {\n      stream[kImpl].buf = stream[kImpl].buf.slice(leftover)\n      // process._rawDebug('writing ' + toWrite.length)\n      write(stream, toWrite, nextFlush.bind(null, stream))\n    } else {\n      // multi-byte utf-8\n      stream.flush(() => {\n        // err is already handled in flush()\n        if (stream.destroyed) {\n          return\n        }\n\n        Atomics.store(stream[kImpl].state, READ_INDEX, 0)\n        Atomics.store(stream[kImpl].state, WRITE_INDEX, 0)\n\n        // Find a toWrite length that fits the buffer\n        // it must exists as the buffer is at least 4 bytes length\n        // and the max utf-8 length for a char is 4 bytes.\n        while (toWriteBytes > stream[kImpl].data.length) {\n          leftover = leftover / 2\n          toWrite = stream[kImpl].buf.slice(0, leftover)\n          toWriteBytes = Buffer.byteLength(toWrite)\n        }\n        stream[kImpl].buf = stream[kImpl].buf.slice(leftover)\n        write(stream, toWrite, nextFlush.bind(null, stream))\n      })\n    }\n  } else if (leftover === 0) {\n    if (writeIndex === 0 && stream[kImpl].buf.length === 0) {\n      // we had a flushSync in the meanwhile\n      return\n    }\n    stream.flush(() => {\n      Atomics.store(stream[kImpl].state, READ_INDEX, 0)\n      Atomics.store(stream[kImpl].state, WRITE_INDEX, 0)\n      nextFlush(stream)\n    })\n  } else {\n    // This should never happen\n    destroy(stream, new Error('overwritten'))\n  }\n}\n\nfunction onWorkerMessage (msg) {\n  const stream = this.stream.deref()\n  if (stream === undefined) {\n    this.exited = true\n    // Terminate the worker.\n    this.terminate()\n    return\n  }\n\n  switch (msg.code) {\n    case 'READY':\n      // Replace the FakeWeakRef with a\n      // proper one.\n      this.stream = new WeakRef(stream)\n\n      stream.flush(() => {\n        stream[kImpl].ready = true\n        stream.emit('ready')\n      })\n      break\n    case 'ERROR':\n      destroy(stream, msg.err)\n      break\n    case 'EVENT':\n      if (Array.isArray(msg.args)) {\n        stream.emit(msg.name, ...msg.args)\n      } else {\n        stream.emit(msg.name, msg.args)\n      }\n      break\n    case 'WARNING':\n      process.emitWarning(msg.err)\n      break\n    default:\n      destroy(stream, new Error('this should not happen: ' + msg.code))\n  }\n}\n\nfunction onWorkerExit (code) {\n  const stream = this.stream.deref()\n  if (stream === undefined) {\n    // Nothing to do, the worker already exit\n    return\n  }\n  registry.unregister(stream)\n  stream.worker.exited = true\n  stream.worker.off('exit', onWorkerExit)\n  destroy(stream, code !== 0 ? new Error('the worker thread exited') : null)\n}\n\nclass ThreadStream extends EventEmitter {\n  constructor (opts = {}) {\n    super()\n\n    if (opts.bufferSize < 4) {\n      throw new Error('bufferSize must at least fit a 4-byte utf-8 char')\n    }\n\n    this[kImpl] = {}\n    this[kImpl].stateBuf = new SharedArrayBuffer(128)\n    this[kImpl].state = new Int32Array(this[kImpl].stateBuf)\n    this[kImpl].dataBuf = new SharedArrayBuffer(opts.bufferSize || 4 * 1024 * 1024)\n    this[kImpl].data = Buffer.from(this[kImpl].dataBuf)\n    this[kImpl].sync = opts.sync || false\n    this[kImpl].ending = false\n    this[kImpl].ended = false\n    this[kImpl].needDrain = false\n    this[kImpl].destroyed = false\n    this[kImpl].flushing = false\n    this[kImpl].ready = false\n    this[kImpl].finished = false\n    this[kImpl].errored = null\n    this[kImpl].closed = false\n    this[kImpl].buf = ''\n\n    // TODO (fix): Make private?\n    this.worker = createWorker(this, opts) // TODO (fix): make private\n    this.on('message', (message, transferList) => {\n      this.worker.postMessage(message, transferList)\n    })\n  }\n\n  write (data) {\n    if (this[kImpl].destroyed) {\n      error(this, new Error('the worker has exited'))\n      return false\n    }\n\n    if (this[kImpl].ending) {\n      error(this, new Error('the worker is ending'))\n      return false\n    }\n\n    if (this[kImpl].flushing && this[kImpl].buf.length + data.length >= MAX_STRING) {\n      try {\n        writeSync(this)\n        this[kImpl].flushing = true\n      } catch (err) {\n        destroy(this, err)\n        return false\n      }\n    }\n\n    this[kImpl].buf += data\n\n    if (this[kImpl].sync) {\n      try {\n        writeSync(this)\n        return true\n      } catch (err) {\n        destroy(this, err)\n        return false\n      }\n    }\n\n    if (!this[kImpl].flushing) {\n      this[kImpl].flushing = true\n      setImmediate(nextFlush, this)\n    }\n\n    this[kImpl].needDrain = this[kImpl].data.length - this[kImpl].buf.length - Atomics.load(this[kImpl].state, WRITE_INDEX) <= 0\n    return !this[kImpl].needDrain\n  }\n\n  end () {\n    if (this[kImpl].destroyed) {\n      return\n    }\n\n    this[kImpl].ending = true\n    end(this)\n  }\n\n  flush (cb) {\n    if (this[kImpl].destroyed) {\n      if (typeof cb === 'function') {\n        process.nextTick(cb, new Error('the worker has exited'))\n      }\n      return\n    }\n\n    // TODO write all .buf\n    const writeIndex = Atomics.load(this[kImpl].state, WRITE_INDEX)\n    // process._rawDebug(`(flush) readIndex (${Atomics.load(this.state, READ_INDEX)}) writeIndex (${Atomics.load(this.state, WRITE_INDEX)})`)\n    wait(this[kImpl].state, READ_INDEX, writeIndex, Infinity, (err, res) => {\n      if (err) {\n        destroy(this, err)\n        process.nextTick(cb, err)\n        return\n      }\n      if (res === 'not-equal') {\n        // TODO handle deadlock\n        this.flush(cb)\n        return\n      }\n      process.nextTick(cb)\n    })\n  }\n\n  flushSync () {\n    if (this[kImpl].destroyed) {\n      return\n    }\n\n    writeSync(this)\n    flushSync(this)\n  }\n\n  unref () {\n    this.worker.unref()\n  }\n\n  ref () {\n    this.worker.ref()\n  }\n\n  get ready () {\n    return this[kImpl].ready\n  }\n\n  get destroyed () {\n    return this[kImpl].destroyed\n  }\n\n  get closed () {\n    return this[kImpl].closed\n  }\n\n  get writable () {\n    return !this[kImpl].destroyed && !this[kImpl].ending\n  }\n\n  get writableEnded () {\n    return this[kImpl].ending\n  }\n\n  get writableFinished () {\n    return this[kImpl].finished\n  }\n\n  get writableNeedDrain () {\n    return this[kImpl].needDrain\n  }\n\n  get writableObjectMode () {\n    return false\n  }\n\n  get writableErrored () {\n    return this[kImpl].errored\n  }\n}\n\nfunction error (stream, err) {\n  setImmediate(() => {\n    stream.emit('error', err)\n  })\n}\n\nfunction destroy (stream, err) {\n  if (stream[kImpl].destroyed) {\n    return\n  }\n  stream[kImpl].destroyed = true\n\n  if (err) {\n    stream[kImpl].errored = err\n    error(stream, err)\n  }\n\n  if (!stream.worker.exited) {\n    stream.worker.terminate()\n      .catch(() => {})\n      .then(() => {\n        stream[kImpl].closed = true\n        stream.emit('close')\n      })\n  } else {\n    setImmediate(() => {\n      stream[kImpl].closed = true\n      stream.emit('close')\n    })\n  }\n}\n\nfunction write (stream, data, cb) {\n  // data is smaller than the shared buffer length\n  const current = Atomics.load(stream[kImpl].state, WRITE_INDEX)\n  const length = Buffer.byteLength(data)\n  stream[kImpl].data.write(data, current)\n  Atomics.store(stream[kImpl].state, WRITE_INDEX, current + length)\n  Atomics.notify(stream[kImpl].state, WRITE_INDEX)\n  cb()\n  return true\n}\n\nfunction end (stream) {\n  if (stream[kImpl].ended || !stream[kImpl].ending || stream[kImpl].flushing) {\n    return\n  }\n  stream[kImpl].ended = true\n\n  try {\n    stream.flushSync()\n\n    let readIndex = Atomics.load(stream[kImpl].state, READ_INDEX)\n\n    // process._rawDebug('writing index')\n    Atomics.store(stream[kImpl].state, WRITE_INDEX, -1)\n    // process._rawDebug(`(end) readIndex (${Atomics.load(stream.state, READ_INDEX)}) writeIndex (${Atomics.load(stream.state, WRITE_INDEX)})`)\n    Atomics.notify(stream[kImpl].state, WRITE_INDEX)\n\n    // Wait for the process to complete\n    let spins = 0\n    while (readIndex !== -1) {\n      // process._rawDebug(`read = ${read}`)\n      Atomics.wait(stream[kImpl].state, READ_INDEX, readIndex, 1000)\n      readIndex = Atomics.load(stream[kImpl].state, READ_INDEX)\n\n      if (readIndex === -2) {\n        destroy(stream, new Error('end() failed'))\n        return\n      }\n\n      if (++spins === 10) {\n        destroy(stream, new Error('end() took too long (10s)'))\n        return\n      }\n    }\n\n    process.nextTick(() => {\n      stream[kImpl].finished = true\n      stream.emit('finish')\n    })\n  } catch (err) {\n    destroy(stream, err)\n  }\n  // process._rawDebug('end finished...')\n}\n\nfunction writeSync (stream) {\n  const cb = () => {\n    if (stream[kImpl].ending) {\n      end(stream)\n    } else if (stream[kImpl].needDrain) {\n      process.nextTick(drain, stream)\n    }\n  }\n  stream[kImpl].flushing = false\n\n  while (stream[kImpl].buf.length !== 0) {\n    const writeIndex = Atomics.load(stream[kImpl].state, WRITE_INDEX)\n    let leftover = stream[kImpl].data.length - writeIndex\n    if (leftover === 0) {\n      flushSync(stream)\n      Atomics.store(stream[kImpl].state, READ_INDEX, 0)\n      Atomics.store(stream[kImpl].state, WRITE_INDEX, 0)\n      continue\n    } else if (leftover < 0) {\n      // stream should never happen\n      throw new Error('overwritten')\n    }\n\n    let toWrite = stream[kImpl].buf.slice(0, leftover)\n    let toWriteBytes = Buffer.byteLength(toWrite)\n    if (toWriteBytes <= leftover) {\n      stream[kImpl].buf = stream[kImpl].buf.slice(leftover)\n      // process._rawDebug('writing ' + toWrite.length)\n      write(stream, toWrite, cb)\n    } else {\n      // multi-byte utf-8\n      flushSync(stream)\n      Atomics.store(stream[kImpl].state, READ_INDEX, 0)\n      Atomics.store(stream[kImpl].state, WRITE_INDEX, 0)\n\n      // Find a toWrite length that fits the buffer\n      // it must exists as the buffer is at least 4 bytes length\n      // and the max utf-8 length for a char is 4 bytes.\n      while (toWriteBytes > stream[kImpl].buf.length) {\n        leftover = leftover / 2\n        toWrite = stream[kImpl].buf.slice(0, leftover)\n        toWriteBytes = Buffer.byteLength(toWrite)\n      }\n      stream[kImpl].buf = stream[kImpl].buf.slice(leftover)\n      write(stream, toWrite, cb)\n    }\n  }\n}\n\nfunction flushSync (stream) {\n  if (stream[kImpl].flushing) {\n    throw new Error('unable to flush while flushing')\n  }\n\n  // process._rawDebug('flushSync started')\n\n  const writeIndex = Atomics.load(stream[kImpl].state, WRITE_INDEX)\n\n  let spins = 0\n\n  // TODO handle deadlock\n  while (true) {\n    const readIndex = Atomics.load(stream[kImpl].state, READ_INDEX)\n\n    if (readIndex === -2) {\n      throw Error('_flushSync failed')\n    }\n\n    // process._rawDebug(`(flushSync) readIndex (${readIndex}) writeIndex (${writeIndex})`)\n    if (readIndex !== writeIndex) {\n      // TODO stream timeouts for some reason.\n      Atomics.wait(stream[kImpl].state, READ_INDEX, readIndex, 1000)\n    } else {\n      break\n    }\n\n    if (++spins === 10) {\n      throw new Error('_flushSync took too long (10s)')\n    }\n  }\n  // process._rawDebug('flushSync finished')\n}\n\nmodule.exports = ThreadStream\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/thread-stream/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/thread-stream/lib/indexes.js":
/*!***************************************************!*\
  !*** ./node_modules/thread-stream/lib/indexes.js ***!
  \***************************************************/
/***/ ((module) => {

eval("\n\nconst WRITE_INDEX = 4\nconst READ_INDEX = 8\n\nmodule.exports = {\n  WRITE_INDEX,\n  READ_INDEX\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvdGhyZWFkLXN0cmVhbS9saWIvaW5kZXhlcy5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcQ29kZVxcb25lLW1haWxcXG5vZGVfbW9kdWxlc1xcdGhyZWFkLXN0cmVhbVxcbGliXFxpbmRleGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5jb25zdCBXUklURV9JTkRFWCA9IDRcbmNvbnN0IFJFQURfSU5ERVggPSA4XG5cbm1vZHVsZS5leHBvcnRzID0ge1xuICBXUklURV9JTkRFWCxcbiAgUkVBRF9JTkRFWFxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/thread-stream/lib/indexes.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/thread-stream/lib/wait.js":
/*!************************************************!*\
  !*** ./node_modules/thread-stream/lib/wait.js ***!
  \************************************************/
/***/ ((module) => {

eval("\n\nconst MAX_TIMEOUT = 1000\n\nfunction wait (state, index, expected, timeout, done) {\n  const max = Date.now() + timeout\n  let current = Atomics.load(state, index)\n  if (current === expected) {\n    done(null, 'ok')\n    return\n  }\n  let prior = current\n  const check = (backoff) => {\n    if (Date.now() > max) {\n      done(null, 'timed-out')\n    } else {\n      setTimeout(() => {\n        prior = current\n        current = Atomics.load(state, index)\n        if (current === prior) {\n          check(backoff >= MAX_TIMEOUT ? MAX_TIMEOUT : backoff * 2)\n        } else {\n          if (current === expected) done(null, 'ok')\n          else done(null, 'not-equal')\n        }\n      }, backoff)\n    }\n  }\n  check(1)\n}\n\n// let waitDiffCount = 0\nfunction waitDiff (state, index, expected, timeout, done) {\n  // const id = waitDiffCount++\n  // process._rawDebug(`>>> waitDiff ${id}`)\n  const max = Date.now() + timeout\n  let current = Atomics.load(state, index)\n  if (current !== expected) {\n    done(null, 'ok')\n    return\n  }\n  const check = (backoff) => {\n    // process._rawDebug(`${id} ${index} current ${current} expected ${expected}`)\n    // process._rawDebug('' + backoff)\n    if (Date.now() > max) {\n      done(null, 'timed-out')\n    } else {\n      setTimeout(() => {\n        current = Atomics.load(state, index)\n        if (current !== expected) {\n          done(null, 'ok')\n        } else {\n          check(backoff >= MAX_TIMEOUT ? MAX_TIMEOUT : backoff * 2)\n        }\n      }, backoff)\n    }\n  }\n  check(1)\n}\n\nmodule.exports = { wait, waitDiff }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/thread-stream/lib/wait.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/thread-stream/package.json":
/*!*************************************************!*\
  !*** ./node_modules/thread-stream/package.json ***!
  \*************************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"name":"thread-stream","version":"3.1.0","description":"A streaming way to send data to a Node.js Worker Thread","main":"index.js","types":"index.d.ts","dependencies":{"real-require":"^0.2.0"},"devDependencies":{"@types/node":"^20.1.0","@types/tap":"^15.0.0","@yao-pkg/pkg":"^5.11.5","desm":"^1.3.0","fastbench":"^1.0.1","husky":"^9.0.6","pino-elasticsearch":"^8.0.0","sonic-boom":"^4.0.1","standard":"^17.0.0","tap":"^16.2.0","ts-node":"^10.8.0","typescript":"^5.3.2","why-is-node-running":"^2.2.2"},"scripts":{"build":"tsc --noEmit","test":"standard && npm run build && npm run transpile && tap \\"test/**/*.test.*js\\" && tap --ts test/*.test.*ts","test:ci":"standard && npm run transpile && npm run test:ci:js && npm run test:ci:ts","test:ci:js":"tap --no-check-coverage --timeout=120 --coverage-report=lcovonly \\"test/**/*.test.*js\\"","test:ci:ts":"tap --ts --no-check-coverage --coverage-report=lcovonly \\"test/**/*.test.*ts\\"","test:yarn":"npm run transpile && tap \\"test/**/*.test.js\\" --no-check-coverage","transpile":"sh ./test/ts/transpile.sh","prepare":"husky install"},"standard":{"ignore":["test/ts/**/*","test/syntax-error.mjs"]},"repository":{"type":"git","url":"git+https://github.com/mcollina/thread-stream.git"},"keywords":["worker","thread","threads","stream"],"author":"Matteo Collina <<EMAIL>>","license":"MIT","bugs":{"url":"https://github.com/mcollina/thread-stream/issues"},"homepage":"https://github.com/mcollina/thread-stream#readme"}');

/***/ })

};
;